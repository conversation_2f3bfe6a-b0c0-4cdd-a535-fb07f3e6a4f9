#!/usr/bin/env python3
"""
医保项目检索API测试脚本
"""
import requests
import json
import time

# 测试配置
BASE_URL = "http://127.0.0.1:5000"
TEST_CASES = [
    {
        "name": "PostgreSQL默认连接搜索",
        "data": {
            "search_term": "检查",
            "database": "pg",
            "host": "default",
            "schema": "",
            "limit": 10
        }
    },
    {
        "name": "Oracle默认连接搜索",
        "data": {
            "search_term": "治疗",
            "database": "oracle",
            "host": "default",
            "schema": "",
            "limit": 10
        }
    },
    {
        "name": "空搜索词测试",
        "data": {
            "search_term": "",
            "database": "pg",
            "host": "default",
            "schema": "",
            "limit": 10
        }
    },
    {
        "name": "特殊字符搜索测试",
        "data": {
            "search_term": "CT",
            "database": "pg",
            "host": "default",
            "schema": "",
            "limit": 5
        }
    }
]

def test_medical_insurance_search():
    """测试医保项目搜索API"""
    print("=" * 60)
    print("医保项目检索API测试")
    print("=" * 60)
    
    for i, test_case in enumerate(TEST_CASES, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        try:
            # 发送请求
            response = requests.post(
                f"{BASE_URL}/api/medical-insurance/search",
                headers={"Content-Type": "application/json"},
                json=test_case["data"],
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"成功: {result.get('success', False)}")
                
                if result.get('success'):
                    data = result.get('data', [])
                    total = result.get('total', 0)
                    database = result.get('database', 'Unknown')
                    host = result.get('host', 'Unknown')
                    schema = result.get('schema', '')
                    
                    print(f"数据库: {database}")
                    print(f"主机: {host}")
                    print(f"Schema: {schema or '默认'}")
                    print(f"找到项目数: {total}")
                    
                    if data:
                        print("\n前3个项目:")
                        for j, item in enumerate(data[:3], 1):
                            print(f"  {j}. 编码: {item.get('医保项目编码', 'N/A')}")
                            print(f"     名称: {item.get('医保项目名称', 'N/A')}")
                            print(f"     医院编码: {item.get('医院项目编码', 'N/A')}")
                            print(f"     医院名称: {item.get('医院项目名称', 'N/A')}")
                            print(f"     费用类别: {item.get('费用类别', 'N/A')}")
                            print()
                    else:
                        print("未找到匹配的项目")
                else:
                    print(f"错误: {result.get('error', '未知错误')}")
            else:
                print(f"HTTP错误: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"错误信息: {error_data}")
                except:
                    print(f"响应内容: {response.text}")
                    
        except requests.exceptions.ConnectionError:
            print("连接错误: 无法连接到服务器，请确保Flask应用正在运行")
        except requests.exceptions.Timeout:
            print("超时错误: 请求超时")
        except Exception as e:
            print(f"其他错误: {str(e)}")
        
        # 测试间隔
        if i < len(TEST_CASES):
            print("等待2秒...")
            time.sleep(2)

def test_database_connection():
    """测试数据库连接"""
    print("\n" + "=" * 60)
    print("数据库连接测试")
    print("=" * 60)
    
    databases = ["pg", "oracle"]
    
    for db in databases:
        print(f"\n测试 {db.upper()} 数据库连接:")
        print("-" * 30)
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/database_schemas",
                headers={"Content-Type": "application/json"},
                json={"database": db, "host": "default"},
                timeout=10
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    schemas = result.get('schemas', [])
                    print(f"连接成功，找到 {len(schemas)} 个Schema")
                    if schemas:
                        print(f"前5个Schema: {schemas[:5]}")
                else:
                    print(f"连接失败: {result.get('error', '未知错误')}")
            else:
                print(f"HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"连接测试失败: {str(e)}")

def main():
    """主函数"""
    print("医保项目检索功能测试")
    print("请确保Flask应用正在运行 (python app.py)")
    print("按Enter键开始测试...")
    input()
    
    # 测试数据库连接
    test_database_connection()
    
    # 测试医保项目搜索
    test_medical_insurance_search()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main() 