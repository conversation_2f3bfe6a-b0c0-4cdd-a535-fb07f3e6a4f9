"""
Data Access Object (DAO) layer for file system operations.
"""
import os
import json
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
from models.rule import RuleInfo, Rule, RuleData
from utils.config import config
from utils.error_handler import SystemError, BusinessError


class FileSystemDAO:
    """Data Access Object for file system operations."""
    
    def __init__(self):
        self.output_dir = config.OUTPUT_DIR
        self.metadata_dir = os.path.join(self.output_dir, '.metadata')
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist."""
        directories = [self.output_dir, self.metadata_dir]
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def _get_rule_file_path(self, rule_name: str) -> str:
        """Get the file path for a rule."""
        return os.path.join(self.output_dir, f"{rule_name}.sql")
    
    def _get_metadata_file_path(self, rule_name: str) -> str:
        """Get the metadata file path for a rule."""
        return os.path.join(self.metadata_dir, f"{rule_name}.json")
    
    def _save_metadata(self, rule_name: str, metadata: Dict[str, Any]) -> None:
        """Save rule metadata to JSON file."""
        try:
            metadata_path = self._get_metadata_file_path(rule_name)
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise SystemError(f"Failed to save metadata for rule '{rule_name}': {str(e)}")
    
    def _load_metadata(self, rule_name: str) -> Dict[str, Any]:
        """Load rule metadata from JSON file."""
        try:
            metadata_path = self._get_metadata_file_path(rule_name)
            if not os.path.exists(metadata_path):
                return {}
            
            with open(metadata_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            # Return empty dict if metadata can't be loaded
            return {}
    
    def list_rule_files(self) -> List[RuleInfo]:
        """List all rule files in the output directory."""
        try:
            rules = []
            if not os.path.exists(self.output_dir):
                return rules
            
            for filename in os.listdir(self.output_dir):
                if filename.endswith(".sql"):
                    rule_name = os.path.splitext(filename)[0]
                    file_path = self._get_rule_file_path(rule_name)
                    
                    try:
                        stat = os.stat(file_path)
                        metadata = self._load_metadata(rule_name)
                        
                        rules.append(RuleInfo(
                            name=rule_name,
                            created_at=stat.st_ctime,
                            file_size=stat.st_size,
                            status=metadata.get('status', 'active'),
                            description=metadata.get('description'),
                            category=metadata.get('category'),
                            rule_type=metadata.get('rule_type'),
                            policy_basis=metadata.get('policy_basis')
                        ))
                    except OSError as e:
                        # Log the error but continue processing other files
                        print(f"Error accessing file {file_path}: {e}")
            
            return rules
            
        except Exception as e:
            raise SystemError(f"Failed to list rule files: {str(e)}")
    
    def read_rule_file(self, rule_name: str) -> Rule:
        """Read a rule file and return Rule object."""
        if not rule_name or not rule_name.strip():
            raise BusinessError("Rule name is required")
        
        file_path = self._get_rule_file_path(rule_name)
        
        if not os.path.exists(file_path):
            raise BusinessError(
                f"Rule '{rule_name}' not found",
                error_code="RULE_NOT_FOUND",
                details={"rule_name": rule_name}
            )
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            stat = os.stat(file_path)
            metadata = self._load_metadata(rule_name)
            
            return Rule(
                name=rule_name,
                content=content,
                id=metadata.get('id'),  # 新增：读取UUID
                created_at=stat.st_ctime,
                file_size=stat.st_size,
                status=metadata.get('status', 'active'),
                description=metadata.get('description'),
                category=metadata.get('category'),
                rule_type=metadata.get('rule_type'),
                policy_basis=metadata.get('policy_basis'),
                template_id=metadata.get('template_id'),
                parameters=metadata.get('parameters'),
                database_type=metadata.get('database_type'),
                created_by=metadata.get('created_by'),
                updated_at=metadata.get('updated_at'),
                # New attributes for intelligent template selection
                patient_type=metadata.get('patient_type'),
                match_method=metadata.get('match_method'),
                applicable_scope=metadata.get('applicable_scope')
            )
            
        except Exception as e:
            raise SystemError(f"Failed to read rule '{rule_name}': {str(e)}")
    
    def write_rule_file(self, rule_data: RuleData) -> bool:
        """Write rule data to file and return True if it's a new file."""
        if not rule_data.validate():
            raise BusinessError(
                "Invalid rule data",
                error_code="INVALID_RULE_DATA",
                details={
                    "name": rule_data.name,
                    "has_content": bool(rule_data.content and rule_data.content.strip())
                }
            )
        
        file_path = self._get_rule_file_path(rule_data.name)
        is_new_file = not os.path.exists(file_path)
        
        try:
            # Write the SQL content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(rule_data.content)
            
            # Save metadata
            metadata = {
                'id': rule_data.id,  # 新增：保存UUID
                'description': rule_data.description,
                'category': rule_data.category,
                'rule_type': rule_data.rule_type,
                'policy_basis': rule_data.policy_basis,
                'template_id': rule_data.template_id,
                'parameters': rule_data.parameters,
                'database_type': rule_data.database_type,
                'created_by': rule_data.created_by,
                'status': 'active',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                # New attributes for intelligent template selection
                'patient_type': rule_data.patient_type,
                'match_method': rule_data.match_method,
                'applicable_scope': rule_data.applicable_scope
            }
            
            # 如果是新建规则且没有ID，自动生成UUID
            if is_new_file and not rule_data.id:
                metadata['id'] = str(uuid.uuid4())
            # 如果是更新现有规则且没有ID，也生成UUID（保持一致性）
            elif not is_new_file and not rule_data.id:
                metadata['id'] = str(uuid.uuid4())
            
            # If updating existing rule, preserve original creation time
            if not is_new_file:
                existing_metadata = self._load_metadata(rule_data.name)
                if 'created_at' in existing_metadata:
                    metadata['created_at'] = existing_metadata['created_at']
            
            self._save_metadata(rule_data.name, metadata)
            
            return is_new_file
            
        except Exception as e:
            raise SystemError(f"Failed to write rule '{rule_data.name}': {str(e)}")
    
    def delete_rule_file(self, rule_name: str) -> bool:
        """Delete a rule file and its metadata."""
        if not rule_name or not rule_name.strip():
            raise BusinessError("Rule name is required")
        
        file_path = self._get_rule_file_path(rule_name)
        metadata_path = self._get_metadata_file_path(rule_name)
        
        if not os.path.exists(file_path):
            raise BusinessError(
                f"Rule '{rule_name}' not found",
                error_code="RULE_NOT_FOUND",
                details={"rule_name": rule_name}
            )
        
        try:
            # Delete the SQL file
            os.remove(file_path)
            
            # Delete metadata file if it exists
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            return True
            
        except Exception as e:
            raise SystemError(f"Failed to delete rule '{rule_name}': {str(e)}")
    
    def rule_file_exists(self, rule_name: str) -> bool:
        """Check if a rule file exists."""
        if not rule_name:
            return False
        
        file_path = self._get_rule_file_path(rule_name)
        return os.path.exists(file_path)
    
    def get_rule_metadata(self, rule_name: str) -> Dict[str, Any]:
        """Get metadata for a specific rule."""
        return self._load_metadata(rule_name)
    
    def update_rule_metadata(self, rule_name: str, metadata: Dict[str, Any]) -> None:
        """Update metadata for a specific rule."""
        if not self.rule_file_exists(rule_name):
            raise BusinessError(
                f"Rule '{rule_name}' not found",
                error_code="RULE_NOT_FOUND",
                details={"rule_name": rule_name}
            )
        
        existing_metadata = self._load_metadata(rule_name)
        existing_metadata.update(metadata)
        existing_metadata['updated_at'] = datetime.now().isoformat()
        
        self._save_metadata(rule_name, existing_metadata)