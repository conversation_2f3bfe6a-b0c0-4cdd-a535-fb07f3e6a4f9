"""
Configuration management module for the rule management system.
"""
import os
import json
from typing import Dict, Any, List
import uuid
from datetime import datetime


class Config:
    """Application configuration class."""
    
    def __init__(self):
        self.BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.OUTPUT_DIR = os.path.join(self.BASE_DIR, 'output')
        self.TEMPLATES_DIR = os.path.join(self.BASE_DIR, 'templates')
        self.TEMPLATES_RULE_DIR = os.path.join(self.TEMPLATES_DIR, 'rule')
        self.CONFIG_DIR = os.path.join(self.BASE_DIR, 'config')
        self.DB_CONFIG_FILE = os.path.join(self.CONFIG_DIR, 'database_config.json')
        self.DB_CONNECTIONS_FILE = os.path.join(self.CONFIG_DIR, 'database_connections.json')
        
        # Flask configuration
        self.FLASK_CONFIG = {
            'TEMPLATES_AUTO_RELOAD': True,
            'SEND_FILE_MAX_AGE_DEFAULT': 0,
            'DEBUG': os.getenv('DEBUG', 'True').lower() == 'true'
        }
        
        # Ensure required directories exist
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            self.OUTPUT_DIR,
            self.TEMPLATES_DIR,
            self.TEMPLATES_RULE_DIR,
            self.CONFIG_DIR
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def get_flask_config(self) -> Dict[str, Any]:
        """Get Flask configuration dictionary."""
        return self.FLASK_CONFIG.copy()
    
    def save_database_config(self, config_data: Dict[str, Any]) -> bool:
        """保存数据库配置到文件"""
        try:
            # 确保配置目录存在
            self._ensure_directories()
            
            # 保存配置到JSON文件
            with open(self.DB_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            print(f"保存数据库配置失败: {str(e)}")
            return False
    
    def load_database_config(self) -> Dict[str, Any]:
        """从文件加载数据库配置"""
        try:
            if not os.path.exists(self.DB_CONFIG_FILE):
                # 如果配置文件不存在，返回默认配置
                return {
                    'oracle': {
                        'host': '127.0.0.1',
                        'port': '1521',
                        'username': 'datachange',
                        'password': 'drgs2019',
                        'dsn': 'orcl'
                    },
                    'postgresql': {
                        'host': '*************',
                        'port': '5432',
                        'username': 'postgres',
                        'password': 'P@ssw0rd',
                        'database': 'databasetools'
                    }
                }
            
            with open(self.DB_CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            return config
        except Exception as e:
            print(f"加载数据库配置失败: {str(e)}")
            # 返回默认配置
            return {
                'oracle': {
                    'host': '127.0.0.1',
                    'port': '1521',
                    'username': 'datachange',
                    'password': 'drgs2019',
                    'dsn': 'orcl'
                },
                'postgresql': {
                    'host': '*************',
                    'port': '5432',
                    'username': 'postgres',
                    'password': 'P@ssw0rd',
                    'database': 'databasetools'
                }
            }
    
    def save_database_connection(self, connection_data: Dict[str, Any]) -> Dict[str, Any]:
        """保存数据库连接配置"""
        try:
            # 确保配置目录存在
            self._ensure_directories()
            
            # 加载现有连接配置
            connections = self.load_database_connections()
            
            # 生成唯一ID
            connection_id = connection_data.get('id') or str(uuid.uuid4())
            
            # 创建连接配置
            connection_config = {
                'id': connection_id,
                'name': connection_data.get('name', f'连接_{connection_id[:8]}'),
                'host': connection_data.get('host', ''),
                'port': connection_data.get('port', ''),
                'database': connection_data.get('database', ''),
                'username': connection_data.get('username', ''),
                'password': connection_data.get('password', ''),
                'database_type': connection_data.get('database_type', 'postgresql'),
                'created_at': connection_data.get('created_at', datetime.now().isoformat()),
                'updated_at': datetime.now().isoformat(),
                'description': connection_data.get('description', '')
            }
            
            # 保存到连接列表
            connections[connection_id] = connection_config
            
            # 保存到文件
            with open(self.DB_CONNECTIONS_FILE, 'w', encoding='utf-8') as f:
                json.dump(connections, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'connection': connection_config,
                'message': '数据库连接配置已保存'
            }
            
        except Exception as e:
            print(f"保存数据库连接配置失败: {str(e)}")
            return {
                'success': False,
                'error': f'保存配置失败: {str(e)}'
            }
    
    def load_database_connections(self) -> Dict[str, Any]:
        """加载所有数据库连接配置"""
        try:
            if not os.path.exists(self.DB_CONNECTIONS_FILE):
                return {}
            
            with open(self.DB_CONNECTIONS_FILE, 'r', encoding='utf-8') as f:
                connections = json.load(f)
            
            return connections
        except Exception as e:
            print(f"加载数据库连接配置失败: {str(e)}")
            return {}
    
    def delete_database_connection(self, connection_id: str) -> Dict[str, Any]:
        """删除数据库连接配置"""
        try:
            connections = self.load_database_connections()
            
            if connection_id not in connections:
                return {
                    'success': False,
                    'error': '连接配置不存在'
                }
            
            # 删除连接配置
            deleted_connection = connections.pop(connection_id)
            
            # 保存更新后的配置
            with open(self.DB_CONNECTIONS_FILE, 'w', encoding='utf-8') as f:
                json.dump(connections, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'message': f'连接配置 "{deleted_connection.get("name", connection_id)}" 已删除',
                'deleted_connection': deleted_connection
            }
            
        except Exception as e:
            print(f"删除数据库连接配置失败: {str(e)}")
            return {
                'success': False,
                'error': f'删除配置失败: {str(e)}'
            }
    
    def update_database_connection(self, connection_id: str, connection_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新数据库连接配置"""
        try:
            connections = self.load_database_connections()
            
            if connection_id not in connections:
                return {
                    'success': False,
                    'error': '连接配置不存在'
                }
            
            # 更新连接配置
            connections[connection_id].update({
                'name': connection_data.get('name', connections[connection_id]['name']),
                'host': connection_data.get('host', connections[connection_id]['host']),
                'port': connection_data.get('port', connections[connection_id]['port']),
                'database': connection_data.get('database', connections[connection_id]['database']),
                'username': connection_data.get('username', connections[connection_id]['username']),
                'password': connection_data.get('password', connections[connection_id]['password']),
                'database_type': connection_data.get('database_type', connections[connection_id]['database_type']),
                'description': connection_data.get('description', connections[connection_id]['description']),
                'updated_at': datetime.now().isoformat()
            })
            
            # 保存更新后的配置
            with open(self.DB_CONNECTIONS_FILE, 'w', encoding='utf-8') as f:
                json.dump(connections, f, ensure_ascii=False, indent=2)
            
            return {
                'success': True,
                'connection': connections[connection_id],
                'message': '数据库连接配置已更新'
            }
            
        except Exception as e:
            print(f"更新数据库连接配置失败: {str(e)}")
            return {
                'success': False,
                'error': f'更新配置失败: {str(e)}'
            }
    
    def get_database_connection(self, connection_id: str) -> Dict[str, Any]:
        """获取指定的数据库连接配置"""
        try:
            connections = self.load_database_connections()
            return connections.get(connection_id, {})
        except Exception as e:
            print(f"获取数据库连接配置失败: {str(e)}")
            return {}


# Global configuration instance
config = Config()