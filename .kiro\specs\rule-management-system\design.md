# Design Document

## Overview

临时规则编写工具是一个基于Flask的Web应用程序，旨在为医保风控业务人员提供一个直观、高效的规则创建和管理平台。系统采用分层单体架构，通过模板化的方式将复杂的SQL规则生成过程简化为表单填写，显著降低了业务人员的技术门槛。

系统的核心价值在于将原本需要技术人员参与、耗时数小时甚至数天的规则开发工作，转化为业务人员几分钟内即可完成的自助服务，从而大幅提升临时性、小批量规则的创建效率。

## Architecture

### System Architecture Overview

```mermaid
graph TB
    subgraph "前端层 (Presentation Layer)"
        A[规则管理页面]
        B[SQL生成器页面]
        C[数据库配置页面]
    end
    
    subgraph "应用层 (Application Layer)"
        D[Flask路由控制器]
        E[API端点]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        F[规则管理服务]
        G[SQL生成服务]
        H[模板管理服务]
        I[文件操作服务]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        J[文件系统DAO]
        K[模板文件DAO]
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        L[文件系统存储]
        M[SQL模板库]
        N[配置管理]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
    F --> I
    G --> H
    H --> J
    I --> J
    J --> K
    K --> L
    K --> M
    F --> N
    G --> N
```

### Technology Stack

- **后端框架**: Flask 2.x - 轻量级、灵活的Python Web框架
- **前端技术**: HTML5 + CSS3 + JavaScript (ES6+) + Tailwind CSS
- **模板引擎**: Jinja2 - 用于SQL模板的参数化渲染
- **文件存储**: 本地文件系统 - 规则和模板的持久化存储
- **代码高亮**: Highlight.js - SQL代码的语法高亮显示
- **UI框架**: Tailwind CSS + Font Awesome - 现代化的响应式界面
- **数据格式**: JSON - API数据交换格式

## Components and Interfaces

### Core Components

#### 1. Rule Management Service (规则管理服务)

**职责**: 负责规则的完整生命周期管理，包括创建、读取、更新、删除操作。

**接口定义**:
```python
class RuleManagementService:
    def list_rules() -> List[RuleInfo]
    def get_rule(rule_name: str) -> Rule
    def save_rule(rule_data: RuleData) -> Result
    def delete_rule(rule_name: str) -> Result
    def update_rule(rule_name: str, rule_data: RuleData) -> Result
```

**核心方法**:
- `list_rules()`: 扫描output目录，返回所有已保存规则的元数据
- `get_rule(rule_name)`: 读取指定规则的完整内容
- `save_rule(rule_data)`: 将规则数据保存为.sql文件
- `delete_rule(rule_name)`: 删除指定的规则文件

#### 2. SQL Generation Service (SQL生成服务)

**职责**: 基于选定的模板和用户输入的参数，生成符合业务需求的SQL查询语句。

**接口定义**:
```python
class SqlGenerationService:
    def get_templates() -> List[TemplateInfo]
    def get_template_content(template_id: str) -> TemplateContent
    def generate_sql(template_id: str, parameters: Dict) -> GeneratedSQL
    def validate_parameters(template_id: str, parameters: Dict) -> ValidationResult
```

**核心方法**:
- `get_templates()`: 获取所有可用的SQL模板列表
- `generate_sql()`: 使用Jinja2引擎将参数填充到模板中
- `validate_parameters()`: 验证用户输入参数的完整性和有效性

#### 3. Template Management Service (模板管理服务)

**职责**: 管理SQL模板的加载、解析和分类，支持多数据库类型的模板适配。

**接口定义**:
```python
class TemplateManagementService:
    def load_template(template_path: str) -> Template
    def parse_template_parameters(template_content: str) -> List[Parameter]
    def get_template_categories() -> List[Category]
    def get_templates_by_category(category: str) -> List[Template]
```

### API Endpoints

#### RESTful API设计

| HTTP方法 | 端点 | 功能描述 | 请求体 | 响应体 |
|---------|------|---------|--------|--------|
| GET | `/api/rules` | 获取规则列表 | - | `List<RuleInfo>` |
| POST | `/api/rules` | 创建新规则 | `RuleData` | `Result` |
| GET | `/api/rules/{name}` | 获取特定规则 | - | `Rule` |
| DELETE | `/api/rules/{name}` | 删除规则 | - | `Result` |
| GET | `/api/sql-templates` | 获取模板列表 | - | `List<Template>` |
| POST | `/api/sql/generate` | 生成SQL | `GenerationRequest` | `GeneratedSQL` |

#### 数据模型定义

```python
# 规则信息模型
@dataclass
class RuleInfo:
    name: str
    created_at: float
    file_size: int
    status: str = "active"

# 规则数据模型
@dataclass
class RuleData:
    name: str
    content: str
    description: Optional[str] = None
    category: Optional[str] = None

# 模板信息模型
@dataclass
class TemplateInfo:
    id: str
    description: str
    category: str
    parameters: List[str]
    database_type: str

# SQL生成请求模型
@dataclass
class GenerationRequest:
    template_id: str
    parameters: Dict[str, Any]
    database_type: str = "postgresql"
```

## Data Models

### File System Data Structure

```
project-root/
├── output/                    # 规则存储目录
│   ├── rule1.sql             # 用户创建的规则文件
│   ├── rule2.sql
│   └── ...
├── templates/                 # SQL模板库
│   ├── rule/                 # 通用规则模板
│   │   ├── 超每日数量.sql
│   │   ├── 重复收费.sql
│   │   └── 限性别.sql
│   ├── rule_pg_name_inpatient/  # PostgreSQL住院模板
│   ├── rule_oracle_name_inpatient/  # Oracle住院模板
│   └── ...
└── page/                     # 前端页面模板
    └── temp_rule_editor.html
```

### Template Parameter Model

SQL模板使用大括号语法定义参数化字段：

```sql
-- 示例：超每日数量模板
WHERE 医保项目名称 in ({医保名称1})
HAVING sum(数量) > {违规数量}
and not A.出院诊断名称 ~* '({排除诊断})'
and not B.开单科室名称 ~* '({排除科室})'
and a.患者性别 <> '{性别}'
```

**参数类型定义**:
- `{医保名称1}`: 医保项目名称列表，支持多选
- `{违规数量}`: 数值型参数，定义违规阈值
- `{排除诊断}`: 文本型参数，正则表达式模式
- `{排除科室}`: 文本型参数，科室名称模式
- `{性别}`: 枚举型参数，男/女选择

### Rule Metadata Model

```python
class RuleMetadata:
    """规则元数据模型"""
    name: str              # 规则名称
    description: str       # 规则描述
    policy_basis: str      # 政策依据
    rule_type: str         # 规则类型（超频次、重复收费等）
    template_id: str       # 使用的模板ID
    parameters: Dict       # 模板参数值
    database_type: str     # 目标数据库类型
    created_by: str        # 创建者
    created_at: datetime   # 创建时间
    updated_at: datetime   # 更新时间
    status: str           # 规则状态（active, inactive, draft）
```

## Error Handling

### Error Classification

#### 1. 业务逻辑错误
- **规则名称冲突**: 当用户尝试创建已存在的规则名称时
- **模板参数缺失**: 必填参数未提供或格式不正确
- **规则内容为空**: SQL内容为空或仅包含注释

#### 2. 系统级错误
- **文件系统错误**: 磁盘空间不足、权限不足、文件损坏
- **模板加载错误**: 模板文件不存在或格式错误
- **网络错误**: API请求超时或连接失败

#### 3. 用户输入错误
- **参数验证失败**: 数值超出范围、格式不符合要求
- **SQL语法错误**: 生成的SQL包含语法错误
- **权限错误**: 用户尝试访问无权限的资源

### Error Response Format

```json
{
  "status": "error",
  "error_code": "RULE_NAME_CONFLICT",
  "message": "规则名称已存在，请选择其他名称",
  "details": {
    "field": "rule_name",
    "value": "existing_rule_name",
    "suggestion": "尝试添加日期或版本号后缀"
  },
  "timestamp": "2023-10-27T10:30:00Z"
}
```

### Error Handling Strategy

```python
class ErrorHandler:
    """统一错误处理器"""
    
    @staticmethod
    def handle_business_error(error: BusinessError) -> Response:
        """处理业务逻辑错误"""
        return jsonify({
            "status": "error",
            "error_code": error.code,
            "message": error.message,
            "details": error.details
        }), 400
    
    @staticmethod
    def handle_system_error(error: SystemError) -> Response:
        """处理系统级错误"""
        logger.error(f"System error: {error}")
        return jsonify({
            "status": "error",
            "error_code": "SYSTEM_ERROR",
            "message": "系统暂时不可用，请稍后重试"
        }), 500
    
    @staticmethod
    def handle_validation_error(error: ValidationError) -> Response:
        """处理输入验证错误"""
        return jsonify({
            "status": "error",
            "error_code": "VALIDATION_ERROR",
            "message": "输入数据验证失败",
            "details": error.validation_errors
        }), 422
```

## Testing Strategy

### Unit Testing Plan

#### 1. 服务层测试
```python
class TestRuleManagementService:
    """规则管理服务单元测试"""
    
    def test_list_rules_empty_directory(self):
        """测试空目录下的规则列表"""
        
    def test_save_rule_success(self):
        """测试成功保存规则"""
        
    def test_save_rule_duplicate_name(self):
        """测试重复规则名称处理"""
        
    def test_delete_rule_not_found(self):
        """测试删除不存在的规则"""

class TestSqlGenerationService:
    """SQL生成服务单元测试"""
    
    def test_generate_sql_with_valid_parameters(self):
        """测试使用有效参数生成SQL"""
        
    def test_generate_sql_missing_parameters(self):
        """测试缺失参数时的错误处理"""
        
    def test_template_parameter_parsing(self):
        """测试模板参数解析功能"""
```

#### 2. API端点测试
```python
class TestRuleAPI:
    """规则API端点测试"""
    
    def test_get_rules_list(self):
        """测试获取规则列表API"""
        
    def test_create_rule_success(self):
        """测试创建规则API成功场景"""
        
    def test_create_rule_invalid_data(self):
        """测试创建规则API数据验证"""
        
    def test_delete_rule_success(self):
        """测试删除规则API成功场景"""

class TestTemplateAPI:
    """模板API端点测试"""
    
    def test_get_templates_list(self):
        """测试获取模板列表API"""
        
    def test_generate_sql_api(self):
        """测试SQL生成API"""
```

#### 3. 前端交互测试
```javascript
describe('Rule Management UI', () => {
    test('should load rules list on page load', () => {
        // 测试页面加载时规则列表的显示
    });
    
    test('should generate SQL when template is selected', () => {
        // 测试选择模板后SQL的生成
    });
    
    test('should save rule successfully', () => {
        // 测试规则保存功能
    });
    
    test('should handle API errors gracefully', () => {
        // 测试API错误的前端处理
    });
});
```

### Integration Testing

#### 1. 端到端工作流测试
- **规则创建流程**: 从模板选择到参数填写再到规则保存的完整流程
- **规则管理流程**: 规则的查看、编辑、删除操作
- **模板系统集成**: 模板加载、参数解析、SQL生成的集成测试

#### 2. 文件系统集成测试
- **文件读写操作**: 测试规则文件的创建、读取、更新、删除
- **目录管理**: 测试output和templates目录的管理
- **并发访问**: 测试多用户同时操作时的文件系统行为

#### 3. 模板系统集成测试
- **多数据库模板**: 测试Oracle和PostgreSQL模板的正确加载
- **参数化渲染**: 测试Jinja2模板引擎的参数替换功能
- **模板验证**: 测试模板语法和参数定义的验证

### Performance Testing

#### 1. 响应时间测试
- **页面加载时间**: 确保主页面在2秒内完成加载
- **SQL生成时间**: 确保SQL生成在1秒内完成
- **规则列表加载**: 测试大量规则时的列表加载性能

#### 2. 并发测试
- **多用户同时访问**: 测试系统在多用户并发访问时的稳定性
- **文件系统并发**: 测试多个用户同时创建/修改规则时的数据一致性

#### 3. 资源使用测试
- **内存使用**: 监控应用程序的内存占用情况
- **磁盘空间**: 测试大量规则文件对磁盘空间的影响
- **CPU使用**: 监控SQL生成和模板渲染的CPU消耗