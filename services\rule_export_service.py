"""
Rule export service for exporting local rules to remote databases.
"""
import psycopg
import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from services.rule_service import RuleManagementService
from utils.error_handler import BusinessError, SystemError

logger = logging.getLogger(__name__)

class RuleExportService:
    """Service for exporting local rules to remote databases."""
    
    def __init__(self):
        self.rule_service = RuleManagementService()
    
    def export_rules_to_database(self, connection_info: Dict[str, Any], 
                                selected_rule_names: List[str]) -> Dict[str, Any]:
        """
        将本地规则导出到远程数据库
        
        Args:
            connection_info: 数据库连接信息
            selected_rule_names: 选定的规则名称列表
            
        Returns:
            导出结果字典
        """
        try:
            host = connection_info.get('host', 'localhost')
            port = connection_info.get('port', 5432)
            database = connection_info.get('database', 'databasetools')
            username = connection_info.get('username', 'postgres')
            password = connection_info.get('password', 'P@ssw0rd')
            
            exported_count = 0
            failed_rules = []
            updated_count = 0
            
            # 连接远程数据库
            conn_str = f"host={host} port={port} dbname={database} user={username} password={password}"
            with psycopg.connect(conn_str) as conn:
                with conn.cursor() as cursor:
                    for rule_name in selected_rule_names:
                        try:
                            # 获取本地规则
                            local_rule = self.rule_service.get_rule(rule_name)
                            
                            # 检查远程数据库中是否已存在该规则
                            cursor.execute("""
                                SELECT id, rule_name, sql_name 
                                FROM public.kj_rule 
                                WHERE rule_name = %s
                            """, (rule_name,))
                            
                            existing_rule = cursor.fetchone()
                            
                            if existing_rule:
                                # 规则已存在，更新sql_name字段
                                rule_id = existing_rule[0]
                                cursor.execute("""
                                    UPDATE public.kj_rule 
                                    SET sql_name = %s, 
                                        modby = %s, 
                                        modtime = %s
                                    WHERE id = %s
                                """, (
                                    local_rule.content,
                                    'system_export',
                                    datetime.now(),
                                    rule_id
                                ))
                                updated_count += 1
                                logger.info(f"更新规则: {rule_name}")
                            else:
                                # 规则不存在，创建新规则
                                rule_id = str(uuid.uuid4())
                                cursor.execute("""
                                    INSERT INTO public.kj_rule (
                                        id, rule_name, rule_type, medical_category, 
                                        rule_intension, status, creator, create_time, 
                                        modby, sql_name
                                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """, (
                                    rule_id,
                                    rule_name,
                                    local_rule.rule_type or '病例提取',
                                    local_rule.category or '导入规则',
                                    local_rule.description or '',
                                    '1',  # 启用状态
                                    'system_export',
                                    datetime.now(),
                                    'system_export',
                                    local_rule.content
                                ))
                                exported_count += 1
                                logger.info(f"创建新规则: {rule_name}")
                            
                        except Exception as e:
                            error_msg = f"规则 {rule_name}: {str(e)}"
                            failed_rules.append(error_msg)
                            logger.error(error_msg)
                    
                    # 提交事务
                    conn.commit()
            
            # 返回导出结果
            result = {
                'success': True,
                'exported_count': exported_count,
                'updated_count': updated_count,
                'failed_count': len(failed_rules),
                'failed_rules': failed_rules,
                'connection_info': {
                    'name': connection_info.get('name', ''),
                    'host': host,
                    'port': port,
                    'database': database
                }
            }
            
            if failed_rules:
                result['message'] = f'成功导出 {exported_count} 个规则，更新 {updated_count} 个规则，{len(failed_rules)} 个失败'
            else:
                result['message'] = f'成功导出 {exported_count} 个规则，更新 {updated_count} 个规则'
            
            return result
            
        except Exception as e:
            logger.error(f"导出规则失败: {str(e)}")
            raise SystemError(f"导出规则失败: {str(e)}")
    
    def validate_export_data(self, rule_names: List[str]) -> List[str]:
        """
        验证要导出的规则数据
        
        Args:
            rule_names: 规则名称列表
            
        Returns:
            验证错误列表
        """
        errors = []
        
        if not rule_names:
            errors.append("没有选择要导出的规则")
            return errors
        
        for rule_name in rule_names:
            try:
                # 检查规则是否存在
                rule = self.rule_service.get_rule(rule_name)
                
                # 检查规则内容
                if not rule.content or not rule.content.strip():
                    errors.append(f"规则 {rule_name}: 规则内容为空")
                
                # 检查规则名称长度
                if len(rule_name) > 100:
                    errors.append(f"规则 {rule_name}: 规则名称过长（超过100字符）")
                
                # 检查规则内容长度
                if len(rule.content) > 10000:
                    errors.append(f"规则 {rule_name}: 规则内容过长（超过10000字符）")
                    
            except Exception as e:
                errors.append(f"规则 {rule_name}: {str(e)}")
        
        return errors
    
    def get_export_preview(self, rule_names: List[str]) -> Dict[str, Any]:
        """
        获取导出预览信息
        
        Args:
            rule_names: 规则名称列表
            
        Returns:
            预览信息字典
        """
        try:
            preview_data = []
            total_size = 0
            
            for rule_name in rule_names:
                try:
                    rule = self.rule_service.get_rule(rule_name)
                    rule_size = len(rule.content.encode('utf-8'))
                    total_size += rule_size
                    
                    preview_data.append({
                        'name': rule_name,
                        'description': rule.description or '',
                        'rule_type': rule.rule_type or '病例提取',
                        'category': rule.category or '导入规则',
                        'size': rule_size,
                        'content_preview': rule.content[:200] + '...' if len(rule.content) > 200 else rule.content
                    })
                    
                except Exception as e:
                    logger.error(f"获取规则 {rule_name} 预览失败: {str(e)}")
                    preview_data.append({
                        'name': rule_name,
                        'error': str(e),
                        'size': 0
                    })
            
            return {
                'success': True,
                'preview_data': preview_data,
                'total_count': len(rule_names),
                'total_size': total_size,
                'estimated_time': len(rule_names) * 2  # 估算时间（秒）
            }
            
        except Exception as e:
            logger.error(f"获取导出预览失败: {str(e)}")
            raise SystemError(f"获取导出预览失败: {str(e)}") 