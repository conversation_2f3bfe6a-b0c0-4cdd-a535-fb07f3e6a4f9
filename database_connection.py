"""
数据库连接管理模块
"""
import oracledb
import psycopg
from psycopg_pool import ConnectionPool
import logging

logger = logging.getLogger(__name__)

# 全局连接池变量
oracle_pool = None
postgresql_pool = None

def init_db_pool():
    """初始化数据库连接池"""
    global oracle_pool, postgresql_pool
    
    try:
        # 尝试从配置文件加载数据库配置
        try:
            from utils.config import Config
            config = Config()
            db_configs = config.load_database_config()
            oracle_config = db_configs.get('oracle', {})
            postgresql_config = db_configs.get('postgresql', {})
            logger.info("成功从配置文件加载数据库配置")
        except Exception as config_error:
            logger.warning(f"无法从配置文件加载数据库配置，使用默认配置: {str(config_error)}")
            # 使用默认配置
            oracle_config = {
                'username': 'datachange',
                'password': 'drgs2019',
                'host': '127.0.0.1',
                'port': '1521',
                'dsn': 'orcl'
            }
            postgresql_config = {
                'host': '*************',
                'port': '5432',
                'username': 'postgres',
                'password': 'P@ssw0rd',
                'database': 'databasetools'
            }
        
        # 初始化Oracle客户端，用来支持11g的oracle
        try:
            oracledb.init_oracle_client()  
            logger.info("Oracle客户端初始化成功")
        except Exception as e:
            # 如果已经初始化过，会抛出异常，但这不影响后续操作
            logger.warning(f"Oracle客户端初始化时出现警告（可能已初始化）: {str(e)}")

        # 创建Oracle连接池，使用配置参数
        oracle_dsn = f"{oracle_config.get('host', '127.0.0.1')}:{oracle_config.get('port', '1521')}/{oracle_config.get('dsn', 'orcl')}"
        oracle_pool = oracledb.create_pool(
            user=oracle_config.get('username', 'datachange'),
            password=oracle_config.get('password', 'drgs2019'),
            dsn=oracle_dsn,
            min=3,                           # 增加最小连接数
            max=10,                          # 增加最大连接数
            increment=1,
            getmode=oracledb.POOL_GETMODE_WAIT,
            wait_timeout=20000,              # 增加等待超时时间
            timeout=600,                     # 增加超时时间
            retry_count=5,                   # 增加重试次数
            retry_delay=2,
            max_lifetime_session=43200,      # 增加会话生命周期到12小时
            ping_interval=60                 # 添加ping间隔，保持连接活跃
        )
        
        logger.info("Oracle连接池初始化成功")
        
        # 初始化PostgreSQL连接池
        try:
            # 确保数据库名称不为空，如果为空则使用默认值
            database_name = postgresql_config.get('database', 'databasetools')
            if not database_name:
                database_name = 'databasetools'
                
            pg_conninfo = (
                f"host={postgresql_config.get('host', '*************')} "
                f"port={postgresql_config.get('port', '5432')} "
                f"dbname={database_name} "
                f"user={postgresql_config.get('username', 'postgres')} "
                f"password={postgresql_config.get('password', 'P@ssw0rd')} "
            )
            
            postgresql_pool = ConnectionPool(
                conninfo=pg_conninfo,
                min_size=2,
                max_size=5,
                timeout=30
            )
            logger.info("PostgreSQL连接池初始化成功")
            return oracle_pool, postgresql_pool
        except Exception as pg_error:
            logger.error(f"PostgreSQL连接池初始化失败: {str(pg_error)}")
            # 即使PostgreSQL连接池初始化失败，我们仍然返回Oracle连接池
            # 这样即使PostgreSQL不可用，Oracle相关功能仍然可以正常工作
            return oracle_pool, None
        
    except Exception as e:
        logger.error(f"Oracle连接池初始化失败: {str(e)}")
        raise

def init_oracle_pool():
    """初始化Oracle连接池（兼容性函数）"""
    global oracle_pool
    try:
        oracle_pool, _ = init_db_pool()
        return oracle_pool
    except Exception as e:
        logger.error(f"Oracle连接池初始化失败: {str(e)}")
        return None

def init_postgresql_pool():
    """初始化PostgreSQL连接池（兼容性函数）"""
    global postgresql_pool
    try:
        _, postgresql_pool = init_db_pool()
        return postgresql_pool
    except Exception as e:
        logger.error(f"PostgreSQL连接池初始化失败: {str(e)}")
        return None

def get_oracle_connection():
    """获取Oracle连接"""
    global oracle_pool
    if oracle_pool is None:
        oracle_pool = init_oracle_pool()
    
    if oracle_pool:
        return oracle_pool.acquire()
    else:
        # 如果连接池不可用，返回直接连接
        return oracledb.connect(
            user="datachange",
            password="drgs2019",
            dsn="127.0.0.1:1521/orcl"
        )

def get_postgresql_connection():
    """获取PostgreSQL连接"""
    global postgresql_pool
    if postgresql_pool is None:
        postgresql_pool = init_postgresql_pool()
    
    if postgresql_pool:
        return postgresql_pool.getconn()
    else:
        # 如果连接池不可用，返回直接连接
        # 尝试从配置文件加载配置
        try:
            from utils.config import Config
            config = Config()
            db_configs = config.load_database_config()
            postgresql_config = db_configs.get('postgresql', {})
            
            database_name = postgresql_config.get('database', 'databasetools')
            if not database_name:
                database_name = 'databasetools'
                
            return psycopg.connect(
                host=postgresql_config.get('host', '*************'),
                port=postgresql_config.get('port', 5432),
                dbname=database_name,
                user=postgresql_config.get('username', 'postgres'),
                password=postgresql_config.get('password', 'P@ssw0rd')
            )
        except Exception:
            # 如果加载配置失败，使用默认值
            return psycopg.connect(
                host="*************",
                port=5432,
                dbname="databasetools",
                user="postgres",
                password="P@ssw0rd"
            )

def close_oracle_connection(connection):
    """关闭Oracle连接"""
    global oracle_pool
    if oracle_pool and connection:
        oracle_pool.release(connection)
    elif connection:
        connection.close()

def close_postgresql_connection(connection):
    """关闭PostgreSQL连接"""
    global postgresql_pool
    if postgresql_pool and connection:
        postgresql_pool.putconn(connection)
    elif connection:
        connection.close()

def close_all_pools():
    """关闭所有连接池"""
    global oracle_pool, postgresql_pool
    
    if oracle_pool:
        oracle_pool.close()
        oracle_pool = None
        logger.info("Oracle连接池已关闭")
    
    if postgresql_pool:
        postgresql_pool.close()
        postgresql_pool = None
        logger.info("PostgreSQL连接池已关闭") 