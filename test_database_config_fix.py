#!/usr/bin/env python3
"""
数据库配置修复测试脚本
"""
import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:5000"

def test_database_config_save():
    """测试数据库配置保存功能"""
    print("=== 测试数据库配置保存功能 ===")
    
    # 测试保存Oracle配置
    oracle_config = {
        "type": "oracle",
        "host": "*************",
        "port": "1521",
        "username": "test_user",
        "password": "test_pass"
    }
    
    response = requests.post(f"{BASE_URL}/api/database/config/save", 
                           json=oracle_config)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ Oracle配置保存成功")
        else:
            print(f"❌ Oracle配置保存失败: {result.get('error')}")
    else:
        print(f"❌ Oracle配置保存请求失败: {response.status_code}")
    
    # 测试保存PostgreSQL配置
    pg_config = {
        "type": "pg",
        "host": "*************",
        "port": "5432",
        "username": "pg_user",
        "password": "pg_pass"
    }
    
    response = requests.post(f"{BASE_URL}/api/database/config/save", 
                           json=pg_config)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ PostgreSQL配置保存成功")
        else:
            print(f"❌ PostgreSQL配置保存失败: {result.get('error')}")
    else:
        print(f"❌ PostgreSQL配置保存请求失败: {response.status_code}")

def test_database_config_load():
    """测试数据库配置加载功能"""
    print("\n=== 测试数据库配置加载功能 ===")
    
    response = requests.get(f"{BASE_URL}/api/database/config/load")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            config = result.get('config', {})
            print("✅ 数据库配置加载成功")
            print(f"Oracle配置: {config.get('oracle', '未配置')}")
            print(f"PostgreSQL配置: {config.get('postgresql', '未配置')}")
        else:
            print(f"❌ 数据库配置加载失败: {result.get('error')}")
    else:
        print(f"❌ 数据库配置加载请求失败: {response.status_code}")

def test_medical_insurance_search():
    """测试医保项目检索功能"""
    print("\n=== 测试医保项目检索功能 ===")
    
    # 测试使用保存的配置进行检索
    search_data = {
        "search_term": "血常规",
        "database": "pg",
        "host": "default",  # 使用默认主机，应该会加载保存的配置
        "schema": "",
        "limit": 10
    }
    
    response = requests.post(f"{BASE_URL}/api/medical-insurance/search", 
                           json=search_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            data = result.get('data', [])
            total = result.get('total', 0)
            print(f"✅ 医保项目检索成功，找到 {total} 个项目")
            if data:
                print(f"第一个项目: {data[0]}")
        else:
            print(f"❌ 医保项目检索失败: {result.get('error')}")
    else:
        print(f"❌ 医保项目检索请求失败: {response.status_code}")

def test_page_initialization():
    """测试页面初始化时的配置加载"""
    print("\n=== 测试页面初始化 ===")
    
    # 访问主页面
    response = requests.get(f"{BASE_URL}/")
    
    if response.status_code == 200:
        print("✅ 主页面加载成功")
        # 检查页面内容是否包含配置相关的元素
        content = response.text
        if 'databaseSelect' in content and 'hostInput' in content:
            print("✅ 页面包含数据库配置元素")
        else:
            print("❌ 页面缺少数据库配置元素")
    else:
        print(f"❌ 主页面加载失败: {response.status_code}")

def main():
    """主测试函数"""
    print("开始测试数据库配置修复效果...")
    print(f"测试目标: {BASE_URL}")
    
    try:
        # 测试配置保存
        test_database_config_save()
        
        # 等待一下确保配置保存完成
        time.sleep(1)
        
        # 测试配置加载
        test_database_config_load()
        
        # 测试医保检索功能
        test_medical_insurance_search()
        
        # 测试页面初始化
        test_page_initialization()
        
        print("\n=== 测试完成 ===")
        print("请检查以上测试结果，确保所有功能正常工作。")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保应用正在运行")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")

if __name__ == "__main__":
    main() 