<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时规则编写工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
    <style>
        /* For modal transitions */
        .modal-backdrop {
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-300 font-sans p-8">

    <!-- Main Container -->
    <div class="container mx-auto space-y-8">

        <!-- Part 1: SQL Generator -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white"><i class="fas fa-magic-wand-sparkles mr-2"></i>规则SQL生成器</h1>
                <button onclick="openModal('db-config-modal')" class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-database mr-2"></i> 数据库参数配置
                </button>
            </div>

            <div class="flex space-x-8">
                <!-- Left Panel: Form -->
                <div class="w-1/2 space-y-6">
                    <div>
                        <label for="rule-name" class="block mb-2 text-sm font-medium text-gray-300">规则名称</label>
                        <input type="text" id="rule-name" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" placeholder="例如：限定支付-限单人多次">
                    </div>

                    <div>
                        <label for="rule-template" class="block mb-2 text-sm font-medium text-gray-300">SQL模板</label>
                        <select id="rule-template" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                            <option value="">选择一个模板</option>
                            <option value="template_a">模板A：按病人分组，限定项目编码</option>
                            <option value="template_b">模板B：按就诊记录，限定诊断和药品</option>
                        </select>
                    </div>

                    <div class="border-t border-gray-700 pt-6">
                        <h3 class="text-lg font-semibold text-white mb-4">规则条件 (由模板生成)</h3>
                        <div class="space-y-4" id="conditions-container">
                            <p class="text-gray-500">请先选择一个SQL模板</p>
                        </div>
                    </div>
                </div>

                <!-- Right Panel: SQL Preview -->
                <div class="w-1/2 flex flex-col">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-white">SQL预览</h2>
                        <button id="copy-sql" class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm"><i class="fas fa-copy mr-2"></i> 复制</button>
                    </div>
                    <div class="bg-gray-900 rounded-lg flex-grow p-1 relative">
                        <pre><code id="sql-output" class="language-sql h-full block">-- 在左侧表单中填写信息以生成SQL...</code></pre>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg flex items-center"><i class="fas fa-check-circle mr-2"></i> 保存规则</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Part 2: Rule Management -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white"><i class="fas fa-tasks mr-2"></i>规则管理</h1>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-400">
                    <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3">规则名称</th>
                            <th scope="col" class="px-6 py-3">来源</th>
                            <th scope="col" class="px-6 py-3">状态</th>
                            <th scope="col" class="px-6 py-3">创建日期</th>
                            <th scope="col" class="px-6 py-3 text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                            <td class="px-6 py-4 font-medium text-white whitespace-nowrap">限定支付-限单人多次</td>
                            <td class="px-6 py-4">手动创建</td>
                            <td class="px-6 py-4"><span class="bg-green-900 text-green-300 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">已启用</span></td>
                            <td class="px-6 py-4">2023-10-26</td>
                            <td class="px-6 py-4 text-center">
                                <button onclick='editRule("限定支付-限单人多次", "template_a", [{"field": "项目编码", "op": "等于", "value": "P001"}])' class="font-medium text-blue-500 hover:underline">编辑</button>
                            </td>
                        </tr>
                        <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                            <td class="px-6 py-4 font-medium text-white whitespace-nowrap">项目内涵-重复收费</td>
                            <td class="px-6 py-4">模型生成</td>
                            <td class="px-6 py-4"><span class="bg-green-900 text-green-300 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">已启用</span></td>
                            <td class="px-6 py-4">2023-09-11</td>
                            <td class="px-6 py-4 text-center">
                                <button onclick='editRule("项目内涵-重复收费", "template_b", [{"field": "诊断编码", "op": "包含", "value": "J45"}, {"field": "药品编码", "op": "等于", "value": "D005"}])' class="font-medium text-blue-500 hover:underline">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- DB Config Modal -->
    <div id="db-config-modal" class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-md transform scale-95">
            <h2 class="text-2xl font-bold text-white mb-6">数据库配置</h2>
            <div class="space-y-4">
                <div>
                    <label for="db-type" class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                    <select id="db-type" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        <option>PostgreSQL</option>
                        <option>Oracle</option>
                    </select>
                </div>
                <div>
                    <label for="db-host" class="block mb-2 text-sm font-medium text-gray-300">主机</label>
                    <input type="text" id="db-host" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5" placeholder="localhost">
                </div>
                <div>
                    <label for="db-port" class="block mb-2 text-sm font-medium text-gray-300">端口</label>
                    <input type="text" id="db-port" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5" placeholder="5432">
                </div>
                <div>
                    <label for="db-user" class="block mb-2 text-sm font-medium text-gray-300">用户名</label>
                    <input type="text" id="db-user" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5" placeholder="admin">
                </div>
                <div>
                    <label for="db-pass" class="block mb-2 text-sm font-medium text-gray-300">密码</label>
                    <input type="password" id="db-pass" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                </div>
            </div>
            <div class="flex justify-end space-x-4 mt-8">
                <button onclick="closeModal('db-config-modal')" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">取消</button>
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">保存</button>
            </div>
        </div>
    </div>

    <!-- Search Modal -->
    <div id="search-modal" class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-2xl transform scale-95">
            <h2 class="text-2xl font-bold text-white mb-6">从[医保三目表]检索</h2>
            <input type="text" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5 mb-4" placeholder="输入医保编码或名称...">
            <div class="max-h-80 overflow-y-auto">
                <table class="w-full text-sm text-left text-gray-400">
                    <thead class="text-xs text-gray-300 uppercase bg-gray-700 sticky top-0"><tr><th class="px-4 py-2">医保编码</th><th class="px-4 py-2">医保名称</th><th class="px-4 py-2">操作</th></tr></thead>
                    <tbody>
                        <tr class="border-b border-gray-700"><td>P001</td><td>项目一</td><td><button class="text-blue-500">选择</button></td></tr>
                        <tr class="border-b border-gray-700"><td>P002</td><td>项目二</td><td><button class="text-blue-500">选择</button></td></tr>
                        <tr class="border-b border-gray-700"><td>D005</td><td>药品A</td><td><button class="text-blue-500">选择</button></td></tr>
                    </tbody>
                </table>
            </div>
            <div class="flex justify-end mt-8">
                <button onclick="closeModal('search-modal')" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // --- Modal Logic ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.modal-content').classList.remove('scale-95');
                modal.classList.remove('opacity-0');
            }, 10);
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.querySelector('.modal-content').classList.add('scale-95');
            modal.classList.add('opacity-0');
            setTimeout(() => modal.classList.add('hidden'), 300);
        }

        // --- Template & Conditions Logic ---
        const templates = {
            'template_a': [{ name: '项目编码', type: 'searchable' }],
            'template_b': [{ name: '诊断编码', type: 'text' }, { name: '药品编码', type: 'searchable' }]
        };

        const conditionsContainer = document.getElementById('conditions-container');
        document.getElementById('rule-template').addEventListener('change', function() {
            const templateId = this.value;
            conditionsContainer.innerHTML = '';
            if (templateId && templates[templateId]) {
                templates[templateId].forEach(cond => {
                    const conditionHtml = `
                        <div class="flex items-center space-x-2">
                            <label class="w-1/4 text-sm">${cond.name}</label>
                            <select class="bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-1/4">
                                <option>等于</option><option>不等于</option><option>包含</option>
                            </select>
                            <div class="relative w-1/2">
                                <input type="text" class="bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-full pr-10">
                                ${cond.type === 'searchable' ? `<button onclick="openModal('search-modal')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white"><i class="fas fa-search"></i></button>` : ''}
                            </div>
                        </div>
                    `;
                    conditionsContainer.insertAdjacentHTML('beforeend', conditionHtml);
                });
            } else {
                conditionsContainer.innerHTML = '<p class="text-gray-500">请先选择一个SQL模板</p>';
            }
        });

        // --- Edit Rule Logic ---
        function editRule(name, templateId, conditions) {
            document.getElementById('rule-name').value = name;
            document.getElementById('rule-template').value = templateId;
            
            // Trigger change to populate conditions
            document.getElementById('rule-template').dispatchEvent(new Event('change'));

            // Fill condition values after a short delay to ensure they are created
            setTimeout(() => {
                const conditionRows = conditionsContainer.querySelectorAll('.flex.items-center');
                if (conditionRows.length === conditions.length) {
                    conditions.forEach((cond, index) => {
                        const row = conditionRows[index];
                        row.querySelector('select').value = cond.op;
                        row.querySelector('input[type=\"text\"]').value = cond.value;
                    });
                }
            }, 100);

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // --- Highlight & Copy Logic ---
        document.addEventListener('DOMContentLoaded', () => hljs.highlightAll());
        document.getElementById('copy-sql').addEventListener('click', () => {
            navigator.clipboard.writeText(document.getElementById('sql-output').textContent);
        });

    </script>
</body>
</html>