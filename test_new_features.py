#!/usr/bin/env python3
"""
测试新添加的功能：数据库监控和规则导入
"""
import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_database_connection():
    """测试数据库连接功能"""
    print("=== 测试数据库连接功能 ===")
    
    # 测试连接
    test_data = {
        "host": "localhost",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/database/test-connection", 
                               json=test_data)
        result = response.json()
        print(f"连接测试结果: {result}")
        return result.get('success', False)
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False

def test_check_requirements():
    """测试检查数据库需求功能"""
    print("\n=== 测试检查数据库需求功能 ===")
    
    test_data = {
        "host": "localhost",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/database/check-requirements", 
                               json=test_data)
        result = response.json()
        print(f"需求检查结果: {result}")
        return result.get('success', False)
    except Exception as e:
        print(f"需求检查失败: {e}")
        return False

def test_import_rules():
    """测试规则导入功能"""
    print("\n=== 测试规则导入功能 ===")
    
    test_data = {
        "host": "localhost",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/rules/import", 
                               json=test_data)
        result = response.json()
        print(f"规则导入查询结果: {result}")
        return result.get('success', False)
    except Exception as e:
        print(f"规则导入查询失败: {e}")
        return False

def test_import_execute():
    """测试规则导入执行功能"""
    print("\n=== 测试规则导入执行功能 ===")
    
    test_data = {
        "selected_rules": [1, 2, 3],  # 示例规则ID
        "overwrite": False,
        "keep_original": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/rules/import/execute", 
                               json=test_data)
        result = response.json()
        print(f"规则导入执行结果: {result}")
        return result.get('success', False)
    except Exception as e:
        print(f"规则导入执行失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试新功能...")
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    # 测试各项功能
    connection_ok = test_database_connection()
    requirements_ok = test_check_requirements()
    import_query_ok = test_import_rules()
    import_execute_ok = test_import_execute()
    
    # 输出测试结果
    print("\n=== 测试结果汇总 ===")
    print(f"数据库连接测试: {'✓' if connection_ok else '✗'}")
    print(f"需求检查测试: {'✓' if requirements_ok else '✗'}")
    print(f"规则导入查询测试: {'✓' if import_query_ok else '✗'}")
    print(f"规则导入执行测试: {'✓' if import_execute_ok else '✗'}")
    
    if all([connection_ok, requirements_ok, import_query_ok, import_execute_ok]):
        print("\n所有测试通过！新功能已成功实现。")
    else:
        print("\n部分测试失败，请检查功能实现。")

if __name__ == "__main__":
    main() 