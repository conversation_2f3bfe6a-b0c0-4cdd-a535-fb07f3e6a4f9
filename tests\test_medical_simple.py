#!/usr/bin/env python3
"""
简化的医保项目检索测试
"""
import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_oracle_search():
    """测试Oracle医保项目搜索"""
    print("测试Oracle医保项目搜索...")
    
    # 测试多个搜索词
    search_terms = ["P", "检查", "CT", "血常规", "心电图"]
    
    for term in search_terms:
        print(f"\n搜索词: '{term}'")
        test_data = {
            "search_term": term,
            "database": "oracle",
            "host": "default",
            "schema": "",
            "limit": 10
        }
    
        try:
            response = requests.post(
                f"{BASE_URL}/api/medical-insurance/search",
                headers={"Content-Type": "application/json"},
                json=test_data,
                timeout=15
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"成功: {result.get('success', False)}")
                
                if result.get('success'):
                    data = result.get('data', [])
                    total = result.get('total', 0)
                    note = result.get('note', '')
                    
                    print(f"找到项目数: {total}")
                    if note:
                        print(f"备注: {note}")
                    
                    if data:
                        print("搜索结果:")
                        for i, item in enumerate(data, 1):
                            print(f"  {i}. {item.get('医保项目编码', 'N/A')} - {item.get('医保项目名称', 'N/A')}")
                    else:
                        print("未找到匹配的项目")
                else:
                    print(f"错误: {result.get('error', '未知错误')}")
            else:
                print(f"HTTP错误: {response.status_code}")
                print(f"响应: {response.text}")
                
        except Exception as e:
            print(f"测试失败: {str(e)}")

def test_empty_search():
    """测试空搜索词"""
    print("\n测试空搜索词...")
    
    test_data = {
        "search_term": "",
        "database": "oracle",
        "host": "default",
        "schema": "",
        "limit": 10
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/medical-insurance/search",
            headers={"Content-Type": "application/json"},
            json=test_data,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 400:
            result = response.json()
            print(f"预期错误: {result.get('error', '未知错误')}")
        else:
            print(f"意外状态码: {response.status_code}")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")

def main():
    """主函数"""
    print("简化医保项目检索测试")
    print("=" * 40)
    
    test_oracle_search()
    test_empty_search()
    
    print("\n测试完成")

if __name__ == "__main__":
    main() 