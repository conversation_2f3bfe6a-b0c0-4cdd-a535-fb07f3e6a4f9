# 用户故事地图 (User Story Map) - 临时规则编写工具

## 1. 用户故事地图概述

本用户故事地图通过可视化的方式，展现了“临时规则编写工具”的完整用户旅程。它以用户活动为骨架，将用户任务和具体的用户故事进行分解和组织，并映射到不同的产品发布版本（MVP, v2.0），旨在帮助团队更好地理解用户需求，排定功能优先级，并确保产品迭代的连贯性。

---

## 2. 用户故事地图

| **用户活动 (Activities)** | **管理我的规则** | **创建一条新规则** | **优化与测试规则** | **(未来) 智能创建与部署** |
| :--- | :--- | :--- | :--- | :--- |
| **用户任务 (Tasks)** | **查看规则** | **构思与描述** | **选择模板与配置** | **生成与保存** | **迭代与验证** | **高级管理** | **AI辅助** | **系统集成** |
| **用户故事 (Stories)** | | | | | | | | |
| **MVP (v1.0)** | | | | | | | | |
| | `作为业务员, 我想看到一个包含所有我创建规则的列表, 以便我能统一管理它们。` | `作为业务员, 我想为我的规则起一个明确的名字, 以便我能轻松识别它。` | `作为业务员, 我想从一个预设的模板列表里选择一个, 以便我能快速开始。` | `作为业务员, 我想点击一个按钮就能生成SQL, 以便我能预览它的逻辑。` | `作为业务员, 我想修改已保存规则的参数, 以便根据新情况进行调整。` | | | |
| | `作为业务员, 我想在列表上看到规则的关键信息(如名称、类型), 以便我快速定位。` | `作为业务员, 我想简单描述一下规则的作用和依据, 以便日后回顾和他人理解。` | `作为业务员, 我想在一个表单里填写模板需要的几个关键参数, 比如'项目A'和'数量限制'。` | `作为业务员, 我想保存我创建的规则和生成的SQL, 以便它能被记录下来。` | `作为业务员, 我想删除一个不再需要的规则, 以便保持我的规则库清爽。` | | | |
| | `作为业务员, 我想通过搜索规则名称来快速找到某条规则。` | | `作为业务员, 我想看到每个参数的清晰说明, 以便我能正确填写。` | | | | | |
| **v2.0** | | | | | | | | |
| | `作为业务员, 我想根据规则的类型或状态来筛选列表, 以便我能聚焦于某一类规则。` | | `作为业务员, 我想从更多的模板中进行选择, 以便能覆盖更复杂的业务场景。` | | `作为业务员, 我想复制一条已有的规则, 以便我能在此基础上快速创建一个类似的新规则。` | `作为业务员, 我想标记规则的状态(如'测试中', '已上线'), 以便跟踪它的生命周期。` | `作为业务员, 我想把一批规则导出到Excel, 以便我能进行离线分析或备份。` | | |
| | | | `作为业务员, 我想在填写参数时, 能从一个下拉列表(如城市列表)中选择, 而不是手动输入。` | | | `作为业务员, 我想能连接到一个测试数据库, 来验证我生成的SQL是否能跑通、结果是否准确。` | `作为业务员, 我想从一个Excel文件批量导入规则, 以便我能快速初始化我的规则库。` | | |
| **v3.0 (Future)** | | | | | | | | |
| | | `作为业务员, 我只想用自然语言描述我想要的规则, 让系统帮我推荐合适的模板。` | `作为业务员, 我希望系统能根据我选的模板, 智能地为一些参数提供建议值。` | | | | | `作为业务员, 我想把我验证通过的规则一键推送到生产的风控引擎中, 实现快速部署。` |
| | | | | | | | | `作为开发员, 我想通过API来调用和执行规则, 以便将规则能力集成到其他业务系统中。` |

---