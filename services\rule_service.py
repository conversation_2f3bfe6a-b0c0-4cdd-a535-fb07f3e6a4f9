"""
Rule management service for handling rule CRUD operations.
"""
import os
from typing import List, Optional, Dict, Any
from models.rule import RuleInfo, Rule, RuleData
from models.dao import FileSystemDAO
from utils.config import config
from utils.error_handler import BusinessError, ValidationError, SystemError


class RuleManagementService:
    """Service class for managing rules."""
    
    def __init__(self):
        self.output_dir = config.OUTPUT_DIR
        self.dao = FileSystemDAO()
        
        # Valid values for rule attributes
        self.valid_rule_types = [
            "超频次", "重复收费", "限性别", "限年龄", "病例提取", "多项合并", 
            "组套收费", "禁忌药物", "诊断项目不匹配", "超用药金额", "超合理用药疗程", "限医保等级"
        ]
        
        self.valid_patient_types = ["inpatient", "outpatient", "general"]
        self.valid_match_methods = ["name", "code"]
        self.valid_database_types = ["postgresql", "oracle"]
    
    def list_rules(self, search_query: Optional[str] = None, page: int = 1, page_size: int = 20, include_deleted: bool = False) -> dict:
        """List all saved rule files with optional search and pagination."""
        try:
            all_rules = self.dao.list_rule_files()
            
            # Filter out deleted rules unless explicitly requested
            if not include_deleted:
                all_rules = [rule for rule in all_rules if rule.status != 'deleted']
            
            # Apply search filter if provided
            if search_query and search_query.strip():
                search_query = search_query.strip().lower()
                filtered_rules = []
                for rule in all_rules:
                    if (search_query in rule.name.lower() or 
                        (rule.description and search_query in rule.description.lower()) or
                        (rule.category and search_query in rule.category.lower())):
                        filtered_rules.append(rule)
                all_rules = filtered_rules
            
            # Sort by creation time, newest first
            all_rules.sort(key=lambda x: x.created_at, reverse=True)
            
            # Apply pagination
            total_count = len(all_rules)
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            paginated_rules = all_rules[start_index:end_index]
            
            return {
                'rules': paginated_rules,
                'total_count': total_count,
                'page': page,
                'page_size': page_size,
                'total_pages': (total_count + page_size - 1) // page_size,
                'has_next': end_index < total_count,
                'has_prev': page > 1
            }
            
        except Exception as e:
            raise SystemError(f"Failed to list rules: {str(e)}")
    
    def search_rules(self, query: str) -> List[RuleInfo]:
        """Search rules by name, description, or category."""
        if not query or not query.strip():
            return []
        
        try:
            result = self.list_rules(search_query=query, page=1, page_size=1000)
            return result['rules']
        except Exception as e:
            raise SystemError(f"Failed to search rules: {str(e)}")
    
    def get_rule(self, rule_name: str) -> Rule:
        """Get a specific rule by name."""
        if not rule_name or not rule_name.strip():
            raise ValidationError("Rule name is required")
        
        try:
            return self.dao.read_rule_file(rule_name)
        except Exception as e:
            raise SystemError(f"Failed to read rule '{rule_name}': {str(e)}")
    
    def save_rule(self, rule_data: RuleData, allow_overwrite: bool = False) -> dict:
        """Save a new rule or update an existing one with conflict detection."""
        if not rule_data.validate():
            raise ValidationError(
                "Rule name and content are required",
                error_code="INVALID_RULE_DATA",
                details={
                    "name": rule_data.name,
                    "has_content": bool(rule_data.content and rule_data.content.strip())
                }
            )
        
        # Check for rule name conflicts
        rule_exists = self.dao.rule_file_exists(rule_data.name)
        
        if rule_exists and not allow_overwrite:
            raise BusinessError(
                f"Rule '{rule_data.name}' already exists",
                error_code="RULE_NAME_CONFLICT",
                details={
                    "rule_name": rule_data.name,
                    "suggestion": "Use a different name or set allow_overwrite=True to update the existing rule"
                }
            )
        
        try:
            is_new = self.dao.write_rule_file(rule_data)
            
            return {
                "is_new": is_new,
                "rule_name": rule_data.name,
                "action": "created" if is_new else "updated"
            }
        except Exception as e:
            raise SystemError(f"Failed to save rule '{rule_data.name}': {str(e)}")
    
    def delete_rule(self, rule_name: str, logical_delete: bool = True) -> dict:
        """Delete a rule by name (logical or physical deletion)."""
        if not rule_name or not rule_name.strip():
            raise ValidationError("Rule name is required")
        
        if not self.rule_exists(rule_name):
            raise BusinessError(
                f"Rule '{rule_name}' not found",
                error_code="RULE_NOT_FOUND",
                details={"rule_name": rule_name}
            )
        
        try:
            if logical_delete:
                # Perform logical deletion by updating status
                metadata = self.dao.get_rule_metadata(rule_name)
                metadata['status'] = 'deleted'
                self.dao.update_rule_metadata(rule_name, metadata)
                
                return {
                    "rule_name": rule_name,
                    "action": "logically_deleted",
                    "message": f"Rule '{rule_name}' marked as deleted"
                }
            else:
                # Perform physical deletion
                self.dao.delete_rule_file(rule_name)
                
                return {
                    "rule_name": rule_name,
                    "action": "physically_deleted",
                    "message": f"Rule '{rule_name}' permanently deleted"
                }
        except Exception as e:
            raise SystemError(f"Failed to delete rule '{rule_name}': {str(e)}")
    
    def restore_rule(self, rule_name: str) -> dict:
        """Restore a logically deleted rule."""
        if not rule_name or not rule_name.strip():
            raise ValidationError("Rule name is required")
        
        if not self.rule_exists(rule_name):
            raise BusinessError(
                f"Rule '{rule_name}' not found",
                error_code="RULE_NOT_FOUND",
                details={"rule_name": rule_name}
            )
        
        try:
            metadata = self.dao.get_rule_metadata(rule_name)
            
            if metadata.get('status') != 'deleted':
                raise BusinessError(
                    f"Rule '{rule_name}' is not deleted and cannot be restored",
                    error_code="RULE_NOT_DELETED",
                    details={"rule_name": rule_name, "current_status": metadata.get('status', 'active')}
                )
            
            metadata['status'] = 'active'
            self.dao.update_rule_metadata(rule_name, metadata)
            
            return {
                "rule_name": rule_name,
                "action": "restored",
                "message": f"Rule '{rule_name}' restored successfully"
            }
        except Exception as e:
            raise SystemError(f"Failed to restore rule '{rule_name}': {str(e)}")
    
    def create_rule(self, rule_data: RuleData) -> dict:
        """Create a new rule with conflict detection."""
        if not rule_data.validate():
            raise ValidationError(
                "Rule name and content are required",
                error_code="INVALID_RULE_DATA",
                details={
                    "name": rule_data.name,
                    "has_content": bool(rule_data.content and rule_data.content.strip())
                }
            )
        
        # Check for rule name conflicts (only consider active rules)
        if self.rule_exists(rule_data.name):
            raise BusinessError(
                f"Rule '{rule_data.name}' already exists",
                error_code="RULE_NAME_CONFLICT",
                details={
                    "rule_name": rule_data.name,
                    "suggestion": "Use a different name or use the update endpoint to modify the existing rule"
                }
            )
        
        try:
            self.dao.write_rule_file(rule_data)
            
            return {
                "rule_name": rule_data.name,
                "action": "created",
                "message": f"Rule '{rule_data.name}' created successfully"
            }
        except Exception as e:
            raise SystemError(f"Failed to create rule '{rule_data.name}': {str(e)}")
    
    def update_rule(self, rule_name: str, rule_data: RuleData) -> dict:
        """Update an existing rule."""
        # First check if the rule exists
        if not self.rule_exists(rule_name):
            raise BusinessError(
                f"Rule '{rule_name}' not found",
                error_code="RULE_NOT_FOUND",
                details={"rule_name": rule_name}
            )
        
        # If the name is changing, we need to handle it as delete + create
        if rule_name != rule_data.name:
            # Check if the new name conflicts with another rule
            if self.dao.rule_file_exists(rule_data.name):
                raise BusinessError(
                    f"Cannot rename rule to '{rule_data.name}' - name already exists",
                    error_code="RULE_NAME_CONFLICT",
                    details={
                        "old_name": rule_name,
                        "new_name": rule_data.name,
                        "suggestion": "Choose a different name for the rule"
                    }
                )
            
            # Delete the old rule and create with new name
            self.delete_rule(rule_name)
            self.dao.write_rule_file(rule_data)
            
            return {
                "old_name": rule_name,
                "new_name": rule_data.name,
                "action": "renamed_and_updated",
                "message": f"Rule renamed from '{rule_name}' to '{rule_data.name}' and updated successfully"
            }
        else:
            # Update existing rule with same name
            self.dao.write_rule_file(rule_data)
            
            return {
                "rule_name": rule_data.name,
                "action": "updated",
                "message": f"Rule '{rule_data.name}' updated successfully"
            }
    
    def rule_exists(self, rule_name: str) -> bool:
        """Check if a rule exists and is not deleted."""
        if not rule_name:
            return False
        
        # Check if file exists
        if not self.dao.rule_file_exists(rule_name):
            return False
        
        # Check if rule is not logically deleted
        try:
            metadata = self.dao.get_rule_metadata(rule_name)
            return metadata.get('status', 'active') != 'deleted'
        except Exception:
            # If metadata can't be loaded, assume rule exists
            return True
    
    def validate_rule_attributes(self, rule_data: RuleData) -> List[str]:
        """
        Validate rule attributes and return list of validation errors.
        
        Args:
            rule_data: Rule data to validate
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Validate rule type
        if rule_data.rule_type and rule_data.rule_type not in self.valid_rule_types:
            errors.append(f"Invalid rule type '{rule_data.rule_type}'. Valid types: {', '.join(self.valid_rule_types)}")
        
        # Validate patient type
        if rule_data.patient_type and rule_data.patient_type not in self.valid_patient_types:
            errors.append(f"Invalid patient type '{rule_data.patient_type}'. Valid types: {', '.join(self.valid_patient_types)}")
        
        # Validate match method
        if rule_data.match_method and rule_data.match_method not in self.valid_match_methods:
            errors.append(f"Invalid match method '{rule_data.match_method}'. Valid methods: {', '.join(self.valid_match_methods)}")
        
        # Validate database type
        if rule_data.database_type and rule_data.database_type not in self.valid_database_types:
            errors.append(f"Invalid database type '{rule_data.database_type}'. Valid types: {', '.join(self.valid_database_types)}")
        
        # Validate template parameters if provided
        if rule_data.parameters:
            param_errors = self._validate_template_parameters(rule_data.parameters)
            errors.extend(param_errors)
        
        return errors
    
    def _validate_template_parameters(self, parameters: Dict[str, Any]) -> List[str]:
        """Validate template parameters."""
        errors = []
        
        if not isinstance(parameters, dict):
            errors.append("Template parameters must be a dictionary")
            return errors
        
        # Check for empty parameter values
        for param_name, param_value in parameters.items():
            if param_value is None or (isinstance(param_value, str) and not param_value.strip()):
                errors.append(f"Parameter '{param_name}' cannot be empty")
        
        return errors
    
    def save_rule_with_validation(self, rule_data: RuleData, allow_overwrite: bool = False) -> dict:
        """
        Save a rule with comprehensive validation including attribute validation.
        
        Args:
            rule_data: Rule data to save
            allow_overwrite: Whether to allow overwriting existing rules
            
        Returns:
            Dictionary with save result information
        """
        # Basic validation
        if not rule_data.validate():
            raise ValidationError(
                "Rule name and content are required",
                error_code="INVALID_RULE_DATA",
                details={
                    "name": rule_data.name,
                    "has_content": bool(rule_data.content and rule_data.content.strip())
                }
            )
        
        # Attribute validation
        attribute_errors = self.validate_rule_attributes(rule_data)
        if attribute_errors:
            raise ValidationError(
                "Rule attribute validation failed",
                error_code="INVALID_RULE_ATTRIBUTES",
                details={"errors": attribute_errors}
            )
        
        # Proceed with normal save logic
        return self.save_rule(rule_data, allow_overwrite)
    
    def create_rule_with_validation(self, rule_data: RuleData) -> dict:
        """
        Create a new rule with comprehensive validation.
        
        Args:
            rule_data: Rule data to create
            
        Returns:
            Dictionary with creation result information
        """
        # Basic validation
        if not rule_data.validate():
            raise ValidationError(
                "Rule name and content are required",
                error_code="INVALID_RULE_DATA",
                details={
                    "name": rule_data.name,
                    "has_content": bool(rule_data.content and rule_data.content.strip())
                }
            )
        
        # Attribute validation
        attribute_errors = self.validate_rule_attributes(rule_data)
        if attribute_errors:
            raise ValidationError(
                "Rule attribute validation failed",
                error_code="INVALID_RULE_ATTRIBUTES",
                details={"errors": attribute_errors}
            )
        
        # Proceed with normal create logic
        return self.create_rule(rule_data)
    
    def update_rule_with_validation(self, rule_name: str, rule_data: RuleData) -> dict:
        """
        Update an existing rule with comprehensive validation.
        
        Args:
            rule_name: Name of the rule to update
            rule_data: New rule data
            
        Returns:
            Dictionary with update result information
        """
        # Basic validation
        if not rule_data.validate():
            raise ValidationError(
                "Rule name and content are required",
                error_code="INVALID_RULE_DATA",
                details={
                    "name": rule_data.name,
                    "has_content": bool(rule_data.content and rule_data.content.strip())
                }
            )
        
        # Attribute validation
        attribute_errors = self.validate_rule_attributes(rule_data)
        if attribute_errors:
            raise ValidationError(
                "Rule attribute validation failed",
                error_code="INVALID_RULE_ATTRIBUTES",
                details={"errors": attribute_errors}
            )
        
        # Proceed with normal update logic
        return self.update_rule(rule_name, rule_data)
    
    def get_rule_attribute_options(self) -> Dict[str, List[Any]]:
        """
        Get all available options for rule attributes.
        
        Returns:
            Dictionary containing all valid attribute options
        """
        return {
            "rule_types": [
                {"value": rt, "label": rt} for rt in self.valid_rule_types
            ],
            "patient_types": [
                {"value": "inpatient", "label": "住院"},
                {"value": "outpatient", "label": "门诊"},
                {"value": "general", "label": "通用"}
            ],
            "match_methods": [
                {"value": "name", "label": "按名称"},
                {"value": "code", "label": "按编码"}
            ],
            "database_types": [
                {"value": "postgresql", "label": "PostgreSQL"},
                {"value": "oracle", "label": "Oracle"}
            ]
        }
    
    def analyze_rule_name_for_attributes(self, rule_name: str) -> Dict[str, Any]:
        """
        Analyze rule name and suggest appropriate attributes.
        
        Args:
            rule_name: Name of the rule to analyze
            
        Returns:
            Dictionary with suggested attributes
        """
        from services.intelligent_template_service import IntelligentTemplateService
        
        intelligent_service = IntelligentTemplateService()
        return intelligent_service.analyze_rule_attributes(rule_name)
    
    def get_rules_by_attributes(self, rule_type: Optional[str] = None,
                              patient_type: Optional[str] = None,
                              match_method: Optional[str] = None,
                              database_type: Optional[str] = None) -> List[RuleInfo]:
        """
        Get rules filtered by attributes.
        
        Args:
            rule_type: Filter by rule type
            patient_type: Filter by patient type
            match_method: Filter by match method
            database_type: Filter by database type
            
        Returns:
            List of filtered rules
        """
        try:
            all_rules = self.dao.list_rule_files()
            filtered_rules = []
            
            for rule_info in all_rules:
                # Skip deleted rules
                if rule_info.status == 'deleted':
                    continue
                
                # Get full rule data to check attributes
                try:
                    rule = self.dao.read_rule_file(rule_info.name)
                    
                    # Apply filters
                    if rule_type and rule.rule_type != rule_type:
                        continue
                    if patient_type and rule.patient_type != patient_type:
                        continue
                    if match_method and rule.match_method != match_method:
                        continue
                    if database_type and rule.database_type != database_type:
                        continue
                    
                    filtered_rules.append(rule_info)
                    
                except Exception:
                    # Skip rules that can't be read
                    continue
            
            return filtered_rules
            
        except Exception as e:
            raise SystemError(f"Failed to filter rules by attributes: {str(e)}")
    
    def get_rule_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about rules and their attributes.
        
        Returns:
            Dictionary with rule statistics
        """
        try:
            all_rules = self.dao.list_rule_files()
            active_rules = [r for r in all_rules if r.status != 'deleted']
            
            # Count by rule type
            rule_type_counts = {}
            patient_type_counts = {}
            database_type_counts = {}
            
            for rule_info in active_rules:
                try:
                    rule = self.dao.read_rule_file(rule_info.name)
                    
                    # Count rule types
                    if rule.rule_type:
                        rule_type_counts[rule.rule_type] = rule_type_counts.get(rule.rule_type, 0) + 1
                    
                    # Count patient types
                    if rule.patient_type:
                        patient_type_counts[rule.patient_type] = patient_type_counts.get(rule.patient_type, 0) + 1
                    
                    # Count database types
                    if rule.database_type:
                        database_type_counts[rule.database_type] = database_type_counts.get(rule.database_type, 0) + 1
                        
                except Exception:
                    # Skip rules that can't be read
                    continue
            
            return {
                "total_rules": len(all_rules),
                "active_rules": len(active_rules),
                "deleted_rules": len(all_rules) - len(active_rules),
                "rule_type_distribution": rule_type_counts,
                "patient_type_distribution": patient_type_counts,
                "database_type_distribution": database_type_counts
            }
            
        except Exception as e:
            raise SystemError(f"Failed to get rule statistics: {str(e)}")
    
    def get_rule_sql_content(self, rule_id: str) -> Optional[str]:
        """Get SQL content for a specific rule."""
        try:
            # 通过规则ID获取规则信息
            rule = self.dao.read_rule_file(rule_id)
            if rule and hasattr(rule, 'content'):
                return rule.content
            return None
        except Exception as e:
            # 如果通过ID读取失败，尝试通过名称读取
            try:
                rule = self.dao.read_rule_file(rule_id + '.sql')
                if rule and hasattr(rule, 'content'):
                    return rule.content
            except:
                pass
            raise SystemError(f"Failed to get SQL content for rule '{rule_id}': {str(e)}")
    
    def sync_rule_sql_to_database(self, rule_id: str, connection_id: str) -> Dict[str, Any]:
        """Sync SQL content to target database."""
        try:
            print(f"开始同步SQL，规则ID: {rule_id}, 连接ID: {connection_id}")
            
            # 获取规则的SQL内容
            sql_content = self.get_rule_sql_content(rule_id)
            if not sql_content:
                raise BusinessError(f"未找到规则 '{rule_id}' 的SQL内容")
            
            print(f"成功获取SQL内容，长度: {len(sql_content)}")
            
            # 获取数据库连接信息
            from controllers.database_controller import get_database_connection_by_id
            connection_info = get_database_connection_by_id(connection_id)
            if not connection_info:
                raise BusinessError(f"未找到数据库连接 '{connection_id}'")
            
            print(f"成功获取数据库连接信息: {connection_info.get('name')}")
            
            # 执行SQL更新
            result = self._update_kj_rule_table(connection_info, rule_id, sql_content)
            
            return {
                'success': True,
                'message': f'SQL已成功同步到数据库 {connection_info.get("name", "未知")}',
                'details': {
                    'rule_id': rule_id,
                    'connection_id': connection_id,
                    'connection_name': connection_info.get('name'),
                    'updated_rows': result.get('affected_rows', 0)
                }
            }
            
        except BusinessError as e:
            print(f"业务错误: {str(e)}")
            raise e
        except Exception as e:
            print(f"系统错误: {str(e)}")
            raise SystemError(f"同步SQL失败: {str(e)}")
    
    def _update_kj_rule_table(self, connection_info: Dict[str, Any], rule_id: str, sql_content: str) -> Dict[str, Any]:
        """Update kj_rule table in target database."""
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor
            
            # 连接数据库
            conn = psycopg2.connect(
                host=connection_info['host'],
                port=connection_info['port'],
                database=connection_info['database'],
                user=connection_info['username'],
                password=connection_info['password']
            )
            
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 更新kj_rule表的sql_name字段
                update_query = """
                    UPDATE kj_rule 
                    SET sql_name = %s, 
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """
                
                cursor.execute(update_query, (sql_content, rule_id))
                
                # 检查是否有行被更新
                if cursor.rowcount == 0:
                    # 如果没有找到匹配的记录，尝试插入新记录
                    insert_query = """
                        INSERT INTO kj_rule (id, sql_name, created_at, updated_at)
                        VALUES (%s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                        ON CONFLICT (id) DO UPDATE SET
                            sql_name = EXCLUDED.sql_name,
                            updated_at = CURRENT_TIMESTAMP
                    """
                    
                    cursor.execute(insert_query, (rule_id, sql_content))
                
                conn.commit()
                
                return {
                    'affected_rows': cursor.rowcount,
                    'message': 'SQL同步成功'
                }
                
        except psycopg2.Error as e:
            raise BusinessError(f"数据库操作失败: {str(e)}")
        except Exception as e:
            raise SystemError(f"更新kj_rule表失败: {str(e)}")
        finally:
            if 'conn' in locals():
                conn.close()