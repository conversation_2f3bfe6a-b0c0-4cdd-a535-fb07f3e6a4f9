# 系统架构设计文档 - 临时规则编写工具

## 1. 文档信息

### 1.1 版本历史

| 版本 | 日期       | 作者 | 变更描述         |
| ---- | ---------- | ---- | ---------------- |
| 1.0  | 2023-10-27 | AI架构师 | 初始版本创建     |

### 1.2 文档目的

本文档旨在为“临时规则编写工具”提供一个清晰、全面且可执行的技术架构蓝图。它将详细描述系统的架构风格、核心组件、技术选型、数据管理方案以及非功能性需求的实现策略，作为后续详细设计、开发、测试和运维工作的核心指导依据。

### 1.3 相关文档引用

- [产品需求文档 (PRD)](./PRD.md)
- [产品路线图 (Roadmap)](./Roadmap.md)
- [用户故事地图 (User Story Map)](./User_Story_Map.md)

## 2. 系统概述

### 2.1 系统愿景与目标

**愿景**: 打造一个高效、智能、易用的医保飞检规则生成与管理平台，显著提升数据分析师的工作效率，确保规则的准确性和一致性。

**目标**:
- **MVP**: 实现核心的规则管理（CRUD）和基于模板的SQL生成功能。
- **中期**: 引入智能化辅助（如AI提取字段）、批量处理和更完善的测试与验证机制。
- **长期**: 发展为覆盖规则全生命周期管理的综合性平台，支持多数据库、多场景的规则适配。

### 2.2 业务领域与核心功能

本系统服务于医保风控领域的数据分析师，核心是围绕“飞检规则”的生命周期管理。从架构师视角看，核心业务可抽象为：

1.  **规则知识库管理**: 对结构化的规则信息进行统一存储和维护，是系统的核心资产。
2.  **规则实例化**: 基于知识库中的规则定义和动态参数，结合预设的SQL模板，生成可在特定数据库（Oracle, PostgreSQL）上执行的SQL查询脚本。
3.  **数据集成与交互**: 与多个外部数据源（如医保三目表）进行交互，以获取规则生成所需的上下文信息。

### 2.3 非功能需求详述

- **性能**: 规则查询响应时间 < 2秒；SQL生成时间 < 1秒；批量导入（100条规则） < 30秒。
- **可用性**: 系统核心功能可用性 > 99.9%。作为内部工具，允许在非工作时间进行计划内停机维护。
- **安全性**: 用户访问需进行身份认证。数据库凭证等敏感信息需加密存储和管理。防止常见的Web攻击（SQL注入、XSS等）。
- **可伸缩性**: 初期部署为单体应用，但架构设计应具备良好的模块化，以便未来在用户量或规则复杂度显著增加时，能够平滑地将关键模块（如SQL生成服务）拆分为独立微服务。
- **可维护性**: 代码结构清晰，遵循统一规范。关键业务逻辑有充足的注释和文档。提供完善的日志记录和监控告警机制。
- **成本考量**: 优先使用成熟的开源技术（Flask, PostgreSQL），以控制软件许可成本。初期采用单服务器部署，降低基础设施和运维成本。

### 2.4 项目约束与假设

- **技术栈约束**: 后端强制使用 **Flask** 框架。
- **数据库环境**: 系统需要同时支持 **Oracle** 和 **PostgreSQL** 两种数据库。
- **团队技能**: 团队熟悉 Python 和 Flask 开发，具备SQL优化能力。
- **基础设施**: 假设部署环境为标准的Linux服务器，具备Python运行环境和网络连接。
- **数据源**: 假设“医保三目表”等依赖的数据源是稳定且可访问的。

## 3. 架构风格与原则

### 3.1 选择的架构风格

选择 **分层单体架构 (Layered Monolithic Architecture)**。

**选择理由**:
1.  **业务规模**: 当前系统业务边界清晰，功能集中，单体架构足以应对，开发和部署效率最高。
2.  **团队熟悉度**: 对于中小型项目和团队，单体架构更易于理解、开发和维护。
3.  **演进能力**: 采用严格的内部分层设计，将表现层、业务逻辑层和数据访问层清晰分离。这种高内聚、低耦合的设计使得未来在必要时，可以平滑地将某个业务层模块重构并拆分为微服务。
4.  **成本效益**: 避免了微服务架构带来的额外复杂性，如服务治理、分布式事务、部署运维等，在项目初期能实现快速交付和迭代。

### 3.2 遵循的核心架构原则

- **高内聚，低耦合**: 模块内部功能紧密相关，模块之间依赖关系最少。
- **关注点分离 (SoC)**: 不同层级的代码只负责自己的核心职责（如Flask路由负责请求分发，服务层负责业务逻辑，数据访问层负责数据库交互）。
- **契约优先**: 服务之间的交互通过明确的API接口定义，降低耦合度。
- **弹性设计**: 关键外部依赖（如数据库、AI服务）的调用需要有超时、重试和熔断机制，保证系统的健壮性。

## 4. 架构视图

### 4.1 逻辑视图

#### 系统功能分解与模块划分

```mermaid
componentDiagram
    title 系统逻辑组件图

    package "用户界面 (Frontend)" {
        [规则管理页面] as RuleMgmtUI
        [SQL生成器页面] as SqlGenUI
        [数据导入页面] as ImportUI
    }

    package "应用层 (Application Layer)" {
        [API Endpoints] as API
        [Web Pages] as Web
    }

    package "业务逻辑层 (Business Logic Layer)" {
        [规则管理服务] as RuleService
        [SQL生成服务] as SqlGenService
        [数据导入服务] as ImportService
        [外部服务集成] as ExternalService
    }

    package "数据访问层 (Data Access Layer)" {
        [规则知识库DAO] as RuleDAO
        [医保编码DAO] as MedCodeDAO
        [数据库连接池] as DBPool
    }

    package "基础设施 (Infrastructure)" {
        [数据库 (Oracle/PG)] as DB
        [文件系统] as FS
        [Gemini API] as AI
    }

    RuleMgmtUI --> API
    SqlGenUI --> API
    ImportUI --> API
    Web --> RuleService

    API --> RuleService
    API --> SqlGenService
    API --> ImportService

    RuleService --> RuleDAO
    SqlGenService --> RuleDAO
    SqlGenService --> MedCodeDAO
    ImportService --> RuleService
    ImportService --> FS
    ExternalService --> AI
    RuleService --> ExternalService

    RuleDAO --> DBPool
    MedCodeDAO --> DBPool
    DBPool --> DB
```

### 4.2 进程视图

#### 关键流程：生成单条规则SQL

```mermaid
sequenceDiagram
    title 生成SQL流程时序图
    actor User as 用户
    participant UI as 前端页面
    participant App as Flask应用 (API层)
    participant SqlGenService as SQL生成服务
    participant RuleDAO as 规则DAO
    participant TemplateEngine as 模板引擎
    participant DB as 数据库

    User->>UI: 在SQL生成器页面选择规则并点击“生成”
    UI->>App: 发起 /api/rules/generate-sql 请求 (含rule_id)
    App->>SqlGenService: 调用 generate_sql(rule_id)
    SqlGenService->>RuleDAO: 调用 find_by_id(rule_id) 获取规则详情
    RuleDAO->>DB: 执行 SELECT * FROM rule_table WHERE id = ?
    DB-->>RuleDAO: 返回规则数据
    RuleDAO-->>SqlGenService: 返回规则对象
    SqlGenService->>TemplateEngine: 根据规则类型加载对应SQL模板
    TemplateEngine-->>SqlGenService: 返回模板内容
    SqlGenService->>SqlGenService: 将规则数据填充到模板中
    SqlGenService-->>App: 返回生成的SQL字符串
    App-->>UI: 返回 { "sql": "SELECT ..." }
    UI->>User: 在页面上展示生成的SQL
```

### 4.3 部署视图

系统初期采用简单的单服务器部署模式。

```mermaid
deploymentDiagram
    title 系统部署图 (MVP阶段)
    node "Web Server (e.g., Nginx)" {
        artifact "Static Files" as Static
        node "Application Server (Gunicorn + Flask)" {
            artifact "App.py" as App
        }
    }

    node "Database Server" {
        database "Oracle DB" as Oracle
        database "PostgreSQL DB" as PG
    }

    node "External Services" {
        node "Google Cloud" {
            artifact "Gemini API" as Gemini
        }
    }

    App -->> Oracle: (JDBC/ODBC)
    App -->> PG: (JDBC/ODBC)
    App -->> Gemini: (HTTPS/API)
    User -->> "Web Server (e.g., Nginx)": (HTTP/HTTPS)
    "Web Server (e.g., Nginx)" -->> App: (Proxy Pass)
```

### 4.4 开发视图

推荐的代码组织结构，促进模块化和可维护性。

```
/project-root
├── app.py                 # Flask应用入口, 路由定义, 全局配置
├── services/              # 业务逻辑层
│   ├── rule_service.py
│   ├── sql_generator_service.py
│   └── import_service.py
├── models/                # 数据访问层 (DAO) 和数据模型
│   ├── __init__.py
│   ├── base_dao.py
│   └── rule_dao.py
├── utils/                 # 通用工具模块
│   ├── db_utils.py        # 数据库连接池管理
│   └── decorators.py      # 自定义装饰器 (如错误处理)
├── static/                # 静态文件 (CSS, JS, Images)
│   ├── css/
│   └── js/
├── templates/             # Flask/Jinja2 页面模板
│   ├── rule_management.html
│   └── sql_generator.html
├── sql_templates/         # SQL模板文件
│   ├── oracle/
│   └── postgresql/
├── tests/                 # 单元测试和集成测试
│   ├── test_rule_service.py
│   └── ...
├── config.py              # 配置文件 (数据库连接, API Keys)
└── requirements.txt       # Python依赖
```

**技术栈选择**:

- **后端框架**: **Flask** (已指定)。轻量、灵活，适合快速开发。
- **Web服务器**: **Gunicorn**。成熟的Python WSGI服务器，用于生产环境部署。
- **反向代理**: **Nginx**。处理静态文件、负载均衡和HTTPS终止，性能卓越。
- **数据库驱动**: `cx_Oracle` for Oracle, `psycopg2-binary` for PostgreSQL。
- **前端**: 原生 **HTML/CSS/JavaScript** 配合 **jQuery** 和 **Bootstrap**，满足内部工具的快速开发需求。
- **数据处理**: **Pandas**。用于处理Excel文件的导入导出，功能强大。

## 5. 关键模块设计

### 5.1 规则管理服务 (`RuleService`)

- **职责**: 负责规则的增、删、改、查核心逻辑，以及与规则相关的业务验证。
- **接口规范**:
  - `get_rule(rule_id)`: 获取单个规则详情。
  - `get_rules_paginated(page, per_page, filters)`: 分页和筛选查询规则列表。
  - `create_rule(data)`: 创建新规则，包含数据校验、默认值处理。
  - `update_rule(rule_id, data)`: 更新现有规则。
  - `delete_rule(rule_id)`: 删除规则。
- **数据模型**: 对应数据库中的“飞检规则知识库”和“规则医保编码对照”表。

### 5.2 SQL生成服务 (`SqlGenService`)

- **职责**: 根据规则ID和目标数据库类型，加载相应的SQL模板，并将规则数据渲染成最终可执行的SQL语句。
- **内部架构**: 使用 **Jinja2** 作为模板引擎，利用其强大的模板填充能力。
- **接口规范**:
  - `generate_sql(rule_id, db_type='postgresql')`: 生成SQL。
  - `get_template_list()`: 获取可用的SQL模板列表。
- **数据模型**: 输入为规则对象，输出为SQL字符串。

## 6. 横向技术方案

- **身份认证与授权**: 初期简化处理，可通过Nginx配置HTTP Basic Auth或部署在内部网络中实现访问控制。未来可集成LDAP或OAuth2。
- **日志记录**: 使用Flask标准的`logging`模块，配置`RotatingFileHandler`进行日志滚动。日志级别分为DEBUG, INFO, WARNING, ERROR。关键操作需记录详细的上下文信息。
- **监控与告警**: 部署一个简单的Flask中间件，记录每个请求的响应时间。使用`psutil`监控服务器的CPU和内存。当错误率或响应时间超过阈值时，通过邮件发送告警。
- **配置管理**: 创建`config.py`文件，通过环境变量区分开发、测试和生产环境的配置（如数据库连接字符串、密钥等）。严禁将敏感信息硬编码在代码中。

## 7. 数据存储与管理方案

- **数据库选型**: 已确定为 **Oracle** 和 **PostgreSQL**。
- **数据同步与一致性**: 系统主要为读多写少。写操作（创建/更新规则）通过API完成，在业务层通过事务保证单次操作的原子性。目前不涉及复杂的分布式事务。
- **数据备份与恢复**: 依赖数据库自身的备份恢复机制。制定定期备份策略（如每日全量备份）。

## 8. 安全架构考量

- **输入验证**: 所有来自客户端的输入都必须在后端进行严格验证，特别是SQL生成服务的参数，防止SQL注入。
- **ORM/参数化查询**: 数据访问层必须使用参数化查询（如`psycopg2`和`cx_Oracle`支持的方式），从根本上杜绝SQL注入风险。
- **输出编码**: 使用Jinja2等模板引擎时，默认开启HTML转义，防止XSS攻击。
- **依赖库安全**: 定期使用`pip-audit`或类似工具扫描`requirements.txt`，检查是否存在已知漏洞的依赖库。

## 9. 高可用与容灾方案

- **单点故障**: 在MVP阶段，应用服务器和数据库都是单点。通过编写高质量代码和充分测试来降低故障率。
- **水平扩展**: 未来可通过在Nginx后部署多个Gunicorn应用实例，并使用负载均衡器分发流量来实现无状态应用层的水平扩展。
- **数据库高可用**: 可配置PostgreSQL的主从复制(Streaming Replication)和Oracle的Data Guard，实现读写分离和故障自动切换。
- **容灾备份**: 关键数据和配置文件需异地备份。

## 10. 架构风险与缓解

| 风险点 | 影响与后果 | 缓解措施 | 备选方案 |
| :--- | :--- | :--- | :--- |
| **SQL模板管理复杂化** | 随着规则类型增多，SQL模板数量和复杂度剧增，难以维护和测试。 | 1. 建立严格的模板命名和目录规范。<br>2. 抽取可复用的SQL片段作为子模板。<br>3. 为每个模板编写单元测试。 | 开发一个可视化的SQL模板编辑器。 |
| **数据库方言兼容性** | Oracle和PostgreSQL的SQL方言差异可能导致模板逻辑复杂，或在一种数据库上运行正常，在另一种上失败。 | 1. 尽可能使用标准SQL。<br>2. 为两种数据库维护独立的、经过充分测试的模板文件。<br>3. 在CI/CD流程中加入针对两种数据库的集成测试。 | 引入一个支持多数据库方言的查询构建器库。 |
| **AI服务依赖** | 智能提取字段功能强依赖外部Gemini API，若API不可用或响应慢，将影响用户体验。 | 1. 对API调用设置合理的超时和重试机制。<br>2. 实现熔断器，在API持续失败时暂时禁用该功能。<br>3. 缓存成功的API调用结果。 | 寻找备用的AI服务提供商。 |

## 11. 架构演进与未来规划

- **短期 (v2.0)**: 引入 **Celery** 和 **Redis** 来处理耗时的异步任务，如批量导入和AI分析，避免阻塞Web请求。增强测试覆盖率，引入CI/CD流水线。
- **中长期 (v3.0)**: 如果SQL生成逻辑变得极其复杂或成为性能瓶颈，可考虑将其拆分为一个独立的 **SQL生成微服务**。引入更完善的用户权限管理体系。