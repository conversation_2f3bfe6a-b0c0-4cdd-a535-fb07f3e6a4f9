"""
Controller for intelligent template selection and recommendation.
"""
from flask import Blueprint, request, jsonify
from typing import Dict, Any, List
from services.intelligent_template_service import IntelligentTemplateService, RuleAttributes
from utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BusinessError


# Create blueprint
intelligent_template_bp = Blueprint('intelligent_template', __name__)

# Initialize service
intelligent_template_service = IntelligentTemplateService()


@intelligent_template_bp.route('/api/templates/recommend', methods=['GET'])
def recommend_templates():
    """
    Recommend templates based on rule attributes.
    
    Query parameters:
    - rule_name: Name of the rule (required)
    - rule_type: Type of rule (optional)
    - patient_type: Patient type - inpatient/outpatient/general (optional)
    - match_method: Match method - name/code (optional)
    - database_type: Database type - postgresql/oracle (optional)
    - max_recommendations: Maximum number of recommendations (optional, default: 10)
    """
    try:
        # Get query parameters
        rule_name = request.args.get('rule_name')
        if not rule_name:
            raise BusinessError("rule_name parameter is required")
        
        rule_type = request.args.get('rule_type')
        patient_type = request.args.get('patient_type')
        match_method = request.args.get('match_method')
        database_type = request.args.get('database_type')
        max_recommendations = int(request.args.get('max_recommendations', 10))
        
        # Create rule attributes
        rule_attributes = RuleAttributes(
            rule_name=rule_name,
            rule_type=rule_type,
            patient_type=patient_type,
            match_method=match_method,
            database_type=database_type
        )
        
        # Get recommendations
        print(f"DEBUG: Requesting recommendations with attributes: {rule_attributes}")
        recommendations = intelligent_template_service.recommend_templates(
            rule_attributes, max_recommendations
        )
        print(f"DEBUG: Found {len(recommendations)} recommendations")
        
        # Format response
        response_data = []
        for rec in recommendations:
            response_data.append({
                "template": {
                    "id": rec.template.id,
                    "description": rec.template.description,
                    "category": rec.template.category,
                    "database_type": rec.template.database_type,
                    "patient_type": rec.template.patient_type,
                    "content": rec.template.sql,  # 添加完整的SQL内容
                    "parameters": [
                        {
                            "name": param.name,
                            "description": param.description,
                            "type": param.parameter_type,
                            "required": param.required
                        } for param in (rec.template.parameters or [])
                    ]
                },
                "score": rec.score,
                "reasons": rec.reasons,
                "matches": {
                    "category_match": rec.category_match,
                    "name_match": rec.name_match,
                    "type_match": rec.type_match
                }
            })
        
        return jsonify({
            "status": "success",
            "data": {
                "recommendations": response_data,
                "total_count": len(response_data),
                "rule_attributes": {
                    "rule_name": rule_attributes.rule_name,
                    "rule_type": rule_attributes.rule_type,
                    "patient_type": rule_attributes.patient_type,
                    "match_method": rule_attributes.match_method,
                    "database_type": rule_attributes.database_type
                }
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_template_bp.route('/api/templates/filter', methods=['GET'])
def filter_templates():
    """
    Filter templates based on multiple criteria.
    
    Query parameters:
    - database_type: Database type - postgresql/oracle (optional)
    - patient_type: Patient type - inpatient/outpatient/general (optional)
    - match_method: Match method - name/code (optional)
    - rule_type: Rule type (optional)
    """
    try:
        # Get query parameters
        database_type = request.args.get('database_type')
        patient_type = request.args.get('patient_type')
        match_method = request.args.get('match_method')
        rule_type = request.args.get('rule_type')
        
        # Get filtered templates
        templates = intelligent_template_service.get_filtered_templates(
            database_type=database_type,
            patient_type=patient_type,
            match_method=match_method,
            rule_type=rule_type
        )
        
        # Format response
        response_data = []
        for template in templates:
            response_data.append({
                "id": template.id,
                "description": template.description,
                "category": template.category,
                "database_type": template.database_type,
                "patient_type": template.patient_type,
                "parameters": [
                    {
                        "name": param.name,
                        "description": param.description,
                        "type": param.parameter_type,
                        "required": param.required
                    } for param in (template.parameters or [])
                ]
            })
        
        return jsonify({
            "status": "success",
            "data": {
                "templates": response_data,
                "total_count": len(response_data),
                "filters": {
                    "database_type": database_type,
                    "patient_type": patient_type,
                    "match_method": match_method,
                    "rule_type": rule_type
                }
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_template_bp.route('/api/rules/analyze-attributes', methods=['POST'])
def analyze_rule_attributes():
    """
    Analyze rule name and detect attributes automatically.
    
    Request body:
    {
        "rule_name": "规则名称"
    }
    """
    try:
        data = request.get_json()
        if not data or 'rule_name' not in data:
            raise BusinessError("rule_name is required in request body")
        
        rule_name = data['rule_name']
        
        # Analyze attributes
        analysis = intelligent_template_service.analyze_rule_attributes(rule_name)
        
        return jsonify({
            "status": "success",
            "data": analysis
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_template_bp.route('/api/templates/recommend-by-name', methods=['GET'])
def recommend_templates_by_name():
    """
    Get template recommendations based only on rule name.
    
    Query parameters:
    - rule_name: Name of the rule (required)
    - max_recommendations: Maximum number of recommendations (optional, default: 5)
    """
    try:
        rule_name = request.args.get('rule_name')
        if not rule_name:
            raise BusinessError("rule_name parameter is required")
        
        max_recommendations = int(request.args.get('max_recommendations', 5))
        
        # Get recommendations
        recommendations = intelligent_template_service.get_template_recommendations_by_name(
            rule_name, max_recommendations
        )
        
        # Format response
        response_data = []
        for rec in recommendations:
            response_data.append({
                "template": {
                    "id": rec.template.id,
                    "description": rec.template.description,
                    "category": rec.template.category,
                    "database_type": rec.template.database_type,
                    "patient_type": rec.template.patient_type
                },
                "score": rec.score,
                "reasons": rec.reasons
            })
        
        return jsonify({
            "status": "success",
            "data": {
                "recommendations": response_data,
                "total_count": len(response_data),
                "rule_name": rule_name
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_template_bp.route('/api/templates/categories', methods=['GET'])
def get_template_categories():
    """Get all available template categories with their attributes."""
    try:
        categories = intelligent_template_service.template_service.get_template_categories()
        
        response_data = []
        for category in categories:
            response_data.append({
                "id": category.id,
                "name": category.name,
                "description": category.description,
                "database_type": category.database_type,
                "patient_type": category.patient_type
            })
        
        return jsonify({
            "status": "success",
            "data": {
                "categories": response_data,
                "total_count": len(response_data)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_template_bp.route('/api/templates/rule-types', methods=['GET'])
def get_rule_types():
    """Get all available rule types for selection."""
    try:
        rule_types = list(intelligent_template_service.rule_type_keywords.keys())
        
        return jsonify({
            "status": "success",
            "data": {
                "rule_types": rule_types,
                "total_count": len(rule_types)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_template_bp.route('/api/templates/patient-types', methods=['GET'])
def get_patient_types():
    """Get all available patient types for selection."""
    try:
        patient_types = [
            {"value": "inpatient", "label": "住院"},
            {"value": "outpatient", "label": "门诊"},
            {"value": "general", "label": "通用"}
        ]
        
        return jsonify({
            "status": "success",
            "data": {
                "patient_types": patient_types,
                "total_count": len(patient_types)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_template_bp.route('/api/templates/match-methods', methods=['GET'])
def get_match_methods():
    """Get all available match methods for selection."""
    try:
        match_methods = [
            {"value": "name", "label": "按名称"},
            {"value": "code", "label": "按编码"}
        ]
        
        return jsonify({
            "status": "success",
            "data": {
                "match_methods": match_methods,
                "total_count": len(match_methods)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_template_bp.route('/api/templates/database-types', methods=['GET'])
def get_database_types():
    """Get all available database types for selection."""
    try:
        database_types = [
            {"value": "postgresql", "label": "PostgreSQL"},
            {"value": "oracle", "label": "Oracle"}
        ]
        
        return jsonify({
            "status": "success",
            "data": {
                "database_types": database_types,
                "total_count": len(database_types)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)
