<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-900 text-gray-300 font-sans p-6">
    <div class="container mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-white">规则管理</h1>
            <div class="flex items-center space-x-4">
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i> 新建规则
                </button>
                <button class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-upload mr-2"></i> 导入
                </button>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-gray-800 p-4 rounded-lg mb-6 flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                    <input type="text" placeholder="搜索规则名称、ID或描述..." class="bg-gray-700 border border-gray-600 rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500 w-80">
                </div>
                <select class="bg-gray-700 border border-gray-600 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option>所有来源</option>
                    <option>手动创建</option>
                    <option>模型生成</option>
                </select>
                <select class="bg-gray-700 border border-gray-600 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option>所有状态</option>
                    <option>已启用</option>
                    <option>已禁用</option>
                </select>
            </div>
            <button class="text-gray-400 hover:text-white">
                <i class="fas fa-filter-circle-xmark mr-1"></i> 清除筛选
            </button>
        </div>

        <!-- Rules Table -->
        <div class="bg-gray-800 rounded-lg overflow-hidden">
            <table class="w-full text-sm text-left text-gray-400">
                <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                    <tr>
                        <th scope="col" class="p-4"><input type="checkbox" class="bg-gray-600 border-gray-500 rounded"></th>
                        <th scope="col" class="px-6 py-3">规则名称</th>
                        <th scope="col" class="px-6 py-3">来源</th>
                        <th scope="col" class="px-6 py-3">状态</th>
                        <th scope="col" class="px-6 py-3">创建日期</th>
                        <th scope="col" class="px-6 py-3">最后修改</th>
                        <th scope="col" class="px-6 py-3 text-center">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Sample Row 1 -->
                    <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                        <td class="p-4"><input type="checkbox" class="bg-gray-600 border-gray-500 rounded"></td>
                        <td class="px-6 py-4 font-medium text-white whitespace-nowrap">限定支付-限单人多次</td>
                        <td class="px-6 py-4">手动创建</td>
                        <td class="px-6 py-4"><span class="bg-green-900 text-green-300 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">已启用</span></td>
                        <td class="px-6 py-4">2023-10-26</td>
                        <td class="px-6 py-4">2024-03-15</td>
                        <td class="px-6 py-4 text-center">
                            <a href="#" class="font-medium text-blue-500 hover:underline mr-4">编辑</a>
                            <a href="#" class="font-medium text-red-500 hover:underline">删除</a>
                        </td>
                    </tr>
                    <!-- Sample Row 2 -->
                    <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                        <td class="p-4"><input type="checkbox" class="bg-gray-600 border-gray-500 rounded"></td>
                        <td class="px-6 py-4 font-medium text-white whitespace-nowrap">项目内涵-重复收费</td>
                        <td class="px-6 py-4">模型生成</td>
                        <td class="px-6 py-4"><span class="bg-green-900 text-green-300 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">已启用</span></td>
                        <td class="px-6 py-4">2023-09-11</td>
                        <td class="px-6 py-4">2024-01-20</td>
                        <td class="px-6 py-4 text-center">
                            <a href="#" class="font-medium text-blue-500 hover:underline mr-4">编辑</a>
                            <a href="#" class="font-medium text-red-500 hover:underline">删除</a>
                        </td>
                    </tr>
                    <!-- Sample Row 3 -->
                    <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                        <td class="p-4"><input type="checkbox" class="bg-gray-600 border-gray-500 rounded"></td>
                        <td class="px-6 py-4 font-medium text-white whitespace-nowrap">分解住院</td>
                        <td class="px-6 py-4">手动创建</td>
                        <td class="px-6 py-4"><span class="bg-red-900 text-red-300 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">已禁用</span></td>
                        <td class="px-6 py-4">2023-05-01</td>
                        <td class="px-6 py-4">2023-05-01</td>
                        <td class="px-6 py-4 text-center">
                            <a href="#" class="font-medium text-blue-500 hover:underline mr-4">编辑</a>
                            <a href="#" class="font-medium text-red-500 hover:underline">删除</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="flex justify-between items-center mt-6">
            <span class="text-sm text-gray-400">显示 <span class="font-semibold text-white">1-10</span> of <span class="font-semibold text-white">100</span></span>
            <div class="inline-flex -space-x-px rounded-md shadow-sm">
                <button class="px-3 py-2 ml-0 leading-tight text-gray-400 bg-gray-800 border border-gray-700 rounded-l-lg hover:bg-gray-700 hover:text-white">上一页</button>
                <button class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white">1</button>
                <button class="px-3 py-2 leading-tight text-gray-400 bg-blue-600 border border-gray-700 text-white">2</button>
                <button class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700 hover:bg-gray-700 hover:text-white">3</button>
                <button class="px-3 py-2 leading-tight text-gray-400 bg-gray-800 border border-gray-700 rounded-r-lg hover:bg-gray-700 hover:text-white">下一页</button>
            </div>
        </div>
    </div>
</body>
</html>