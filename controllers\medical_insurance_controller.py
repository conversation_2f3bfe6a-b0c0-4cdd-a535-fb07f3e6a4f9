"""
医保项目检索控制器
"""
from flask import Blueprint, request, jsonify
import logging
from database_connection import get_oracle_connection, get_postgresql_connection, close_oracle_connection, close_postgresql_connection
from functools import wraps

logger = logging.getLogger(__name__)

medical_insurance_bp = Blueprint('medical_insurance', __name__)

def handle_db_error(f):
    """数据库错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"Medical insurance operation failed: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500
    return decorated_function

@medical_insurance_bp.route('/api/medical-insurance/search', methods=['POST'])
@handle_db_error
def search_medical_insurance_items():
    """搜索医保项目"""
    try:
        data = request.json
        search_term = data.get('search_term', '').strip()
        database_type = data.get('database', 'pg')
        host = data.get('host', 'default')
        schema = data.get('schema', '')
        limit = data.get('limit', 50)
        
        if not search_term:
            return jsonify({'success': False, 'error': '搜索关键词不能为空'}), 400
            
        # 支持多种分隔符的多项目检索
        import re
        search_terms = re.split(r'[,，;；\s\n]+', search_term)
        search_terms = [term.strip() for term in search_terms if term.strip()]
        
        logger.info(f"解析的搜索词: {search_terms}")
        
        if not search_terms:
            return jsonify({'success': False, 'error': '搜索关键词不能为空'}), 400

        # 如果使用默认主机，尝试加载保存的配置
        if host == 'default':
            try:
                from utils.config import config
                saved_config = config.load_database_config()
                
                if database_type.lower() == 'oracle' and saved_config.get('oracle'):
                    # 使用保存的Oracle配置
                    oracle_config = saved_config['oracle']
                    return search_oracle_medical_insurance_with_config(search_terms, oracle_config, schema, limit)
                elif database_type.lower() == 'pg' and saved_config.get('postgresql'):
                    # 使用保存的PostgreSQL配置
                    pg_config = saved_config['postgresql']
                    return search_postgresql_medical_insurance_with_config(search_terms, pg_config, schema, limit)
            except Exception as config_error:
                logger.warning(f"加载保存的数据库配置失败，使用默认配置: {str(config_error)}")

        # 如果无法加载保存的配置或指定了特定主机，使用原有逻辑
        if database_type.lower() == 'oracle':
            return search_oracle_medical_insurance(search_terms, host, schema, limit)
        else:
            return search_postgresql_medical_insurance(search_terms, host, schema, limit)

    except Exception as e:
        logger.error(f"搜索医保项目失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

def search_oracle_medical_insurance_with_config(search_terms, oracle_config, schema, limit):
    """使用保存的配置进行Oracle数据库医保项目搜索"""
    try:
        import oracledb
        
        # 构建DSN
        dsn = f"{oracle_config['host']}:{oracle_config['port']}/{oracle_config['dsn']}"
        
        # 使用保存的配置连接数据库
        with oracledb.connect(
            user=oracle_config['username'],
            password=oracle_config['password'],
            dsn=dsn
        ) as conn:
            with conn.cursor() as cursor:
                if schema:
                    cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")
                
                # 首先检查表是否存在
                cursor.execute("""
                    SELECT COUNT(*) FROM user_tables WHERE table_name = '医保对照表'
                """)
                table_exists = cursor.fetchone()[0] > 0
                
                if not table_exists:
                    # 如果表不存在，返回找不到医保对照表的提示
                    logger.warning("医保对照表不存在")
                    return jsonify({
                        'success': False,
                        'error': '找不到医保对照表',
                        'message': f'在数据库 {oracle_config["host"]} 的 {schema} 模式中找不到医保对照表',
                        'database': 'Oracle',
                        'host': oracle_config['host'],
                        'schema': schema
                    }), 404
                
                # 构建多项目搜索的SQL条件
                if len(search_terms) == 1:
                    # 单个搜索词
                    sql = """
                    SELECT DISTINCT
                        医保项目编码,
                        医保项目名称,
                        医院项目编码,
                        医院项目名称,
                        费用类别
                    FROM 医保对照表
                    WHERE (
                        UPPER(医保项目编码) LIKE UPPER(:search_term) OR
                        UPPER(医保项目名称) LIKE UPPER(:search_term) OR
                        UPPER(医院项目编码) LIKE UPPER(:search_term) OR
                        UPPER(医院项目名称) LIKE UPPER(:search_term)
                    )
                    AND ROWNUM <= :limit
                    ORDER BY 医保项目编码
                    """
                    search_pattern = f'%{search_terms[0]}%'
                    cursor.execute(sql, {'search_term': search_pattern, 'limit': limit})
                else:
                    # 多个搜索词，使用OR条件
                    conditions = []
                    params = {}
                    
                    for i, term in enumerate(search_terms):
                        param_name = f'search_term_{i}'
                        conditions.append(f"""(
                            UPPER(医保项目编码) LIKE UPPER(:{param_name}) OR
                            UPPER(医保项目名称) LIKE UPPER(:{param_name}) OR
                            UPPER(医院项目编码) LIKE UPPER(:{param_name}) OR
                            UPPER(医院项目名称) LIKE UPPER(:{param_name})
                        )""")
                        params[param_name] = f'%{term}%'
                    
                    sql = f"""
                    SELECT DISTINCT
                        医保项目编码,
                        医保项目名称,
                        医院项目编码,
                        医院项目名称,
                        费用类别
                    FROM 医保对照表
                    WHERE ({" OR ".join(conditions)})
                    AND ROWNUM <= :limit
                    ORDER BY 医保项目编码
                    """
                    params['limit'] = limit
                    cursor.execute(sql, params)
                
                rows = cursor.fetchall()
                columns = [col[0] for col in cursor.description]
                
                results = []
                for row in rows:
                    result_dict = {}
                    for i, col in enumerate(columns):
                        result_dict[col] = row[i] if row[i] is not None else ''
                    results.append(result_dict)
                
                return jsonify({
                    'success': True,
                    'data': results,
                    'total': len(results),
                    'database': 'Oracle',
                    'host': oracle_config['host'],
                    'schema': schema,
                    'search_terms': search_terms
                })
                
    except Exception as e:
        logger.error(f"Oracle医保项目搜索失败: {str(e)}")
        return jsonify({'success': False, 'error': f'Oracle医保项目搜索失败: {str(e)}'}), 500

def search_oracle_medical_insurance(search_terms, host, schema, limit):
    """Oracle数据库医保项目搜索"""
    try:
        if host == 'default':
            conn = get_oracle_connection()
            try:
                with conn.cursor() as cursor:
                    if schema:
                        cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")
                    
                    # 首先检查表是否存在
                    cursor.execute("""
                        SELECT COUNT(*) FROM user_tables WHERE table_name = '医保对照表'
                    """)
                    table_exists = cursor.fetchone()[0] > 0
                    
                    if not table_exists:
                        # 如果表不存在，返回找不到医保对照表的提示
                        logger.warning("医保对照表不存在")
                        return jsonify({
                            'success': False,
                            'error': '找不到医保对照表',
                            'message': f'在数据库 {host} 的 {schema} 模式中找不到医保对照表',
                            'database': 'Oracle',
                            'host': host,
                            'schema': schema
                        }), 404
                    
                    # 构建多项目搜索的SQL条件
                    if len(search_terms) == 1:
                        # 单个搜索词
                        sql = """
                        SELECT DISTINCT
                            医保项目编码,
                            医保项目名称,
                            医院项目编码,
                            医院项目名称,
                            费用类别
                        FROM 医保对照表
                        WHERE (
                            UPPER(医保项目编码) LIKE UPPER(:search_term) OR
                            UPPER(医保项目名称) LIKE UPPER(:search_term) OR
                            UPPER(医院项目编码) LIKE UPPER(:search_term) OR
                            UPPER(医院项目名称) LIKE UPPER(:search_term)
                        )
                        AND ROWNUM <= :limit
                        ORDER BY 医保项目编码
                        """
                        search_pattern = f'%{search_terms[0]}%'
                        cursor.execute(sql, {'search_term': search_pattern, 'limit': limit})
                    else:
                        # 多个搜索词，使用OR条件
                        conditions = []
                        params = {}
                        
                        for i, term in enumerate(search_terms):
                            param_name = f'search_term_{i}'
                            conditions.append(f"""(
                                UPPER(医保项目编码) LIKE UPPER(:{param_name}) OR
                                UPPER(医保项目名称) LIKE UPPER(:{param_name}) OR
                                UPPER(医院项目编码) LIKE UPPER(:{param_name}) OR
                                UPPER(医院项目名称) LIKE UPPER(:{param_name})
                            )""")
                            params[param_name] = f'%{term}%'
                        
                        sql = f"""
                        SELECT DISTINCT
                            医保项目编码,
                            医保项目名称,
                            医院项目编码,
                            医院项目名称,
                            费用类别
                        FROM 医保对照表
                        WHERE ({" OR ".join(conditions)})
                        AND ROWNUM <= :limit
                        ORDER BY 医保项目编码
                        """
                        params['limit'] = limit
                        cursor.execute(sql, params)
                    
                    rows = cursor.fetchall()
                    columns = [col[0] for col in cursor.description]
                    
                    results = []
                    for row in rows:
                        result_dict = {}
                        for i, col in enumerate(columns):
                            result_dict[col] = row[i] if row[i] is not None else ''
                        results.append(result_dict)
                    
                    return jsonify({
                        'success': True,
                        'data': results,
                        'total': len(results),
                        'database': 'Oracle',
                        'host': host,
                        'schema': schema
                    })
            finally:
                close_oracle_connection(conn)
        else:
            import oracledb
            dsn = f"{host}:1521/orcl"
            with oracledb.connect(user="datachange", password="drgs2019", dsn=dsn) as conn:
                with conn.cursor() as cursor:
                    if schema:
                        cursor.execute(f"ALTER SESSION SET CURRENT_SCHEMA = {schema}")
                    
                    sql = """
                    SELECT DISTINCT
                        医保项目编码,
                        医保项目名称,
                        医院项目编码,
                        医院项目名称,
                        费用类别
                    FROM 医保对照表
                    WHERE (
                        UPPER(医保项目编码) LIKE UPPER(:search_term) OR
                        UPPER(医保项目名称) LIKE UPPER(:search_term) OR
                        UPPER(医院项目编码) LIKE UPPER(:search_term) OR
                        UPPER(医院项目名称) LIKE UPPER(:search_term)
                    )
                    AND ROWNUM <= :limit
                    ORDER BY 医保项目编码
                    """
                    
                    search_pattern = f'%{search_term}%'
                    cursor.execute(sql, {'search_term': search_pattern, 'limit': limit})
                    
                    rows = cursor.fetchall()
                    columns = [col[0] for col in cursor.description]
                    
                    results = []
                    for row in rows:
                        result_dict = {}
                        for i, col in enumerate(columns):
                            result_dict[col] = row[i] if row[i] is not None else ''
                        results.append(result_dict)
                    
                    return jsonify({
                        'success': True,
                        'data': results,
                        'total': len(results),
                        'database': 'Oracle',
                        'host': host,
                        'schema': schema
                    })
                    
    except Exception as e:
        logger.error(f"Oracle医保项目搜索失败: {str(e)}")
        return jsonify({'success': False, 'error': f'Oracle医保项目搜索失败: {str(e)}'}), 500

def search_postgresql_medical_insurance_with_config(search_terms, pg_config, schema, limit):
    """使用保存的配置进行PostgreSQL数据库医保项目搜索"""
    try:
        import psycopg
        
        # 使用保存的配置连接数据库
        with psycopg.connect(
            host=pg_config['host'],
            port=pg_config['port'],
            dbname=pg_config['database'],
            user=pg_config['username'],
            password=pg_config['password']
        ) as conn:
            with conn.cursor() as cursor:
                if schema:
                    cursor.execute(f'SET search_path TO "{schema}", public')
                
                # 首先检查表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = '医保对照表'
                    )
                """)
                table_exists = cursor.fetchone()[0]
                
                if not table_exists:
                    # 如果表不存在，返回找不到医保对照表的提示
                    logger.warning("医保对照表不存在")
                    return jsonify({
                        'success': False,
                        'error': '找不到医保对照表',
                        'message': f'在数据库 {pg_config["host"]} 的 {schema} 模式中找不到医保对照表',
                        'database': 'PostgreSQL',
                        'host': pg_config['host'],
                        'schema': schema
                    }), 404
                
                # 构建多项目搜索的SQL条件
                if len(search_terms) == 1:
                    # 单个搜索词
                    sql = """
                    SELECT DISTINCT
                        医保项目编码,
                        医保项目名称,
                        医院项目编码,
                        医院项目名称,
                        费用类别
                    FROM 医保对照表
                    WHERE (
                        UPPER(医保项目编码) LIKE UPPER(%s) OR
                        UPPER(医保项目名称) LIKE UPPER(%s) OR
                        UPPER(医院项目编码) LIKE UPPER(%s) OR
                        UPPER(医院项目名称) LIKE UPPER(%s)
                    )
                    ORDER BY 医保项目编码
                    LIMIT %s
                    """
                    search_pattern = f'%{search_terms[0]}%'
                    cursor.execute(sql, (search_pattern, search_pattern, search_pattern, search_pattern, limit))
                else:
                    # 多个搜索词，使用OR条件
                    conditions = []
                    params = []
                    
                    for term in search_terms:
                        search_pattern = f'%{term}%'
                        conditions.append("""(
                            UPPER(医保项目编码) LIKE UPPER(%s) OR
                            UPPER(医保项目名称) LIKE UPPER(%s) OR
                            UPPER(医院项目编码) LIKE UPPER(%s) OR
                            UPPER(医院项目名称) LIKE UPPER(%s)
                        )""")
                        params.extend([search_pattern, search_pattern, search_pattern, search_pattern])
                    
                    sql = f"""
                    SELECT DISTINCT
                        医保项目编码,
                        医保项目名称,
                        医院项目编码,
                        医院项目名称,
                        费用类别
                    FROM 医保对照表
                    WHERE ({" OR ".join(conditions)})
                    ORDER BY 医保项目编码
                    LIMIT %s
                    """
                    params.append(limit)
                    cursor.execute(sql, params)
                
                rows = cursor.fetchall()
                columns = [col[0] for col in cursor.description]
                
                results = []
                for row in rows:
                    result_dict = {}
                    for i, col in enumerate(columns):
                        result_dict[col] = row[i] if row[i] is not None else ''
                    results.append(result_dict)
                
                return jsonify({
                    'success': True,
                    'data': results,
                    'total': len(results),
                    'database': 'PostgreSQL',
                    'host': pg_config['host'],
                    'schema': schema,
                    'search_terms': search_terms
                })
                
    except Exception as e:
        logger.error(f"PostgreSQL医保项目搜索失败: {str(e)}")
        return jsonify({'success': False, 'error': f'PostgreSQL医保项目搜索失败: {str(e)}'}), 500

def search_postgresql_medical_insurance(search_terms, host, schema, limit):
    """PostgreSQL数据库医保项目搜索"""
    try:
        if host == 'default':
            conn = get_postgresql_connection()
            try:
                with conn.cursor() as cursor:
                    if schema:
                        cursor.execute(f'SET search_path TO "{schema}", public')
                    
                    # 首先检查表是否存在
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = '医保对照表'
                        )
                    """)
                    table_exists = cursor.fetchone()[0]
                    
                    if not table_exists:
                        # 如果表不存在，返回找不到医保对照表的提示
                        logger.warning("医保对照表不存在")
                        return jsonify({
                            'success': False,
                            'error': '找不到医保对照表',
                            'message': f'在数据库 {host} 的 {schema} 模式中找不到医保对照表',
                            'database': 'PostgreSQL',
                            'host': host,
                            'schema': schema
                        }), 404
                    
                    # 构建多项目搜索的SQL条件
                    if len(search_terms) == 1:
                        # 单个搜索词
                        sql = """
                        SELECT DISTINCT
                            医保项目编码,
                            医保项目名称,
                            医院项目编码,
                            医院项目名称,
                            费用类别
                        FROM 医保对照表
                        WHERE (
                            UPPER(医保项目编码) LIKE UPPER(%s) OR
                            UPPER(医保项目名称) LIKE UPPER(%s) OR
                            UPPER(医院项目编码) LIKE UPPER(%s) OR
                            UPPER(医院项目名称) LIKE UPPER(%s)
                        )
                        ORDER BY 医保项目编码
                        LIMIT %s
                        """
                        search_pattern = f'%{search_terms[0]}%'
                        cursor.execute(sql, (search_pattern, search_pattern, search_pattern, search_pattern, limit))
                    else:
                        # 多个搜索词，使用OR条件
                        conditions = []
                        params = []
                        
                        for term in search_terms:
                            search_pattern = f'%{term}%'
                            conditions.append("""(
                                UPPER(医保项目编码) LIKE UPPER(%s) OR
                                UPPER(医保项目名称) LIKE UPPER(%s) OR
                                UPPER(医院项目编码) LIKE UPPER(%s) OR
                                UPPER(医院项目名称) LIKE UPPER(%s)
                            )""")
                            params.extend([search_pattern, search_pattern, search_pattern, search_pattern])
                        
                        sql = f"""
                        SELECT DISTINCT
                            医保项目编码,
                            医保项目名称,
                            医院项目编码,
                            医院项目名称,
                            费用类别
                        FROM 医保对照表
                        WHERE ({" OR ".join(conditions)})
                        ORDER BY 医保项目编码
                        LIMIT %s
                        """
                        params.append(limit)
                        cursor.execute(sql, params)
                    
                    rows = cursor.fetchall()
                    columns = [col[0] for col in cursor.description]
                    
                    results = []
                    for row in rows:
                        result_dict = {}
                        for i, col in enumerate(columns):
                            result_dict[col] = row[i] if row[i] is not None else ''
                        results.append(result_dict)
                    
                    return jsonify({
                        'success': True,
                        'data': results,
                        'total': len(results),
                        'database': 'PostgreSQL',
                        'host': host,
                        'schema': schema,
                        'search_terms': search_terms
                    })
            finally:
                close_postgresql_connection(conn)
        else:
            import psycopg
            with psycopg.connect(host=host, port=5432, dbname="databasetools", user="postgres", password="P@ssw0rd") as conn:
                with conn.cursor() as cursor:
                    if schema:
                        cursor.execute(f'SET search_path TO "{schema}", public')
                    
                    sql = """
                    SELECT DISTINCT
                        医保项目编码,
                        医保项目名称,
                        医院项目编码,
                        医院项目名称,
                        费用类别
                    FROM 医保对照表
                    WHERE (
                        UPPER(医保项目编码) LIKE UPPER(%s) OR
                        UPPER(医保项目名称) LIKE UPPER(%s) OR
                        UPPER(医院项目编码) LIKE UPPER(%s) OR
                        UPPER(医院项目名称) LIKE UPPER(%s)
                    )
                    ORDER BY 医保项目编码
                    LIMIT %s
                    """
                    
                    search_pattern = f'%{search_term}%'
                    cursor.execute(sql, (search_pattern, search_pattern, search_pattern, search_pattern, limit))
                    
                    rows = cursor.fetchall()
                    columns = [col[0] for col in cursor.description]
                    
                    results = []
                    for row in rows:
                        result_dict = {}
                        for i, col in enumerate(columns):
                            result_dict[col] = row[i] if row[i] is not None else ''
                        results.append(result_dict)
                    
                    return jsonify({
                        'success': True,
                        'data': results,
                        'total': len(results),
                        'database': 'PostgreSQL',
                        'host': host,
                        'schema': schema,
                        'search_terms': search_terms
                    })
                    
    except Exception as e:
        logger.error(f"PostgreSQL医保项目搜索失败: {str(e)}")
        return jsonify({'success': False, 'error': f'PostgreSQL医保项目搜索失败: {str(e)}'}), 500 