#!/usr/bin/env python3
"""
测试医保对照表不存在时的错误提示功能
"""
import sys
import os
import json
import requests

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_table_not_found_response():
    """测试表不存在时的响应"""
    print("=== 测试医保对照表不存在时的错误提示 ===")
    
    # 测试数据
    test_data = {
        'search_term': '血常规',
        'database': 'postgresql',
        'host': '*************',
        'schema': 'public',
        'limit': 10
    }
    
    try:
        # 发送请求到医保搜索API
        response = requests.post(
            'http://localhost:5000/api/medical-insurance/search',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 404:
            response_data = response.json()
            if response_data.get('success') == False and '找不到医保对照表' in response_data.get('error', ''):
                print("✅ 测试通过：正确返回了表不存在的错误提示")
                return True
            else:
                print("❌ 测试失败：返回的错误信息不正确")
                return False
        else:
            print("❌ 测试失败：期望404状态码，但得到了其他状态码")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def test_error_response_structure():
    """测试错误响应的结构"""
    print("\n=== 测试错误响应结构 ===")
    
    # 模拟期望的错误响应结构
    expected_structure = {
        'success': False,
        'error': '找不到医保对照表',
        'message': str,  # 应该是字符串类型
        'database': str,  # 应该是字符串类型
        'host': str,      # 应该是字符串类型
        'schema': str     # 应该是字符串类型
    }
    
    print("期望的错误响应结构:")
    for key, value_type in expected_structure.items():
        print(f"  - {key}: {value_type}")
    
    return True

def main():
    """主测试函数"""
    print("开始测试医保对照表不存在时的错误提示功能...")
    
    # 测试错误响应结构
    structure_ok = test_error_response_structure()
    
    # 测试实际的API响应
    api_ok = test_table_not_found_response()
    
    print("\n=== 测试结果总结 ===")
    print(f"错误响应结构: {'✓' if structure_ok else '✗'}")
    print(f"API响应测试: {'✓' if api_ok else '✗'}")
    
    if structure_ok and api_ok:
        print("\n🎉 所有测试通过！医保对照表不存在时的错误提示功能正常。")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 