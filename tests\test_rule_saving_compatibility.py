#!/usr/bin/env python3
"""
测试规则保存功能兼容性修改
验证导入规则数据结构与现有保存功能的兼容性
"""

import sys
import os
import uuid
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.rule import RuleData, Rule
from services.rule_service import RuleManagementService
from services.rule_import_service import RuleImportService
from utils.config import config

def test_original_save_functionality():
    """测试原有的保存规则功能，确保不受影响"""
    print("=== 测试原有保存规则功能 ===")
    
    rule_service = RuleManagementService()
    
    # 创建测试规则数据（不包含id字段）
    test_rule_data = RuleData(
        name="测试规则_原有功能",
        content="SELECT * FROM test_table WHERE condition = 'test'",
        description="测试原有保存功能",
        category="测试分类",
        rule_type="超频次",
        database_type="postgresql"
    )
    
    try:
        # 保存规则
        result = rule_service.save_rule(test_rule_data, allow_overwrite=True)
        print(f"✓ 原有保存功能测试通过: {result}")
        
        # 读取规则验证
        saved_rule = rule_service.get_rule("测试规则_原有功能")
        print(f"✓ 读取规则成功: {saved_rule.name}")
        print(f"  - ID: {saved_rule.id}")
        print(f"  - 内容: {saved_rule.content[:50]}...")
        
        return True
    except Exception as e:
        print(f"✗ 原有保存功能测试失败: {e}")
        return False

def test_imported_rule_saving():
    """测试导入规则的保存功能"""
    print("\n=== 测试导入规则保存功能 ===")
    
    rule_service = RuleManagementService()
    
    # 模拟导入规则的数据结构
    imported_rule_data = {
        'id': 123,  # 原始数据库ID
        'rule_name': '导入测试规则',
        'rule_intension': 'SELECT * FROM imported_table WHERE status = "active"',
        'policy_basis': '基于政策依据的测试规则',
        'create_time': datetime.now().isoformat()
    }
    
    # 生成36位UUID
    rule_uuid = str(uuid.uuid4())
    
    # 创建RuleData对象，映射导入数据结构
    rule_data = RuleData(
        id=rule_uuid,  # 使用生成的UUID
        name=imported_rule_data['rule_name'],
        content=imported_rule_data['rule_intension'],
        description=imported_rule_data['policy_basis'],
        category='导入规则',
        rule_type='重复收费',  # 模拟智能分析结果
        policy_basis=imported_rule_data['policy_basis'],
        database_type='postgresql',
        patient_type='general',
        match_method='name'
    )
    
    try:
        # 保存导入的规则
        result = rule_service.save_rule(rule_data, allow_overwrite=True)
        print(f"✓ 导入规则保存成功: {result}")
        
        # 读取保存的规则验证
        saved_rule = rule_service.get_rule("导入测试规则")
        print(f"✓ 读取导入规则成功: {saved_rule.name}")
        print(f"  - UUID: {saved_rule.id}")
        print(f"  - 内容: {saved_rule.content[:50]}...")
        print(f"  - 政策依据: {saved_rule.policy_basis}")
        
        # 验证UUID格式
        if saved_rule.id and len(saved_rule.id) == 36:
            print(f"✓ UUID格式正确: {saved_rule.id}")
        else:
            print(f"✗ UUID格式错误: {saved_rule.id}")
            return False
        
        return True
    except Exception as e:
        print(f"✗ 导入规则保存测试失败: {e}")
        return False

def test_json_format_compatibility():
    """测试生成的JSON格式兼容性"""
    print("\n=== 测试JSON格式兼容性 ===")
    
    # 创建测试规则数据
    rule_data = RuleData(
        id=str(uuid.uuid4()),
        name="JSON格式测试规则",
        content="SELECT * FROM json_test_table",
        description="测试JSON格式兼容性",
        policy_basis="JSON格式测试政策依据",
        rule_type="限年龄",
        database_type="postgresql"
    )
    
    try:
        # 验证RuleData可以正确序列化
        rule_dict = {
            'id': rule_data.id,
            'name': rule_data.name,
            'content': rule_data.content,
            'description': rule_data.description,
            'policy_basis': rule_data.policy_basis,
            'rule_type': rule_data.rule_type,
            'database_type': rule_data.database_type
        }
        
        # 测试JSON序列化
        json_str = json.dumps(rule_dict, ensure_ascii=False, indent=2)
        print(f"✓ JSON序列化成功")
        print(f"  - JSON长度: {len(json_str)} 字符")
        
        # 测试JSON反序列化
        parsed_dict = json.loads(json_str)
        print(f"✓ JSON反序列化成功")
        print(f"  - 解析后的字段数: {len(parsed_dict)}")
        
        # 验证关键字段
        expected_fields = ['id', 'name', 'content', 'description', 'policy_basis', 'rule_type', 'database_type']
        for field in expected_fields:
            if field in parsed_dict:
                print(f"  ✓ 字段 '{field}' 存在")
            else:
                print(f"  ✗ 字段 '{field}' 缺失")
                return False
        
        return True
    except Exception as e:
        print(f"✗ JSON格式兼容性测试失败: {e}")
        return False

def test_uuid_uniqueness():
    """测试UUID的唯一性和格式正确性"""
    print("\n=== 测试UUID唯一性和格式 ===")
    
    # 生成多个UUID测试唯一性
    uuids = []
    for i in range(10):
        uuids.append(str(uuid.uuid4()))
    
    # 检查格式
    for i, uuid_str in enumerate(uuids):
        if len(uuid_str) == 36 and uuid_str.count('-') == 4:
            print(f"✓ UUID {i+1} 格式正确: {uuid_str}")
        else:
            print(f"✗ UUID {i+1} 格式错误: {uuid_str}")
            return False
    
    # 检查唯一性
    unique_uuids = set(uuids)
    if len(unique_uuids) == len(uuids):
        print(f"✓ 所有UUID都是唯一的 ({len(uuids)} 个)")
    else:
        print(f"✗ 存在重复UUID")
        return False
    
    return True

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    rule_service = RuleManagementService()
    
    # 测试没有id字段的旧格式规则
    old_format_rule = RuleData(
        name="向后兼容测试规则",
        content="SELECT * FROM backward_compatibility_test",
        description="测试向后兼容性",
        rule_type="超频次"
    )
    
    try:
        # 保存旧格式规则
        result = rule_service.save_rule(old_format_rule, allow_overwrite=True)
        print(f"✓ 旧格式规则保存成功: {result}")
        
        # 读取规则验证
        saved_rule = rule_service.get_rule("向后兼容测试规则")
        print(f"✓ 读取旧格式规则成功")
        print(f"  - ID: {saved_rule.id} (应该为None)")
        print(f"  - 名称: {saved_rule.name}")
        
        if saved_rule.id is None:
            print("✓ 向后兼容性测试通过")
            return True
        else:
            print("✗ 向后兼容性测试失败：ID应该为None")
            return False
            
    except Exception as e:
        print(f"✗ 向后兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试规则保存功能兼容性修改...")
    print("=" * 60)
    
    tests = [
        ("原有保存功能", test_original_save_functionality),
        ("导入规则保存", test_imported_rule_saving),
        ("JSON格式兼容性", test_json_format_compatibility),
        ("UUID唯一性", test_uuid_uniqueness),
        ("向后兼容性", test_backward_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过\n")
            else:
                print(f"✗ {test_name} 测试失败\n")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}\n")
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！规则保存功能兼容性修改成功。")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 