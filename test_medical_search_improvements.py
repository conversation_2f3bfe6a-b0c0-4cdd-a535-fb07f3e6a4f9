#!/usr/bin/env python3
"""
测试医保对照表检索功能改进
"""
import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_multi_item_search():
    """测试多项目检索功能"""
    print("=" * 60)
    print("多项目检索功能测试")
    print("=" * 60)
    
    # 测试多种分隔符
    test_cases = [
        "CT,核磁共振",  # 逗号分隔
        "CT，核磁共振",  # 中文逗号分隔
        "CT;核磁共振",  # 分号分隔
        "CT；核磁共振",  # 中文分号分隔
        "CT 核磁共振",   # 空格分隔
        "CT\n核磁共振",  # 换行分隔
        "CT,核磁共振;B超",  # 混合分隔符
    ]
    
    for i, search_term in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {search_term}")
        
        try:
            response = requests.post(f"{BASE_URL}/api/medical-insurance/search", 
                                   json={
                                       'search_term': search_term,
                                       'database': 'pg',
                                       'host': 'default',
                                       'schema': 'public',
                                       'limit': 10
                                   })
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 搜索成功，找到 {result.get('total', 0)} 个项目")
                    print(f"   搜索词解析: {result.get('search_terms', [])}")
                else:
                    print(f"❌ 搜索失败: {result.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")

def test_confirm_selection_function():
    """测试确认选择按钮功能"""
    print("\n" + "=" * 60)
    print("确认选择按钮功能测试")
    print("=" * 60)
    
    print("测试步骤:")
    print("1. 打开浏览器开发者工具 (F12)")
    print("2. 切换到 Console 标签页")
    print("3. 在规则编辑器中:")
    print("   - 选择一个包含医保项目名称参数的模板")
    print("   - 在规则条件中点击医保项目名称旁边的搜索按钮")
    print("   - 搜索一些项目并选择")
    print("   - 点击'确认选择'按钮")
    print("4. 查看控制台输出")
    
    print("\n预期输出:")
    print("- confirmMedicalSelection 被调用")
    print("- 当前搜索参数名: [参数名]")
    print("- 选中的项目数量: [数量]")
    print("- 选中的编码: [编码列表]")
    print("- 选中的名称: [名称列表]")
    print("- 找到的目标输入框: [元素对象]")
    print("- 填充编码类型/名称类型/组合类型，值: [填充值]")
    print("- 已填充值到输入框: [最终值]")
    print("- 已选择 [数量] 个项目并填充到 '[参数名]'")

def test_smart_fill_logic():
    """测试智能填充逻辑"""
    print("\n" + "=" * 60)
    print("智能填充逻辑测试")
    print("=" * 60)
    
    print("测试场景:")
    print("1. 医保项目编码参数:")
    print("   - 参数名包含'编码'或'代码'")
    print("   - 应填充医保项目编码")
    print("   - 多个项目用逗号分隔")
    
    print("\n2. 医保项目名称参数:")
    print("   - 参数名包含'名称'")
    print("   - 应填充医保项目名称")
    print("   - 多个项目用逗号分隔")
    
    print("\n3. 其他参数:")
    print("   - 默认填充编码+名称组合")
    print("   - 格式: '编码 - 名称'")
    print("   - 多个项目用逗号分隔")
    
    print("\n输入框查找策略:")
    print("1. 通过 data-param-name 属性查找")
    print("2. 通过 placeholder 包含参数名查找")
    print("3. 通过 name 属性查找")
    print("4. 通过 id 查找")
    print("5. 如果都找不到，显示错误提示")

def test_frontend_improvements():
    """测试前端改进"""
    print("\n" + "=" * 60)
    print("前端改进测试")
    print("=" * 60)
    
    print("改进内容:")
    print("1. ✅ 支持多种分隔符的多项目检索")
    print("   - 逗号 (,)、中文逗号 (，)")
    print("   - 分号 (;)、中文分号 (；)")
    print("   - 空格、换行符")
    
    print("\n2. ✅ 确认选择按钮功能完善")
    print("   - 添加详细的调试日志")
    print("   - 多种方式查找目标输入框")
    print("   - 智能判断参数类型")
    print("   - 触发input和change事件")
    print("   - 错误处理和用户提示")
    
    print("\n3. ✅ 智能填充逻辑")
    print("   - 根据参数名判断类型")
    print("   - 编码类型填充医保项目编码")
    print("   - 名称类型填充医保项目名称")
    print("   - 默认类型填充组合格式")

def main():
    """主测试函数"""
    print("医保对照表检索功能改进验证")
    print("=" * 60)
    
    # 等待应用启动
    print("等待应用启动...")
    time.sleep(2)
    
    # 1. 测试多项目检索功能
    test_multi_item_search()
    
    # 2. 测试确认选择按钮功能
    test_confirm_selection_function()
    
    # 3. 测试智能填充逻辑
    test_smart_fill_logic()
    
    # 4. 测试前端改进
    test_frontend_improvements()
    
    print("\n" + "=" * 60)
    print("改进完成")
    print("=" * 60)
    print("\n现在医保对照表检索功能应该:")
    print("1. ✅ 支持多种分隔符的多项目检索")
    print("2. ✅ 确认选择按钮正常工作")
    print("3. ✅ 根据变量类型智能填充")
    print("4. ✅ 提供详细的调试信息")

if __name__ == "__main__":
    main() 