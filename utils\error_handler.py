"""
Unified error handling module for the rule management system.
"""
import logging
from typing import Dict, Any, Optional
from flask import jsonify, Response
from datetime import datetime


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BaseError(Exception):
    """Base exception class for application errors."""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.details = details or {}
        super().__init__(self.message)


class BusinessError(BaseError):
    """Exception for business logic errors."""
    pass


class ValidationError(BaseError):
    """Exception for input validation errors."""
    pass


class SystemError(BaseError):
    """Exception for system-level errors."""
    pass


class ErrorHandler:
    """Unified error handler for the application."""
    
    @staticmethod
    def handle_business_error(error: BusinessError) -> Response:
        """Handle business logic errors."""
        logger.warning(f"Business error: {error.message}")
        return jsonify({
            "status": "error",
            "error_code": error.error_code,
            "message": error.message,
            "details": error.details,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }), 400
    
    @staticmethod
    def handle_validation_error(error: ValidationError) -> Response:
        """Handle input validation errors."""
        logger.warning(f"Validation error: {error.message}")
        return jsonify({
            "status": "error",
            "error_code": error.error_code,
            "message": error.message,
            "details": error.details,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }), 422
    
    @staticmethod
    def handle_system_error(error: SystemError) -> Response:
        """Handle system-level errors."""
        logger.error(f"System error: {error.message}")
        return jsonify({
            "status": "error",
            "error_code": "SYSTEM_ERROR",
            "message": "系统暂时不可用，请稍后重试",
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }), 500
    
    @staticmethod
    def handle_generic_error(error: Exception) -> Response:
        """Handle generic exceptions."""
        logger.error(f"Unexpected error: {str(error)}")
        return jsonify({
            "status": "error",
            "error_code": "INTERNAL_ERROR",
            "message": "发生内部错误，请稍后重试",
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }), 500
    
    @staticmethod
    def success_response(message: str, data: Any = None) -> Response:
        """Create a standardized success response."""
        response_data = {
            "status": "success",
            "message": message,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        
        if data is not None:
            response_data["data"] = data
            
        return jsonify(response_data)


def register_error_handlers(app):
    """Register error handlers with Flask app."""
    
    @app.errorhandler(BusinessError)
    def handle_business_error(error):
        return ErrorHandler.handle_business_error(error)
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(error):
        return ErrorHandler.handle_validation_error(error)
    
    @app.errorhandler(SystemError)
    def handle_system_error(error):
        return ErrorHandler.handle_system_error(error)
    
    @app.errorhandler(404)
    def handle_not_found(error):
        return jsonify({
            "status": "error",
            "error_code": "NOT_FOUND",
            "message": "请求的资源未找到",
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }), 404
    
    @app.errorhandler(500)
    def handle_internal_error(error):
        return ErrorHandler.handle_generic_error(error)