#!/usr/bin/env python3
"""
测试规则导入中的ID传递问题
"""

import requests
import json
import time

def test_rule_import_workflow():
    """测试完整的规则导入流程"""
    base_url = "http://127.0.0.1:5000"
    
    print("=== 测试规则导入流程 ===")
    
    # 1. 测试扫描可导入规则
    print("\n1. 测试扫描可导入规则...")
    scan_data = {
        "connection_data": {
            "host": "**************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        }
    }
    
    try:
        print(f"发送请求到: {base_url}/api/rules/import")
        print(f"请求数据: {json.dumps(scan_data, indent=2)}")
        
        response = requests.post(f"{base_url}/api/rules/import", json=scan_data, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"扫描结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success') and result.get('rules'):
                print(f"\n找到 {len(result['rules'])} 个可导入规则")
                
                # 显示前3个规则的ID和类型
                for i, rule in enumerate(result['rules'][:3]):
                    print(f"规则 {i+1}: ID={rule['id']} (类型: {type(rule['id']).__name__})")
                
                # 2. 测试导入第一个规则
                if result['rules']:
                    first_rule_id = result['rules'][0]['id']
                    print(f"\n2. 测试导入规则ID: {first_rule_id} (类型: {type(first_rule_id).__name__})")
                    
                    import_data = {
                        "selected_rules": [first_rule_id],
                        "overwrite": False,
                        "connection_data": scan_data["connection_data"]
                    }
                    
                    print(f"发送导入请求数据: {json.dumps(import_data, indent=2)}")
                    
                    import_response = requests.post(f"{base_url}/api/rules/import/execute", json=import_data, timeout=10)
                    print(f"导入状态码: {import_response.status_code}")
                    
                    if import_response.status_code == 200:
                        import_result = import_response.json()
                        print(f"导入结果: {json.dumps(import_result, indent=2, ensure_ascii=False)}")
                    else:
                        print(f"导入失败: {import_response.text}")
            else:
                print("没有找到可导入的规则")
        else:
            print(f"扫描失败: {response.text}")
            
    except requests.exceptions.ConnectionError as e:
        print(f"连接错误: {e}")
    except requests.exceptions.Timeout as e:
        print(f"请求超时: {e}")
    except Exception as e:
        print(f"测试失败: {str(e)}")

def test_frontend_data_simulation():
    """模拟前端数据传递"""
    print("\n=== 模拟前端数据传递 ===")
    
    # 模拟前端收集的规则ID
    rule_ids = [1, 2, 3]  # 整数ID
    rule_ids_str = ["1", "2", "3"]  # 字符串ID
    
    print(f"整数ID列表: {rule_ids}")
    print(f"字符串ID列表: {rule_ids_str}")
    
    # 模拟前端发送的数据
    frontend_data_int = {
        "selected_rules": rule_ids,
        "overwrite": False,
        "connection_data": {
            "host": "**************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        }
    }
    
    frontend_data_str = {
        "selected_rules": rule_ids_str,
        "overwrite": False,
        "connection_data": {
            "host": "**************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        }
    }
    
    print(f"\n前端发送的整数ID数据: {json.dumps(frontend_data_int, indent=2)}")
    print(f"前端发送的字符串ID数据: {json.dumps(frontend_data_str, indent=2)}")

if __name__ == "__main__":
    test_frontend_data_simulation()
    test_rule_import_workflow() 