# 设计规范说明 (Design Spec)

本文档定义了“临时规则编写工具”HTML原型的核心视觉设计规范，旨在确保界面的一致性和专业性。

## 1. 主题

*   **默认主题**: 暗黑主题 (Dark Mode)

## 2. 色彩系统 (Color Palette)

我们使用 Tailwind CSS 的默认颜色系统，并为其暗黑主题扩展了更深的灰色层次，以创造更好的视觉对比和深度。

| 颜色 | Hex Code | 用途 |
| :--- | :--- | :--- |
| **背景 (Primary)** | `bg-gray-900` (#121212) | 主要页面背景 |
| **背景 (Secondary)** | `bg-gray-800` (#1e1e1e) | 卡片、表格、次级面板背景 |
| **边框/分隔线** | `border-gray-700` (#2d2d2d) | 元素边框、分隔线 |
| **文本 (Primary)** | `text-gray-200` / `text-white` | 主要文本、标题 |
| **文本 (Secondary)** | `text-gray-400` | 辅助文本、占位符、描述 |
| **品牌色/高亮 (Accent)** | `bg-blue-600` (#2563eb) | 主要按钮、链接、焦点状态 |
| **成功 (Success)** | `bg-green-600` | 成功状态按钮、成功提示 |
| **危险 (Danger)** | `bg-red-500` | 删除按钮、错误提示 |

## 3. 字体排版 (Typography)

*   **字体族**: 无衬线字体 (Sans-Serif)。依赖于操作系统默认的无衬线字体，如 Segoe UI (Windows), San Francisco (macOS), Roboto (Android)。
*   **基础字号**: 16px (Tailwind `text-base`)
*   **字重**: 
    *   `font-normal` (400)
    *   `font-medium` (500)
    *   `font-semibold` (600)
    *   `font-bold` (700)

## 4. 图标库 (Iconography)

*   **图标库**: Font Awesome 6
*   **使用方式**: 通过 CDN 引入，使用 `<i>` 标签和对应的类名，例如 `<i class="fas fa-plus"></i>`。
*   **风格**: 优先使用 `fas` (Solid) 风格以保证清晰度。

## 5. 组件样式 (Component Styles)

### 按钮 (Buttons)

*   **主按钮**: `bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg`
*   **次要按钮**: `bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg`
*   **危险按钮**: `bg-red-600 hover:bg-red-700 ...`

### 输入框 (Inputs)

*   **标准样式**: `bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5`
*   **焦点状态**: 带有蓝色辉光 (`ring-2 ring-blue-500`)。

### 表格 (Tables)

*   **容器**: `bg-gray-800 rounded-lg overflow-hidden`
*   **表头**: `text-xs text-gray-300 uppercase bg-gray-700`
*   **行**: `border-b border-gray-700 hover:bg-gray-700`

### 卡片/面板 (Cards/Panels)

*   **标准样式**: `bg-gray-800 p-4 rounded-lg`

## 6. 模拟窗口样式

为了增强原型的真实感，所有通过 `<iframe>` 嵌入的页面都包含在一个模拟的 macOS 风格的窗口装饰器中。

*   **标题栏**: `bg-gray-700`
*   **窗口控件**: 红、黄、绿三个圆形按钮，分别代表关闭、最小化、最大化。