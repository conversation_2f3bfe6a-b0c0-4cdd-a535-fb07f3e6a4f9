# Implementation Plan

- [-] 1. 重构现有应用为分层架构


  - 在保持现有功能的基础上，将app.py重构为分层架构
  - 创建services、models、utils目录结构
  - 建立配置管理和统一错误处理机制
  - 确保现有前端页面temp_rule_editor.html的正常工作
  - _Requirements: 6.3, 6.4, 6.5_

- [x] 2. 实现规则管理核心服务






  - [x] 2.1 创建规则管理服务类和数据模型

    - 实现RuleManagementService类，包含规则的CRUD操作方法
    - 定义RuleInfo、RuleData等数据模型类
    - 创建文件系统DAO层，处理规则文件的读写操作
    - _Requirements: 1.1, 1.2, 1.7_

  - [x] 2.2 实现规则列表和搜索功能


    - 开发list_rules方法，扫描output目录并返回规则元数据
    - 实现规则名称的模糊搜索功能
    - 添加分页支持，处理大量规则的显示
    - _Requirements: 1.1, 1.2, 1.3, 1.4_

  - [x] 2.3 实现规则的增删改操作


    - 开发save_rule方法，支持新建和更新规则
    - 实现delete_rule方法，执行逻辑删除
    - 添加规则名称冲突检测和处理
    - _Requirements: 1.5, 1.6, 1.7_

- [x] 3. 开发SQL模板管理系统





  - [x] 3.1 创建模板管理服务和模板解析器


    - 实现TemplateManagementService类，管理SQL模板的加载和分类
    - 开发模板参数解析器，提取模板中的参数化字段
    - 建立模板分类和数据库类型的映射关系
    - _Requirements: 3.1, 3.2, 4.1, 4.2_

  - [x] 3.2 实现SQL生成引擎


    - 集成Jinja2模板引擎，实现参数化SQL生成
    - 开发参数验证器，确保必填参数的完整性
    - 实现多数据库类型的SQL方言支持
    - _Requirements: 2.1, 2.2, 2.3, 4.3, 4.4_



  - [x] 3.3 建立模板库管理功能
    - 实现get_templates方法，返回可用模板列表
    - 开发模板内容加载和缓存机制
    - 添加模板参数的动态表单生成逻辑
    - _Requirements: 3.3, 3.4, 3.5_

- [-] 4. 实现智能规则SQL生成器



  - [x] 4.1 开发智能模板选择引擎
    - 实现基于规则属性的模板自动筛选算法
    - 根据规则名称、类型、适用范围(门诊/住院/通用)自动匹配模板
    - 支持基于使用项目名称(按名称/按编码)的模板筛选
    - 集成数据库类型(PostgreSQL/Oracle)的模板过滤
    - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_



  - [ ] 4.2 构建规则属性管理系统
    - 扩展Rule模型，添加规则类型、适用范围、项目匹配方式等属性
    - 实现规则属性的表单输入和验证
    - 开发规则属性与模板类别的映射逻辑
    - 保持现有自由编写SQL功能的完整性


    - _Requirements: 1.1, 1.2, 1.7, 2.1, 6.8_

  - [x] 4.3 实现智能SQL生成工作流


    - 开发规则创建向导，引导用户选择规则属性
    - 实现模板自动推荐和手动选择功能
    - 构建参数填写表单的动态生成
    - 集成SQL预览和编辑功能，支持生成后手动调整
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 3.3, 3.4, 6.6, 6.7_

  - [x] 4.4 增强规则编辑器功能
    - 在现有SQL编辑器基础上添加模板选择面板
    - 实现模板参数填写区域的动态显示
    - 支持在模板生成和自由编写之间切换
    - 保持现有规则保存和管理功能不变
    - _Requirements: 2.5, 2.6, 2.7, 5.3, 6.8_

- [ ] 5. 构建RESTful API层
  - [ ] 5.1 实现规则管理API端点
    - 开发GET /api/rules端点，返回规则列表
    - 实现POST /api/rules端点，创建新规则
    - 创建GET /api/rules/{name}和DELETE /api/rules/{name}端点
    - _Requirements: 1.1, 1.5, 1.6, 1.7_

  - [ ] 5.2 开发智能SQL生成API端点
    - 实现GET /api/templates/recommend端点，基于规则属性推荐模板
    - 开发POST /api/sql/generate端点，生成SQL语句
    - 添加GET /api/templates/filter端点，支持多条件模板筛选
    - 实现POST /api/rules/wizard端点，支持规则创建向导
    - 添加API请求验证和错误处理中间件
    - _Requirements: 2.1, 2.2, 2.3, 3.1, 6.1, 6.2, 6.3, 6.4, 6.5_

  - [ ] 5.3 集成统一错误处理和响应格式
    - 实现ErrorHandler类，统一处理各类异常
    - 建立标准化的API响应格式
    - 添加请求日志记录和性能监控
    - _Requirements: 6.4, 6.5_

- [ ] 6. 基于现有前端页面增强功能
  - [ ] 6.1 重构现有Flask应用架构
    - 将现有的单文件app.py重构为分层架构
    - 保持现有API端点的兼容性，优化内部实现
    - 集成新的服务层到现有的路由处理中
    - _Requirements: 1.1, 1.2, 6.3, 6.4_

  - [ ] 6.2 增强现有规则管理功能
    - 优化现有的规则列表显示，添加规则类型、适用范围等元数据信息
    - 改进规则编辑功能，支持规则属性设置和智能模板选择
    - 增强规则删除确认机制和用户反馈
    - 保持现有自由编写规则功能的完整性
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

  - [ ] 6.3 实现智能规则创建界面
    - 在现有页面基础上添加规则创建向导功能
    - 实现规则属性选择界面(类型、适用范围、项目匹配方式等)
    - 开发模板推荐和选择界面
    - 构建参数填写表单的动态生成和验证
    - 集成SQL预览和手动编辑功能
    - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 4.1, 4.2, 4.3, 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7, 6.8_

  - [ ] 6.4 扩展数据库配置功能
    - 完善现有的数据库配置模态框功能
    - 实现数据库类型选择对模板加载的影响
    - 添加配置验证和连接测试功能
    - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [ ] 7. 实现性能优化和用户体验提升
  - [ ] 7.1 优化系统响应性能
    - 实现模板内容的缓存机制，减少文件系统访问
    - 优化规则列表的加载性能，支持懒加载和分页
    - 添加SQL生成的异步处理，提升用户体验
    - 优化智能模板选择算法的性能
    - _Requirements: 5.1, 5.2_

  - [ ] 7.2 增强用户界面反馈和提示
    - 实现操作状态的实时反馈，如加载动画和进度提示
    - 添加友好的错误信息显示和用户指导
    - 优化表单验证和输入提示功能
    - 为智能模板推荐提供清晰的用户反馈
    - _Requirements: 5.3, 5.4, 5.5_

- [ ] 8. 建立安全性和数据完整性保障
  - [ ] 8.1 实现输入验证和安全防护
    - 开发全面的输入验证器，防止SQL注入和XSS攻击
    - 实现参数化查询和输出编码机制
    - 添加文件操作的权限检查和路径验证
    - _Requirements: 7.1, 7.2, 7.3, 7.4_

  - [ ] 8.2 建立日志记录和监控系统
    - 实现详细的操作日志记录，包括用户行为和系统事件
    - 开发异常监控和告警机制
    - 添加系统性能指标的收集和分析
    - _Requirements: 7.5_

- [ ] 9. 编写全面的单元测试和集成测试
  - [ ] 9.1 创建服务层单元测试
    - 为RuleManagementService编写全面的单元测试
    - 测试SqlGenerationService的各种场景和边界条件
    - 验证TemplateManagementService的模板解析和管理功能
    - 测试智能模板选择引擎的准确性和性能
    - _Requirements: 1.1, 1.2, 1.7, 2.1, 2.2, 2.3, 3.1, 3.2, 4.1, 4.2_

  - [ ] 9.2 开发API端点集成测试
    - 测试所有REST API端点的正常和异常场景
    - 验证API的数据验证和错误处理机制
    - 测试并发访问和性能边界条件
    - 测试智能模板推荐API的准确性和性能
    - _Requirements: 5.1, 5.2, 7.3, 7.4_

  - [ ] 9.3 实现端到端功能测试
    - 测试完整的智能规则创建工作流
    - 验证模板自动选择和SQL生成的准确性
    - 测试多数据库兼容性和用户界面交互
    - 验证自由编写规则功能的完整性
    - _Requirements: 1.1-1.7, 2.1-2.8, 3.1-3.5, 4.1-4.4, 5.1-5.5_

- [ ] 10. 系统部署和配置优化
  - [ ] 10.1 准备生产环境部署配置
    - 创建生产环境的配置文件和环境变量设置
    - 配置Gunicorn和Nginx的生产环境参数
    - 建立日志轮转和备份策略
    - _Requirements: 5.1, 5.2, 7.5_

  - [ ] 10.2 实现系统监控和维护功能
    - 添加健康检查端点和系统状态监控
    - 实现自动化的系统备份和恢复机制
    - 创建运维文档和故障排除指南
    - _Requirements: 7.5_