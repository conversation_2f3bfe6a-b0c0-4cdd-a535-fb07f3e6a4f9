#!/usr/bin/env python3
"""
测试同步SQL功能
"""

import requests
import json
import time

def test_sync_sql_functionality():
    """测试同步SQL功能"""
    base_url = "http://localhost:5000"
    
    print("🧪 开始测试同步SQL功能...")
    
    # 1. 测试获取规则列表
    print("\n1. 测试获取规则列表")
    try:
        response = requests.get(f"{base_url}/api/rules")
        if response.status_code == 200:
            data = response.json()
            rules = data.get('rules', [])
            print(f"   ✅ 成功获取 {len(rules)} 个规则")
            
            if rules:
                # 使用第一个规则进行测试
                test_rule = rules[0]
                rule_id = test_rule.get('name')  # 使用规则名称作为ID
                print(f"   📋 测试规则: {test_rule.get('name')}")
                
                # 2. 测试获取规则SQL内容
                print("\n2. 测试获取规则SQL内容")
                response = requests.get(f"{base_url}/api/rules/{rule_id}/sql")
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        sql_content = data.get('sql_content', '')
                        print(f"   ✅ 成功获取SQL内容，长度: {len(sql_content)} 字符")
                        if sql_content:
                            print(f"   📄 SQL预览: {sql_content[:100]}...")
                        else:
                            print("   ⚠️  SQL内容为空")
                    else:
                        print(f"   ❌ 获取SQL内容失败: {data.get('error')}")
                else:
                    print(f"   ❌ 获取SQL内容请求失败: {response.status_code}")
                
                # 3. 测试获取数据库连接
                print("\n3. 测试获取数据库连接")
                response = requests.get(f"{base_url}/api/database/connections")
                if response.status_code == 200:
                    data = response.json()
                    connections = data.get('connections', [])
                    print(f"   ✅ 成功获取 {len(connections)} 个数据库连接")
                    
                    if connections:
                        # 使用第一个连接进行测试
                        test_connection = connections[0]
                        connection_id = test_connection.get('id')
                        print(f"   📋 测试连接: {test_connection.get('name')}")
                        
                        # 4. 测试同步SQL功能
                        print("\n4. 测试同步SQL功能")
                        sync_data = {
                            'rule_id': rule_id,
                            'connection_id': connection_id
                        }
                        
                        response = requests.post(
                            f"{base_url}/api/rules/sync-sql",
                            json=sync_data,
                            headers={'Content-Type': 'application/json'}
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            if data.get('success'):
                                print(f"   ✅ 同步SQL成功: {data.get('message')}")
                                details = data.get('details', {})
                                print(f"   📊 同步详情: {details}")
                            else:
                                print(f"   ❌ 同步SQL失败: {data.get('error')}")
                        else:
                            print(f"   ❌ 同步SQL请求失败: {response.status_code}")
                            try:
                                error_data = response.json()
                                print(f"   📄 错误详情: {error_data}")
                            except:
                                print(f"   📄 错误响应: {response.text}")
                    else:
                        print("   ⚠️  没有可用的数据库连接")
                else:
                    print(f"   ❌ 获取数据库连接失败: {response.status_code}")
            else:
                print("   ⚠️  没有可用的规则")
        else:
            print(f"   ❌ 获取规则列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 测试过程中出现错误: {e}")
    
    print("\n📋 同步SQL功能测试完成")
    print("主要功能验证：")
    print("1. ✅ 获取规则列表")
    print("2. ✅ 获取规则SQL内容")
    print("3. ✅ 获取数据库连接")
    print("4. ✅ 同步SQL到目标数据库")

if __name__ == "__main__":
    test_sync_sql_functionality() 