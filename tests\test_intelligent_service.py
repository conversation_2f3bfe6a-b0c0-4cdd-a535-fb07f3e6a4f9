"""
Simple test script for intelligent template service.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.intelligent_template_service import IntelligentTemplateService, RuleAttributes

def test_intelligent_service():
    """Test the intelligent template service."""
    try:
        # Initialize service
        service = IntelligentTemplateService()
        print("✓ Intelligent template service initialized successfully")
        
        # Test attribute extraction
        rule_name = "住院患者超每日数量检查"
        attributes = service._extract_attributes_from_name(rule_name)
        print(f"✓ Extracted attributes from '{rule_name}':")
        print(f"  - Rule type: {attributes.rule_type}")
        print(f"  - Patient type: {attributes.patient_type}")
        print(f"  - Match method: {attributes.match_method}")
        
        # Test rule analysis
        analysis = service.analyze_rule_attributes(rule_name)
        print(f"✓ Rule analysis completed:")
        print(f"  - Detected rule type: {analysis['detected_rule_type']}")
        print(f"  - Detected patient type: {analysis['detected_patient_type']}")
        print(f"  - Confidence: {analysis['confidence']}")
        
        # Test template recommendations
        recommendations = service.get_template_recommendations_by_name(rule_name, max_recommendations=3)
        print(f"✓ Found {len(recommendations)} template recommendations")
        
        for i, rec in enumerate(recommendations[:3], 1):
            print(f"  {i}. {rec.template.description} (Score: {rec.score:.1f})")
            print(f"     Category: {rec.template.category}")
            print(f"     Reasons: {', '.join(rec.reasons[:2])}")
        
        # Test filtering
        filtered = service.get_filtered_templates(
            database_type="postgresql",
            patient_type="inpatient"
        )
        print(f"✓ Filtered templates: {len(filtered)} PostgreSQL inpatient templates found")
        
        print("\n🎉 All tests passed! Intelligent template selection engine is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_intelligent_service()