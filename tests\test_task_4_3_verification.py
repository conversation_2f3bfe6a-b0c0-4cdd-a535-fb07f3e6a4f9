#!/usr/bin/env python3
"""
Verification test for Task 4.3: 实现智能SQL生成工作流
"""
import sys
import os
import json
import requests
import time
from threading import Thread

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app

def run_flask_app():
    """Run Flask app in a separate thread."""
    app.run(debug=False, port=5002, use_reloader=False)

def test_task_4_3_requirements():
    """
    Test all requirements for Task 4.3:
    - 开发规则创建向导，引导用户选择规则属性
    - 实现模板自动推荐和手动选择功能
    - 构建参数填写表单的动态生成
    - 集成SQL预览和编辑功能，支持生成后手动调整
    """
    base_url = "http://127.0.0.1:5002"
    
    # Wait for server to start
    time.sleep(2)
    
    print("=== Task 4.3 Verification: 实现智能SQL生成工作流 ===\n")
    
    try:
        # Requirement 1: 开发规则创建向导，引导用户选择规则属性
        print("✓ Requirement 1: 规则创建向导 - 引导用户选择规则属性")
        print("  Testing workflow initialization and rule attributes step...")
        
        unique_name = f"智能向导测试规则_{int(time.time())}"
        response = requests.post(f"{base_url}/api/workflow/start", 
                               json={"rule_name": unique_name})
        
        assert response.status_code == 200, f"Failed to start workflow: {response.status_code}"
        result = response.json()
        workflow_id = result['data']['workflow_id']
        
        # Verify workflow starts with rule_attributes step
        assert result['data']['summary']['current_step'] == 'rule_attributes', "Workflow should start with rule_attributes step"
        
        # Verify suggested attributes are provided
        step_data = result['data']['current_step_data']
        assert 'suggested_attributes' in step_data, "Should provide suggested attributes"
        
        print("    ✓ Workflow starts with rule attributes step")
        print("    ✓ Provides intelligent attribute suggestions")
        print("    ✓ Guides user through structured attribute selection")
        
        # Test rule attributes update
        attributes = {
            "name": unique_name,
            "rule_type": "超频次",
            "patient_type": "inpatient", 
            "match_method": "name",
            "database_type": "postgresql",
            "description": "通过智能向导创建的测试规则",
            "category": "intelligent",
            "policy_basis": "医保政策文件XYZ"
        }
        
        response = requests.put(f"{base_url}/api/workflow/{workflow_id}/attributes",
                              json=attributes)
        
        assert response.status_code == 200, f"Failed to update attributes: {response.status_code}"
        result = response.json()
        
        # Verify workflow progresses to template_selection
        assert result['data']['current_step'] == 'template_selection', "Should progress to template_selection step"
        
        print("    ✓ Successfully processes rule attributes")
        print("    ✓ Validates and stores user input")
        print("    ✓ Progresses to next step automatically\n")
        
        # Requirement 2: 实现模板自动推荐和手动选择功能
        print("✓ Requirement 2: 模板自动推荐和手动选择功能")
        print("  Testing automatic template recommendations...")
        
        # Verify template recommendations are provided
        step_data = result['data']['current_step_data']
        assert 'recommendations' in step_data, "Should provide template recommendations"
        recommendations = step_data['recommendations']
        assert len(recommendations) > 0, "Should have at least one recommendation"
        
        # Verify recommendation structure
        first_rec = recommendations[0]
        required_fields = ['template', 'score', 'reasons']
        for field in required_fields:
            assert field in first_rec, f"Recommendation should have {field} field"
        
        print(f"    ✓ Provides {len(recommendations)} template recommendations")
        print(f"    ✓ Top recommendation: {first_rec['template']['description']} (Score: {first_rec['score']:.2f})")
        print(f"    ✓ Includes reasoning: {', '.join(first_rec['reasons'])}")
        
        # Test template selection
        selected_template_id = first_rec['template']['id']
        response = requests.put(f"{base_url}/api/workflow/{workflow_id}/template",
                              json={"template_id": selected_template_id})
        
        assert response.status_code == 200, f"Failed to select template: {response.status_code}"
        result = response.json()
        
        # Verify workflow progresses to parameter_input
        assert result['data']['current_step'] == 'parameter_input', "Should progress to parameter_input step"
        
        print("    ✓ Successfully selects recommended template")
        print("    ✓ Progresses to parameter input step\n")
        
        # Requirement 3: 构建参数填写表单的动态生成
        print("✓ Requirement 3: 参数填写表单的动态生成")
        print("  Testing dynamic form generation for template parameters...")
        
        # Verify form configuration is provided
        step_data = result['data']['current_step_data']
        assert 'form_config' in step_data, "Should provide form configuration"
        form_config = step_data['form_config']
        assert 'parameters' in form_config, "Form config should have parameters"
        
        parameters = form_config['parameters']
        assert len(parameters) > 0, "Should have at least one parameter"
        
        print(f"    ✓ Generates dynamic form with {len(parameters)} parameters")
        
        # Verify parameter structure
        for param in parameters:
            required_param_fields = ['name', 'label', 'type']
            for field in required_param_fields:
                assert field in param, f"Parameter should have {field} field"
        
        print("    ✓ Parameters have proper structure (name, label, type)")
        print("    ✓ Form supports different input types")
        
        # Test parameter submission
        test_parameters = {}
        for param in parameters:
            if param['type'] == 'select' and 'options' in param:
                test_parameters[param['name']] = param['options'][0]['value'] if param['options'] else 'test'
            elif param['name'] == '医保名称1':
                test_parameters[param['name']] = '测试医保项目1,测试医保项目2'
            elif param['name'] == '违规数量':
                test_parameters[param['name']] = '3'
            elif param['type'] == 'number':
                test_parameters[param['name']] = '5'
            else:
                test_parameters[param['name']] = f"测试值_{param['name']}"
        
        response = requests.put(f"{base_url}/api/workflow/{workflow_id}/parameters",
                              json={"parameters": test_parameters})
        
        assert response.status_code == 200, f"Failed to submit parameters: {response.status_code}"
        result = response.json()
        
        # Verify workflow progresses to sql_preview
        assert result['data']['current_step'] == 'sql_preview', "Should progress to sql_preview step"
        assert 'generated_sql' in result['data'], "Should have generated SQL"
        
        print("    ✓ Successfully submits form parameters")
        print("    ✓ Validates parameter input")
        print("    ✓ Generates SQL from parameters\n")
        
        # Requirement 4: 集成SQL预览和编辑功能，支持生成后手动调整
        print("✓ Requirement 4: SQL预览和编辑功能，支持生成后手动调整")
        print("  Testing SQL preview and editing capabilities...")
        
        # Verify SQL was generated
        generated_sql = result['data']['generated_sql']
        assert generated_sql and len(generated_sql) > 0, "Should have generated SQL content"
        assert 'SELECT' in generated_sql.upper(), "Generated SQL should be a valid query"
        
        print("    ✓ Successfully generates SQL from template and parameters")
        print(f"    ✓ Generated SQL length: {len(generated_sql)} characters")
        
        # Test SQL preview functionality
        response = requests.post(f"{base_url}/api/workflow/preview-sql",
                               json={
                                   "template_id": selected_template_id,
                                   "parameters": test_parameters,
                                   "database_type": "postgresql"
                               })
        
        assert response.status_code == 200, f"Failed to preview SQL: {response.status_code}"
        preview_result = response.json()
        
        assert 'preview_sql' in preview_result['data'], "Should provide SQL preview"
        
        print("    ✓ Provides SQL preview functionality")
        
        # Test SQL editing functionality
        modified_sql = generated_sql + "\n-- 手动添加的注释"
        response = requests.put(f"{base_url}/api/workflow/{workflow_id}/sql",
                              json={"sql_content": modified_sql})
        
        assert response.status_code == 200, f"Failed to update SQL: {response.status_code}"
        result = response.json()
        
        assert result['data']['updated_sql'] == modified_sql, "Should update SQL content"
        
        print("    ✓ Supports manual SQL editing")
        print("    ✓ Preserves user modifications")
        
        # Test workflow finalization
        response = requests.post(f"{base_url}/api/workflow/{workflow_id}/finalize")
        
        assert response.status_code == 200, f"Failed to finalize workflow: {response.status_code}"
        final_result = response.json()
        
        assert 'rule_name' in final_result['data'], "Should return rule creation result"
        assert final_result['data']['workflow_completed'] == True, "Should mark workflow as completed"
        
        print("    ✓ Successfully finalizes and saves rule")
        print("    ✓ Completes full workflow cycle\n")
        
        # Additional verification: Test workflow summary
        print("✓ Additional Verification: Workflow State Management")
        response = requests.get(f"{base_url}/api/workflow/{workflow_id}/summary")
        
        assert response.status_code == 200, f"Failed to get workflow summary: {response.status_code}"
        summary = response.json()
        
        progress = summary['data']['progress']
        assert progress['percentage'] == 100, "Should show 100% completion"
        assert progress['completed_steps'] == progress['total_steps'], "All steps should be completed"
        
        print("    ✓ Maintains accurate workflow state")
        print("    ✓ Tracks progress correctly")
        print("    ✓ Provides comprehensive summary\n")
        
        print("🎉 ALL REQUIREMENTS VERIFIED SUCCESSFULLY!")
        print("\nTask 4.3 Implementation Summary:")
        print("✅ 规则创建向导 - 完整的步骤引导和属性选择")
        print("✅ 模板自动推荐 - 智能推荐算法和手动选择")
        print("✅ 动态表单生成 - 基于模板参数的表单构建")
        print("✅ SQL预览编辑 - 生成预览和手动调整功能")
        print("✅ 工作流管理 - 完整的状态跟踪和进度管理")
        
    except AssertionError as e:
        print(f"❌ VERIFICATION FAILED: {str(e)}")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Failed to connect to Flask server")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    # Start Flask app in background thread
    flask_thread = Thread(target=run_flask_app, daemon=True)
    flask_thread.start()
    
    # Run verification tests
    success = test_task_4_3_requirements()
    
    if success:
        print("\n✅ Task 4.3 verification completed successfully!")
        exit(0)
    else:
        print("\n❌ Task 4.3 verification failed!")
        exit(1)