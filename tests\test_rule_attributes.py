"""
Test script for rule attributes management system.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.rule_service import RuleManagementService
from models.rule import RuleData

def test_rule_attributes():
    """Test the rule attributes management system."""
    try:
        # Initialize service
        service = RuleManagementService()
        print("✓ Rule management service initialized successfully")
        
        # Test getting attribute options
        options = service.get_rule_attribute_options()
        print("✓ Retrieved rule attribute options:")
        print(f"  - Rule types: {len(options['rule_types'])} options")
        print(f"  - Patient types: {len(options['patient_types'])} options")
        print(f"  - Match methods: {len(options['match_methods'])} options")
        print(f"  - Database types: {len(options['database_types'])} options")
        
        # Test rule name analysis
        rule_name = "住院患者超每日数量检查按名称"
        analysis = service.analyze_rule_name_for_attributes(rule_name)
        print(f"✓ Analyzed rule name '{rule_name}':")
        print(f"  - Detected rule type: {analysis['detected_rule_type']}")
        print(f"  - Detected patient type: {analysis['detected_patient_type']}")
        print(f"  - Detected match method: {analysis['detected_match_method']}")
        print(f"  - Confidence: {analysis['confidence']}")
        
        # Test attribute validation - valid attributes
        valid_rule_data = RuleData(
            name="测试规则",
            content="SELECT * FROM test",
            rule_type="超频次",
            patient_type="inpatient",
            match_method="name",
            database_type="postgresql"
        )
        
        errors = service.validate_rule_attributes(valid_rule_data)
        print(f"✓ Validated valid attributes: {len(errors)} errors found")
        
        # Test attribute validation - invalid attributes
        invalid_rule_data = RuleData(
            name="测试规则",
            content="SELECT * FROM test",
            rule_type="无效类型",
            patient_type="invalid_type",
            match_method="invalid_method",
            database_type="invalid_db"
        )
        
        errors = service.validate_rule_attributes(invalid_rule_data)
        print(f"✓ Validated invalid attributes: {len(errors)} errors found")
        for error in errors:
            print(f"    - {error}")
        
        # Test rule statistics
        stats = service.get_rule_statistics()
        print(f"✓ Retrieved rule statistics:")
        print(f"  - Total rules: {stats['total_rules']}")
        print(f"  - Active rules: {stats['active_rules']}")
        print(f"  - Rule type distribution: {stats['rule_type_distribution']}")
        
        # Test filtering rules by attributes
        filtered_rules = service.get_rules_by_attributes(
            patient_type="inpatient",
            database_type="postgresql"
        )
        print(f"✓ Filtered rules by attributes: {len(filtered_rules)} rules found")
        
        print("\n🎉 All tests passed! Rule attributes management system is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_rule_attributes()