#!/usr/bin/env python3
"""
数据库API测试脚本
"""
import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_get_schemas():
    """测试获取Schema列表"""
    print("=== 测试获取Schema列表 ===")
    
    # 测试PostgreSQL
    print("\n1. 测试PostgreSQL Schema列表:")
    response = requests.post(f"{BASE_URL}/api/database_schemas", 
                           json={"database": "pg", "host": "default"})
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    # 测试Oracle
    print("\n2. 测试Oracle Schema列表:")
    response = requests.post(f"{BASE_URL}/api/database_schemas", 
                           json={"database": "oracle", "host": "default"})
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

def test_execute_sql():
    """测试SQL执行"""
    print("\n=== 测试SQL执行 ===")
    
    # 测试PostgreSQL
    print("\n1. 测试PostgreSQL SQL执行:")
    response = requests.post(f"{BASE_URL}/api/rules/execute_sql", 
                           json={
                               "sql": "SELECT 1 as test_column, 'Hello World' as message",
                               "database": "pg",
                               "host": "default",
                               "schema": ""
                           })
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    
    # 测试Oracle
    print("\n2. 测试Oracle SQL执行:")
    response = requests.post(f"{BASE_URL}/api/rules/execute_sql", 
                           json={
                               "sql": "SELECT 1 as test_column, 'Hello World' as message FROM dual",
                               "database": "oracle",
                               "host": "default",
                               "schema": ""
                           })
    print(f"状态码: {response.status_code}")
    print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

def test_dangerous_sql():
    """测试危险SQL拦截"""
    print("\n=== 测试危险SQL拦截 ===")
    
    dangerous_sqls = [
        "UPDATE users SET name = 'test'",
        "DELETE FROM users",
        "DROP TABLE users",
        "CREATE TABLE test (id int)",
        "ALTER TABLE users ADD COLUMN test int"
    ]
    
    for i, sql in enumerate(dangerous_sqls, 1):
        print(f"\n{i}. 测试危险SQL: {sql}")
        response = requests.post(f"{BASE_URL}/api/rules/execute_sql", 
                               json={
                                   "sql": sql,
                                   "database": "pg",
                                   "host": "default",
                                   "schema": ""
                               })
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

if __name__ == "__main__":
    try:
        test_get_schemas()
        test_execute_sql()
        test_dangerous_sql()
        print("\n=== 测试完成 ===")
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器，请确保Flask应用正在运行")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}") 