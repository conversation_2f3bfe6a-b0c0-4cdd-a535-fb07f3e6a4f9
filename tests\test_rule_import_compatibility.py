#!/usr/bin/env python3
"""
测试规则导入功能与新的保存机制的兼容性
验证实际的导入流程是否正常工作
"""

import sys
import os
import uuid
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.rule_import_service import RuleImportService
from services.rule_service import RuleManagementService
from utils.config import config

def test_rule_import_service_initialization():
    """测试规则导入服务初始化"""
    print("=== 测试规则导入服务初始化 ===")
    
    try:
        import_service = RuleImportService()
        print("✓ 规则导入服务初始化成功")
        return True
    except Exception as e:
        print(f"✗ 规则导入服务初始化失败: {e}")
        return False

def test_connection_info_retrieval():
    """测试连接信息获取功能"""
    print("\n=== 测试连接信息获取功能 ===")
    
    import_service = RuleImportService()
    
    # 测试直接提供连接数据
    test_connection_data = {
        'host': '**************',
        'port': 5432,
        'database': 'databasetools',
        'username': 'postgres',
        'password': 'P@ssw0rd',
        'name': '测试连接'
    }
    
    try:
        conn_info = import_service.get_connection_info(connection_data=test_connection_data)
        print("✓ 连接信息获取成功")
        print(f"  - 主机: {conn_info['host']}")
        print(f"  - 端口: {conn_info['port']}")
        print(f"  - 数据库: {conn_info['database']}")
        return True
    except Exception as e:
        print(f"✗ 连接信息获取失败: {e}")
        return False

def test_import_data_structure_mapping():
    """测试导入数据结构映射"""
    print("\n=== 测试导入数据结构映射 ===")
    
    # 模拟从数据库查询返回的规则数据
    imported_rule_data = {
        'id': 123,
        'rule_name': '测试导入规则',
        'rule_intension': 'SELECT * FROM test_import_table WHERE status = "active"',
        'policy_basis': '基于测试政策的规则',
        'create_time': datetime.now()
    }
    
    # 直接使用原始数据库ID，转换为字符串
    rule_id_str = str(imported_rule_data['id'])
    
    # 测试字段映射
    mapped_data = {
        'id': rule_id_str,  # 原始id → 字符串格式的原始ID
        'name': imported_rule_data['rule_name'],  # rule_name → name
        'content': imported_rule_data['rule_intension'],  # rule_intension → content
        'description': imported_rule_data['policy_basis'],  # policy_basis → description
        'policy_basis': imported_rule_data['policy_basis'],  # policy_basis → policy_basis
        'create_time': imported_rule_data['create_time']  # create_time → create_time
    }
    
    print("✓ 数据结构映射成功")
    print(f"  - 原始ID: {imported_rule_data['id']} → 字符串ID: {mapped_data['id']}")
    print(f"  - 规则名称: {mapped_data['name']}")
    print(f"  - 规则内容: {mapped_data['content'][:50]}...")
    print(f"  - 政策依据: {mapped_data['policy_basis']}")
    
    # 验证ID格式（应该是字符串格式的原始ID）
    if mapped_data['id'] == str(imported_rule_data['id']):
        print("✓ ID格式正确（使用原始数据库ID）")
        return True
    else:
        print("✗ ID格式错误")
        return False

def test_rule_data_creation_from_import():
    """测试从导入数据创建RuleData对象"""
    print("\n=== 测试从导入数据创建RuleData对象 ===")
    
    from models.rule import RuleData
    
    # 模拟导入的规则数据
    imported_data = {
        'id': 456,
        'rule_name': '从导入数据创建的规则',
        'rule_intension': 'SELECT * FROM imported_rule_table WHERE condition = "test"',
        'policy_basis': '基于导入数据的政策依据',
        'create_time': datetime.now()
    }
    
    # 直接使用原始数据库ID，转换为字符串
    rule_id_str = str(imported_data['id'])
    
    try:
        # 创建RuleData对象，映射导入数据结构
        rule_data = RuleData(
            id=rule_id_str,  # 使用原始数据库ID
            name=imported_data['rule_name'],
            content=imported_data['rule_intension'],
            description=imported_data['policy_basis'],
            category='导入规则',
            rule_type='重复收费',  # 模拟智能分析结果
            policy_basis=imported_data['policy_basis'],
            database_type='postgresql',
            patient_type='general',
            match_method='name'
        )
        
        print("✓ RuleData对象创建成功")
        print(f"  - ID: {rule_data.id} (原始数据库ID)")
        print(f"  - 名称: {rule_data.name}")
        print(f"  - 内容: {rule_data.content[:50]}...")
        print(f"  - 政策依据: {rule_data.policy_basis}")
        
        # 验证数据完整性
        if (rule_data.id and rule_data.name and rule_data.content and 
            rule_data.policy_basis and rule_data.rule_type):
            print("✓ 数据完整性验证通过")
            return True
        else:
            print("✗ 数据完整性验证失败")
            return False
            
    except Exception as e:
        print(f"✗ RuleData对象创建失败: {e}")
        return False

def test_rule_saving_with_imported_data():
    """测试使用导入数据保存规则"""
    print("\n=== 测试使用导入数据保存规则 ===")
    
    rule_service = RuleManagementService()
    
    # 模拟导入的规则数据
    imported_data = {
        'id': 789,
        'rule_name': '导入数据保存测试规则',
        'rule_intension': 'SELECT * FROM save_test_table WHERE id = 1',
        'policy_basis': '测试保存功能的政策依据',
        'create_time': datetime.now()
    }
    
    # 直接使用原始数据库ID，转换为字符串
    rule_id_str = str(imported_data['id'])
    
    try:
        from models.rule import RuleData
        
        # 创建RuleData对象
        rule_data = RuleData(
            id=rule_id_str,  # 使用原始数据库ID
            name=imported_data['rule_name'],
            content=imported_data['rule_intension'],
            description=imported_data['policy_basis'],
            category='导入规则',
            rule_type='超频次',
            policy_basis=imported_data['policy_basis'],
            database_type='postgresql',
            patient_type='general',
            match_method='name'
        )
        
        # 保存规则
        result = rule_service.save_rule(rule_data, allow_overwrite=True)
        print(f"✓ 规则保存成功: {result}")
        
        # 读取保存的规则验证
        saved_rule = rule_service.get_rule(imported_data['rule_name'])
        print(f"✓ 读取保存的规则成功")
        print(f"  - ID: {saved_rule.id} (原始数据库ID)")
        print(f"  - 名称: {saved_rule.name}")
        print(f"  - 内容: {saved_rule.content}")
        print(f"  - 政策依据: {saved_rule.policy_basis}")
        
        # 验证保存的数据与原始数据一致
        if (saved_rule.name == imported_data['rule_name'] and
            saved_rule.content == imported_data['rule_intension'] and
            saved_rule.policy_basis == imported_data['policy_basis'] and
            saved_rule.id == str(imported_data['id'])):  # 验证ID一致
            print("✓ 保存的数据与原始数据一致")
            return True
        else:
            print("✗ 保存的数据与原始数据不一致")
            return False
            
    except Exception as e:
        print(f"✗ 使用导入数据保存规则失败: {e}")
        return False

def test_json_compatibility_with_imported_data():
    """测试导入数据的JSON兼容性"""
    print("\n=== 测试导入数据的JSON兼容性 ===")
    
    # 模拟导入的规则数据
    imported_data = {
        'id': 999,
        'rule_name': 'JSON兼容性测试规则',
        'rule_intension': 'SELECT * FROM json_compatibility_test',
        'policy_basis': 'JSON兼容性测试政策依据',
        'create_time': datetime.now().isoformat()
    }
    
    # 直接使用原始数据库ID，转换为字符串
    rule_id_str = str(imported_data['id'])
    
    try:
        # 创建映射后的数据结构
        mapped_data = {
            'id': rule_id_str,  # 使用原始数据库ID
            'name': imported_data['rule_name'],
            'content': imported_data['rule_intension'],
            'description': imported_data['policy_basis'],
            'policy_basis': imported_data['policy_basis'],
            'rule_type': '限年龄',
            'database_type': 'postgresql',
            'create_time': imported_data['create_time']
        }
        
        # 测试JSON序列化
        json_str = json.dumps(mapped_data, ensure_ascii=False, indent=2)
        print("✓ JSON序列化成功")
        print(f"  - JSON长度: {len(json_str)} 字符")
        
        # 测试JSON反序列化
        parsed_data = json.loads(json_str)
        print("✓ JSON反序列化成功")
        
        # 验证关键字段
        expected_fields = ['id', 'name', 'content', 'description', 'policy_basis', 'rule_type', 'database_type']
        for field in expected_fields:
            if field in parsed_data:
                print(f"  ✓ 字段 '{field}' 存在")
            else:
                print(f"  ✗ 字段 '{field}' 缺失")
                return False
        
        # 验证ID格式（应该是字符串格式的原始ID）
        if parsed_data['id'] == str(imported_data['id']):
            print("✓ ID格式正确（使用原始数据库ID）")
            return True
        else:
            print("✗ ID格式错误")
            return False
            
    except Exception as e:
        print(f"✗ JSON兼容性测试失败: {e}")
        return False

def test_original_id_usage_for_imported_rules():
    """测试为导入规则使用原始数据库ID"""
    print("\n=== 测试为导入规则使用原始数据库ID ===")
    
    # 模拟多个导入规则
    imported_rules = [
        {'id': 1, 'rule_name': '导入规则1', 'rule_intension': 'SELECT * FROM rule1'},
        {'id': 2, 'rule_name': '导入规则2', 'rule_intension': 'SELECT * FROM rule2'},
        {'id': 3, 'rule_name': '导入规则3', 'rule_intension': 'SELECT * FROM rule3'},
    ]
    
    converted_ids = []
    
    try:
        for i, rule_data in enumerate(imported_rules):
            # 为每个导入规则使用原始数据库ID，转换为字符串
            rule_id_str = str(rule_data['id'])
            converted_ids.append(rule_id_str)
            
            print(f"✓ 规则 {i+1}: {rule_data['rule_name']} → 原始ID: {rule_id_str}")
        
        # 验证ID唯一性
        unique_ids = set(converted_ids)
        if len(unique_ids) == len(converted_ids):
            print(f"✓ 所有原始ID都是唯一的 ({len(converted_ids)} 个)")
        else:
            print("✗ 存在重复的原始ID")
            return False
        
        # 验证ID格式（应该是字符串格式的数字）
        for id_str in converted_ids:
            if id_str.isdigit():
                continue
            else:
                print(f"✗ ID格式错误: {id_str}")
                return False
        
        print("✓ 所有原始ID格式正确")
        return True
        
    except Exception as e:
        print(f"✗ 原始ID使用测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试规则导入功能与新的保存机制的兼容性...")
    print("=" * 70)
    
    tests = [
        ("规则导入服务初始化", test_rule_import_service_initialization),
        ("连接信息获取功能", test_connection_info_retrieval),
        ("导入数据结构映射", test_import_data_structure_mapping),
        ("从导入数据创建RuleData", test_rule_data_creation_from_import),
        ("使用导入数据保存规则", test_rule_saving_with_imported_data),
        ("导入数据的JSON兼容性", test_json_compatibility_with_imported_data),
        ("为导入规则使用原始ID", test_original_id_usage_for_imported_rules)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过\n")
            else:
                print(f"✗ {test_name} 测试失败\n")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}\n")
    
    print("=" * 70)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！规则导入功能与新的保存机制兼容性良好。")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 