"""
Intelligent SQL generation workflow service.
"""
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from models.rule import RuleData, TemplateInfo
from services.intelligent_template_service import IntelligentTemplateService, RuleAttributes
from services.template_service import TemplateManagementService
from services.sql_generation_service import SqlGenerationService
from services.rule_service import RuleManagementService
from utils.error_handler import BusinessError, ValidationError, SystemError


@dataclass
class WorkflowStep:
    """Workflow step definition."""
    step_id: str
    title: str
    description: str
    required: bool = True
    completed: bool = False
    data: Optional[Dict[str, Any]] = None


@dataclass
class WorkflowState:
    """Current state of the workflow."""
    workflow_id: str
    current_step: str
    steps: List[WorkflowStep]
    rule_data: Optional[RuleData] = None
    selected_template: Optional[TemplateInfo] = None
    template_parameters: Optional[Dict[str, Any]] = None
    generated_sql: Optional[str] = None


class IntelligentSqlWorkflowService:
    """Service for managing intelligent SQL generation workflow."""
    
    def __init__(self):
        self.intelligent_template_service = IntelligentTemplateService()
        self.template_service = TemplateManagementService()
        self.sql_generation_service = SqlGenerationService()
        self.rule_service = RuleManagementService()
        
        # Define workflow steps
        self.workflow_steps = [
            WorkflowStep(
                step_id="rule_attributes",
                title="规则属性设置",
                description="设置规则的基本属性，包括名称、类型、适用范围等"
            ),
            WorkflowStep(
                step_id="template_selection",
                title="模板选择",
                description="根据规则属性自动推荐合适的SQL模板"
            ),
            WorkflowStep(
                step_id="parameter_input",
                title="参数填写",
                description="填写模板所需的参数值"
            ),
            WorkflowStep(
                step_id="sql_preview",
                title="SQL预览",
                description="预览生成的SQL并进行必要的调整"
            ),
            WorkflowStep(
                step_id="rule_creation",
                title="规则创建",
                description="保存生成的规则"
            )
        ]
    
    def start_workflow(self, rule_name: str) -> WorkflowState:
        """
        Start a new intelligent SQL generation workflow.
        
        Args:
            rule_name: Name of the rule to create
            
        Returns:
            Initial workflow state
        """
        try:
            import uuid
            workflow_id = str(uuid.uuid4())
            
            # Initialize workflow steps
            steps = [WorkflowStep(**step.__dict__) for step in self.workflow_steps]
            
            # Analyze rule name for initial attributes
            analysis = self.intelligent_template_service.analyze_rule_attributes(rule_name)
            
            # Create initial rule data
            rule_data = RuleData(
                name=rule_name,
                content="",  # Will be generated later
                rule_type=analysis.get('detected_rule_type'),
                patient_type=analysis.get('detected_patient_type'),
                match_method=analysis.get('detected_match_method'),
                database_type=analysis.get('suggested_database_type', 'postgresql')
            )
            
            # Set initial step data
            steps[0].data = {
                "rule_name": rule_name,
                "suggested_attributes": analysis,
                "rule_data": rule_data.__dict__
            }
            
            workflow_state = WorkflowState(
                workflow_id=workflow_id,
                current_step="rule_attributes",
                steps=steps,
                rule_data=rule_data
            )
            
            return workflow_state
            
        except Exception as e:
            raise SystemError(f"Failed to start workflow: {str(e)}")
    
    def update_rule_attributes(self, workflow_state: WorkflowState, 
                             attributes: Dict[str, Any]) -> WorkflowState:
        """
        Update rule attributes in the workflow.
        
        Args:
            workflow_state: Current workflow state
            attributes: Updated rule attributes
            
        Returns:
            Updated workflow state
        """
        try:
            # Validate attributes
            temp_rule_data = RuleData(
                name=attributes.get('name', workflow_state.rule_data.name),
                content="temp",
                rule_type=attributes.get('rule_type'),
                patient_type=attributes.get('patient_type'),
                match_method=attributes.get('match_method'),
                database_type=attributes.get('database_type'),
                description=attributes.get('description'),
                category=attributes.get('category'),
                policy_basis=attributes.get('policy_basis')
            )
            
            validation_errors = self.rule_service.validate_rule_attributes(temp_rule_data)
            if validation_errors:
                raise ValidationError(
                    "Rule attribute validation failed",
                    error_code="INVALID_ATTRIBUTES",
                    details={"errors": validation_errors}
                )
            
            # Update rule data
            workflow_state.rule_data = temp_rule_data
            
            # Mark current step as completed
            current_step = self._get_current_step(workflow_state)
            if current_step:
                current_step.completed = True
                current_step.data = attributes
            
            # Move to next step
            workflow_state.current_step = "template_selection"
            
            # Get template recommendations for next step
            rule_attributes = RuleAttributes(
                rule_name=temp_rule_data.name,
                rule_type=temp_rule_data.rule_type,
                patient_type=temp_rule_data.patient_type,
                match_method=temp_rule_data.match_method,
                database_type=temp_rule_data.database_type
            )
            
            recommendations = self.intelligent_template_service.recommend_templates(
                rule_attributes, max_recommendations=10
            )
            
            # Update template selection step
            template_step = self._get_step_by_id(workflow_state, "template_selection")
            if template_step:
                template_step.data = {
                    "recommendations": [
                        {
                            "template": {
                                "id": rec.template.id,
                                "description": rec.template.description,
                                "category": rec.template.category,
                                "database_type": rec.template.database_type,
                                "patient_type": rec.template.patient_type
                            },
                            "score": rec.score,
                            "reasons": rec.reasons
                        } for rec in recommendations
                    ]
                }
            
            return workflow_state
            
        except Exception as e:
            raise SystemError(f"Failed to update rule attributes: {str(e)}")
    
    def select_template(self, workflow_state: WorkflowState, 
                       template_id: str) -> WorkflowState:
        """
        Select a template in the workflow.
        
        Args:
            workflow_state: Current workflow state
            template_id: ID of the selected template
            
        Returns:
            Updated workflow state
        """
        try:
            # Get template information
            template = self.template_service.get_template_by_id(template_id)
            workflow_state.selected_template = template
            
            # Mark current step as completed
            current_step = self._get_current_step(workflow_state)
            if current_step:
                current_step.completed = True
                current_step.data = {
                    "selected_template_id": template_id,
                    "template_info": {
                        "id": template.id,
                        "description": template.description,
                        "category": template.category
                    }
                }
            
            # Move to parameter input step
            workflow_state.current_step = "parameter_input"
            
            # Generate form configuration for parameters
            form_config = self.template_service.generate_dynamic_form_config(template_id)
            
            # Update parameter input step
            param_step = self._get_step_by_id(workflow_state, "parameter_input")
            if param_step:
                param_step.data = {
                    "form_config": form_config,
                    "template_parameters": template.parameters or []
                }
            
            return workflow_state
            
        except Exception as e:
            raise SystemError(f"Failed to select template: {str(e)}")
    
    def submit_parameters(self, workflow_state: WorkflowState, 
                         parameters: Dict[str, Any]) -> WorkflowState:
        """
        Submit template parameters and generate SQL.
        
        Args:
            workflow_state: Current workflow state
            parameters: Template parameter values
            
        Returns:
            Updated workflow state
        """
        try:
            if not workflow_state.selected_template:
                raise BusinessError("No template selected")
            
            # Validate parameters
            validation_result = self.sql_generation_service.validate_parameters(
                workflow_state.selected_template.id, parameters
            )
            
            if not validation_result.is_valid:
                raise ValidationError(
                    "Parameter validation failed",
                    error_code="INVALID_PARAMETERS",
                    details={"errors": validation_result.errors}
                )
            
            # Generate SQL
            generated_result = self.sql_generation_service.generate_sql(
                workflow_state.selected_template.id,
                parameters,
                workflow_state.rule_data.database_type or "postgresql"
            )
            
            workflow_state.template_parameters = parameters
            workflow_state.generated_sql = generated_result.sql
            
            # Update rule data with generated SQL
            workflow_state.rule_data.content = generated_result.sql
            workflow_state.rule_data.template_id = workflow_state.selected_template.id
            workflow_state.rule_data.parameters = parameters
            
            # Mark current step as completed
            current_step = self._get_current_step(workflow_state)
            if current_step:
                current_step.completed = True
                current_step.data = {
                    "parameters": parameters,
                    "validation_result": {
                        "is_valid": validation_result.is_valid,
                        "warnings": validation_result.warnings
                    }
                }
            
            # Move to SQL preview step
            workflow_state.current_step = "sql_preview"
            
            # Update SQL preview step
            preview_step = self._get_step_by_id(workflow_state, "sql_preview")
            if preview_step:
                preview_step.data = {
                    "generated_sql": generated_result.sql,
                    "template_id": workflow_state.selected_template.id,
                    "parameters": parameters,
                    "can_edit": True
                }
            
            return workflow_state
            
        except Exception as e:
            raise SystemError(f"Failed to submit parameters: {str(e)}")
    
    def update_sql(self, workflow_state: WorkflowState, sql_content: str) -> WorkflowState:
        """
        Update the generated SQL content.
        
        Args:
            workflow_state: Current workflow state
            sql_content: Updated SQL content
            
        Returns:
            Updated workflow state
        """
        try:
            workflow_state.generated_sql = sql_content
            workflow_state.rule_data.content = sql_content
            
            # Update SQL preview step
            preview_step = self._get_step_by_id(workflow_state, "sql_preview")
            if preview_step and preview_step.data:
                preview_step.data["generated_sql"] = sql_content
                preview_step.data["manually_edited"] = True
            
            return workflow_state
            
        except Exception as e:
            raise SystemError(f"Failed to update SQL: {str(e)}")
    
    def finalize_rule(self, workflow_state: WorkflowState) -> Dict[str, Any]:
        """
        Finalize and save the rule.
        
        Args:
            workflow_state: Current workflow state
            
        Returns:
            Rule creation result
        """
        try:
            if not workflow_state.rule_data or not workflow_state.generated_sql:
                raise BusinessError("Incomplete workflow state")
            
            # Final validation
            if not workflow_state.rule_data.validate():
                raise ValidationError("Rule data validation failed")
            
            # Save the rule
            result = self.rule_service.create_rule_with_validation(workflow_state.rule_data)
            
            # Mark sql_preview step as completed if not already
            sql_preview_step = self._get_step_by_id(workflow_state, "sql_preview")
            if sql_preview_step and not sql_preview_step.completed:
                sql_preview_step.completed = True
            
            # Mark final step as completed
            final_step = self._get_step_by_id(workflow_state, "rule_creation")
            if final_step:
                final_step.completed = True
                final_step.data = {
                    "rule_name": workflow_state.rule_data.name,
                    "creation_result": result
                }
            
            workflow_state.current_step = "completed"
            
            return {
                "workflow_id": workflow_state.workflow_id,
                "rule_name": workflow_state.rule_data.name,
                "creation_result": result,
                "workflow_completed": True
            }
            
        except Exception as e:
            raise SystemError(f"Failed to finalize rule: {str(e)}")
    
    def get_workflow_summary(self, workflow_state: WorkflowState) -> Dict[str, Any]:
        """
        Get a summary of the current workflow state.
        
        Args:
            workflow_state: Current workflow state
            
        Returns:
            Workflow summary
        """
        return {
            "workflow_id": workflow_state.workflow_id,
            "current_step": workflow_state.current_step,
            "progress": {
                "completed_steps": len([s for s in workflow_state.steps if s.completed]),
                "total_steps": len(workflow_state.steps),
                "percentage": int((len([s for s in workflow_state.steps if s.completed]) / len(workflow_state.steps)) * 100)
            },
            "rule_info": {
                "name": workflow_state.rule_data.name if workflow_state.rule_data else None,
                "rule_type": workflow_state.rule_data.rule_type if workflow_state.rule_data else None,
                "patient_type": workflow_state.rule_data.patient_type if workflow_state.rule_data else None,
                "database_type": workflow_state.rule_data.database_type if workflow_state.rule_data else None
            },
            "selected_template": {
                "id": workflow_state.selected_template.id if workflow_state.selected_template else None,
                "description": workflow_state.selected_template.description if workflow_state.selected_template else None
            } if workflow_state.selected_template else None,
            "has_generated_sql": bool(workflow_state.generated_sql),
            "steps": [
                {
                    "step_id": step.step_id,
                    "title": step.title,
                    "description": step.description,
                    "completed": step.completed,
                    "is_current": step.step_id == workflow_state.current_step
                } for step in workflow_state.steps
            ]
        }
    
    def _get_current_step(self, workflow_state: WorkflowState) -> Optional[WorkflowStep]:
        """Get the current workflow step."""
        return next((step for step in workflow_state.steps if step.step_id == workflow_state.current_step), None)
    
    def _get_step_by_id(self, workflow_state: WorkflowState, step_id: str) -> Optional[WorkflowStep]:
        """Get a workflow step by ID."""
        return next((step for step in workflow_state.steps if step.step_id == step_id), None)
    
    def preview_sql_with_parameters(self, template_id: str, parameters: Dict[str, Any], 
                                   database_type: str = "postgresql") -> str:
        """
        Preview SQL generation without full validation.
        
        Args:
            template_id: Template ID
            parameters: Template parameters
            database_type: Database type
            
        Returns:
            Preview SQL
        """
        try:
            return self.sql_generation_service.preview_sql(template_id, parameters, database_type)
        except Exception as e:
            return f"-- Error generating preview: {str(e)}"
    
    def get_template_recommendations_for_rule(self, rule_name: str, 
                                            rule_attributes: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Get template recommendations for a rule.
        
        Args:
            rule_name: Name of the rule
            rule_attributes: Optional rule attributes
            
        Returns:
            List of template recommendations
        """
        try:
            if rule_attributes:
                attrs = RuleAttributes(
                    rule_name=rule_name,
                    rule_type=rule_attributes.get('rule_type'),
                    patient_type=rule_attributes.get('patient_type'),
                    match_method=rule_attributes.get('match_method'),
                    database_type=rule_attributes.get('database_type')
                )
            else:
                attrs = self.intelligent_template_service._extract_attributes_from_name(rule_name)
            
            recommendations = self.intelligent_template_service.recommend_templates(attrs, max_recommendations=10)
            
            return [
                {
                    "template": {
                        "id": rec.template.id,
                        "description": rec.template.description,
                        "category": rec.template.category,
                        "database_type": rec.template.database_type,
                        "patient_type": rec.template.patient_type
                    },
                    "score": rec.score,
                    "reasons": rec.reasons,
                    "matches": {
                        "category_match": rec.category_match,
                        "name_match": rec.name_match,
                        "type_match": rec.type_match
                    }
                } for rec in recommendations
            ]
            
        except Exception as e:
            raise SystemError(f"Failed to get template recommendations: {str(e)}")