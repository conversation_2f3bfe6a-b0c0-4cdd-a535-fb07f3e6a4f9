#!/usr/bin/env python3
"""
测试新建规则时使用UUID的逻辑
验证只有新建规则时才生成UUID，导入规则使用原始数据库ID
"""

import sys
import os
import uuid
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.rule import RuleData, Rule
from services.rule_service import RuleManagementService
from services.rule_import_service import RuleImportService

def test_new_rule_uuid_generation():
    """测试新建规则时生成UUID"""
    print("=== 测试新建规则时生成UUID ===")
    
    rule_service = RuleManagementService()
    
    # 创建新的规则数据（不包含id字段，让系统自动生成UUID）
    new_rule_data = RuleData(
        name="新建规则测试",
        content="SELECT * FROM new_rule_table WHERE status = 'active'",
        description="测试新建规则UUID生成",
        category="测试分类",
        rule_type="超频次",
        database_type="postgresql"
    )
    
    try:
        # 保存新规则
        result = rule_service.save_rule(new_rule_data, allow_overwrite=True)
        print(f"✓ 新建规则保存成功: {result}")
        
        # 读取保存的规则验证
        saved_rule = rule_service.get_rule("新建规则测试")
        print(f"✓ 读取新建规则成功")
        print(f"  - ID: {saved_rule.id}")
        print(f"  - 名称: {saved_rule.name}")
        print(f"  - 内容: {saved_rule.content[:50]}...")
        
        # 验证是否生成了UUID
        if saved_rule.id and len(saved_rule.id) == 36 and saved_rule.id.count('-') == 4:
            print("✓ 新建规则生成了正确的UUID格式")
            return True
        else:
            print("✗ 新建规则UUID格式错误")
            return False
            
    except Exception as e:
        print(f"✗ 新建规则UUID生成测试失败: {e}")
        return False

def test_imported_rule_original_id():
    """测试导入规则使用原始数据库ID"""
    print("\n=== 测试导入规则使用原始数据库ID ===")
    
    rule_service = RuleManagementService()
    
    # 模拟导入的规则数据
    imported_data = {
        'id': 12345,  # 原始数据库ID
        'rule_name': '导入规则测试',
        'rule_intension': 'SELECT * FROM imported_table WHERE id = 12345',
        'policy_basis': '基于原始数据库ID的导入规则',
        'create_time': datetime.now()
    }
    
    # 使用原始数据库ID
    rule_id_str = str(imported_data['id'])
    
    try:
        # 创建RuleData对象，使用原始数据库ID
        rule_data = RuleData(
            id=rule_id_str,  # 使用原始数据库ID
            name=imported_data['rule_name'],
            content=imported_data['rule_intension'],
            description=imported_data['policy_basis'],
            category='导入规则',
            rule_type='重复收费',
            policy_basis=imported_data['policy_basis'],
            database_type='postgresql',
            patient_type='general',
            match_method='name'
        )
        
        # 保存导入的规则
        result = rule_service.save_rule(rule_data, allow_overwrite=True)
        print(f"✓ 导入规则保存成功: {result}")
        
        # 读取保存的规则验证
        saved_rule = rule_service.get_rule("导入规则测试")
        print(f"✓ 读取导入规则成功")
        print(f"  - ID: {saved_rule.id} (原始数据库ID)")
        print(f"  - 名称: {saved_rule.name}")
        print(f"  - 内容: {saved_rule.content[:50]}...")
        
        # 验证使用的是原始数据库ID
        if saved_rule.id == str(imported_data['id']):
            print("✓ 导入规则正确使用了原始数据库ID")
            return True
        else:
            print("✗ 导入规则ID不匹配")
            return False
            
    except Exception as e:
        print(f"✗ 导入规则原始ID测试失败: {e}")
        return False

def test_id_format_comparison():
    """测试UUID和原始ID的格式对比"""
    print("\n=== 测试UUID和原始ID的格式对比 ===")
    
    # 生成UUID
    generated_uuid = str(uuid.uuid4())
    
    # 原始数据库ID
    original_id = "12345"
    
    print(f"UUID格式: {generated_uuid}")
    print(f"  - 长度: {len(generated_uuid)} 字符")
    print(f"  - 连字符数量: {generated_uuid.count('-')}")
    print(f"  - 格式正确: {len(generated_uuid) == 36 and generated_uuid.count('-') == 4}")
    
    print(f"原始ID格式: {original_id}")
    print(f"  - 长度: {len(original_id)} 字符")
    print(f"  - 是否为数字: {original_id.isdigit()}")
    print(f"  - 格式正确: {original_id.isdigit()}")
    
    # 验证格式
    uuid_correct = len(generated_uuid) == 36 and generated_uuid.count('-') == 4
    original_id_correct = original_id.isdigit()
    
    if uuid_correct and original_id_correct:
        print("✓ UUID和原始ID格式都正确")
        return True
    else:
        print("✗ 格式验证失败")
        return False

def test_rule_service_id_handling():
    """测试规则服务对ID的处理逻辑"""
    print("\n=== 测试规则服务对ID的处理逻辑 ===")
    
    rule_service = RuleManagementService()
    
    # 测试1: 新建规则（无ID字段）
    new_rule = RuleData(
        name="无ID新建规则",
        content="SELECT * FROM no_id_table",
        description="测试无ID字段的新建规则"
    )
    
    try:
        result1 = rule_service.save_rule(new_rule, allow_overwrite=True)
        saved_rule1 = rule_service.get_rule("无ID新建规则")
        print(f"✓ 无ID新建规则: {saved_rule1.id}")
        
        # 测试2: 导入规则（有ID字段）
        imported_rule = RuleData(
            id="67890",  # 原始数据库ID
            name="有ID导入规则",
            content="SELECT * FROM with_id_table",
            description="测试有ID字段的导入规则"
        )
        
        result2 = rule_service.save_rule(imported_rule, allow_overwrite=True)
        saved_rule2 = rule_service.get_rule("有ID导入规则")
        print(f"✓ 有ID导入规则: {saved_rule2.id}")
        
        # 验证处理逻辑
        # 无ID的新建规则应该生成UUID（36位长度）
        # 有ID的导入规则应该保持原始ID
        if (saved_rule1.id and len(saved_rule1.id) == 36 and 
            saved_rule2.id == "67890"):
            print("✓ 规则服务正确处理了不同的ID情况")
            return True
        else:
            print("✗ 规则服务ID处理逻辑错误")
            return False
            
    except Exception as e:
        print(f"✗ 规则服务ID处理测试失败: {e}")
        return False

def test_backward_compatibility_with_id():
    """测试带ID字段的向后兼容性"""
    print("\n=== 测试带ID字段的向后兼容性 ===")
    
    rule_service = RuleManagementService()
    
    # 测试旧格式规则（无ID字段）
    old_format_rule = RuleData(
        name="旧格式规则",
        content="SELECT * FROM old_format_table",
        description="测试旧格式规则"
    )
    
    try:
        # 保存旧格式规则
        result = rule_service.save_rule(old_format_rule, allow_overwrite=True)
        print(f"✓ 旧格式规则保存成功: {result}")
        
        # 读取规则验证
        saved_rule = rule_service.get_rule("旧格式规则")
        print(f"  - ID: {saved_rule.id} (应该为None)")
        print(f"  - 名称: {saved_rule.name}")
        
        if saved_rule.id is None:
            print("✓ 旧格式规则向后兼容性测试通过")
            return True
        else:
            print("✗ 旧格式规则向后兼容性测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 向后兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试新建规则UUID生成和导入规则原始ID使用...")
    print("=" * 70)
    
    tests = [
        ("新建规则UUID生成", test_new_rule_uuid_generation),
        ("导入规则原始ID使用", test_imported_rule_original_id),
        ("UUID和原始ID格式对比", test_id_format_comparison),
        ("规则服务ID处理逻辑", test_rule_service_id_handling),
        ("带ID字段的向后兼容性", test_backward_compatibility_with_id)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过\n")
            else:
                print(f"✗ {test_name} 测试失败\n")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}\n")
    
    print("=" * 70)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新建规则UUID生成和导入规则原始ID使用逻辑正确。")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 