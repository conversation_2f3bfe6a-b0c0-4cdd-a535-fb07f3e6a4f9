#!/usr/bin/env python3
"""
测试完整的功能
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:5000"

def test_page_access():
    """测试页面访问"""
    print("=== 测试页面访问 ===")
    
    pages = [
        ("主页", "/"),
        ("连接管理页面", "/page/database_connections"),
        ("测试页面", "/test_frontend_connection.html")
    ]
    
    for name, path in pages:
        try:
            response = requests.get(f"{BASE_URL}{path}")
            if response.status_code == 200:
                print(f"✓ {name} 可以正常访问")
            else:
                print(f"✗ {name} 访问失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ {name} 访问失败: {str(e)}")

def test_connection_management():
    """测试连接管理功能"""
    print("\n=== 测试连接管理功能 ===")
    
    # 保存测试连接
    test_connections = [
        {
            "name": "**************数据库",
            "description": "************** 数据库连接",
            "database_type": "postgresql",
            "host": "**************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        },
        {
            "name": "*************数据库",
            "description": "************* 数据库连接",
            "database_type": "postgresql",
            "host": "*************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        }
    ]
    
    saved_ids = []
    
    try:
        # 1. 保存连接
        print("1. 保存测试连接...")
        for conn in test_connections:
            response = requests.post(f"{BASE_URL}/api/database/connections", json=conn)
            result = response.json()
            
            if result.get('success'):
                saved_ids.append(result['connection']['id'])
                print(f"   ✓ 保存连接: {conn['name']} ({conn['host']})")
            else:
                print(f"   ✗ 保存连接失败: {conn['name']} - {result.get('error')}")
        
        # 2. 验证连接已保存
        print("2. 验证连接已保存...")
        response = requests.get(f"{BASE_URL}/api/database/connections")
        result = response.json()
        
        if result.get('success'):
            connections = result.get('connections', [])
            print(f"   ✓ 找到 {len(connections)} 个保存的连接")
            
            for conn in connections:
                print(f"   - {conn['name']}: {conn['host']}:{conn['port']}")
        else:
            print(f"   ✗ 获取连接失败: {result.get('error')}")
        
        # 3. 清理测试数据
        print("3. 清理测试数据...")
        for conn_id in saved_ids:
            response = requests.delete(f"{BASE_URL}/api/database/connections/{conn_id}")
            result = response.json()
            
            if result.get('success'):
                print(f"   ✓ 删除连接成功: {conn_id}")
            else:
                print(f"   ✗ 删除连接失败: {conn_id} - {result.get('error')}")
        
        print("✓ 连接管理功能测试完成！")
        
    except Exception as e:
        print(f"✗ 连接管理测试失败: {str(e)}")

def test_rule_import_with_saved_connection():
    """测试使用保存的连接进行规则导入"""
    print("\n=== 测试使用保存的连接进行规则导入 ===")
    
    # 先保存一个连接
    test_connection = {
        "name": "测试导入连接",
        "description": "用于测试规则导入的连接",
        "database_type": "postgresql",
        "host": "localhost",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        # 1. 保存连接
        print("1. 保存测试连接...")
        response = requests.post(f"{BASE_URL}/api/database/connections", json=test_connection)
        result = response.json()
        
        if result.get('success'):
            connection_id = result['connection']['id']
            print(f"   ✓ 连接保存成功，ID: {connection_id}")
            
            # 2. 使用保存的连接扫描规则
            print("2. 使用保存的连接扫描规则...")
            response = requests.post(f"{BASE_URL}/api/rules/import", json={
                "connection_id": connection_id
            })
            result = response.json()
            
            if result.get('success'):
                rules = result.get('rules', [])
                print(f"   ✓ 扫描成功，找到 {len(rules)} 个可导入规则")
                
                if rules:
                    # 3. 执行导入
                    print("3. 执行规则导入...")
                    selected_rules = [rules[0]['id']]  # 选择第一个规则
                    
                    response = requests.post(f"{BASE_URL}/api/rules/import/execute", json={
                        "selected_rules": selected_rules,
                        "overwrite": False,
                        "connection_id": connection_id
                    })
                    result = response.json()
                    
                    if result.get('success'):
                        print(f"   ✓ 导入成功: {result.get('imported_count', 0)} 个规则")
                    else:
                        print(f"   ✗ 导入失败: {result.get('error')}")
                else:
                    print("   ℹ 没有找到可导入的规则")
            else:
                print(f"   ✗ 扫描失败: {result.get('error')}")
            
            # 4. 清理：删除测试连接
            requests.delete(f"{BASE_URL}/api/database/connections/{connection_id}")
            print(f"   ✓ 清理测试连接完成")
        else:
            print(f"   ✗ 连接保存失败: {result.get('error')}")
            
    except Exception as e:
        print(f"✗ 规则导入测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试完整功能...")
    print("=" * 60)
    
    try:
        # 测试页面访问
        test_page_access()
        
        # 测试连接管理功能
        test_connection_management()
        
        # 测试规则导入功能
        test_rule_import_with_saved_connection()
        
        print("\n" + "=" * 60)
        print("✓ 所有功能测试完成！")
        print("\n功能验证总结：")
        print("✅ 页面访问正常")
        print("✅ 连接管理功能正常")
        print("✅ 规则导入功能正常")
        print("✅ 数据库连接配置持久化正常")
        print("✅ 两阶段导入流程正常")
        
        print("\n用户操作指南：")
        print("1. 访问主页: http://localhost:5000")
        print("2. 访问连接管理: http://localhost:5000/page/database_connections")
        print("3. 在主页点击'导入规则'，选择'使用保存的连接'")
        print("4. 如果看不到连接，请清除浏览器缓存（Ctrl+Shift+R）")
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保服务器正在运行")
        print("启动服务器命令: python app.py")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main() 