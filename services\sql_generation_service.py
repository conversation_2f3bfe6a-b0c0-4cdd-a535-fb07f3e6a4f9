"""
SQL generation service for creating parameterized SQL from templates.
"""
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from jinja2 import Template, Environment, BaseLoader, TemplateError
from models.rule import TemplateInfo, TemplateParameter, GenerationRequest
from services.template_service import TemplateManagementService
from utils.error_handler import BusinessError, SystemError


@dataclass
class ValidationResult:
    """Parameter validation result."""
    is_valid: bool
    errors: List[str]
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


@dataclass
class GeneratedSQL:
    """Generated SQL result."""
    sql: str
    template_id: str
    parameters: Dict[str, Any]
    database_type: str
    validation_result: Optional[ValidationResult] = None


class SqlGenerationService:
    """Service for generating SQL from templates with parameter validation."""
    
    def __init__(self):
        self.template_service = TemplateManagementService()
        self.jinja_env = Environment(loader=BaseLoader())
        
        # Configure Jinja2 for SQL generation
        self.jinja_env.block_start_string = '{%'
        self.jinja_env.block_end_string = '%}'
        self.jinja_env.variable_start_string = '{{'
        self.jinja_env.variable_end_string = '}}'
        self.jinja_env.comment_start_string = '{#'
        self.jinja_env.comment_end_string = '#}'
    
    def generate_sql(self, template_id: str, parameters: Dict[str, Any], 
                    database_type: str = "postgresql") -> GeneratedSQL:
        """Generate SQL from template with given parameters."""
        try:
            # Get template
            template_info = self.template_service.get_template_by_id(template_id)
            
            # Validate parameters
            validation_result = self.validate_parameters(template_id, parameters)
            if not validation_result.is_valid:
                raise BusinessError(
                    f"Parameter validation failed: {', '.join(validation_result.errors)}",
                    error_code="PARAMETER_VALIDATION_ERROR",
                    details={"errors": validation_result.errors}
                )
            
            # Convert template to use Jinja2 syntax if needed
            jinja_template_content = self._convert_to_jinja2_syntax(template_info.sql)
            
            # Apply database-specific transformations
            jinja_template_content = self._apply_database_dialect(jinja_template_content, database_type)
            
            # Create Jinja2 template
            jinja_template = self.jinja_env.from_string(jinja_template_content)
            
            # Process parameters for SQL generation
            processed_params = self._process_parameters(parameters, template_info.parameters)
            
            # Generate SQL
            generated_sql = jinja_template.render(**processed_params)
            
            # Clean up generated SQL
            generated_sql = self._clean_generated_sql(generated_sql)
            
            return GeneratedSQL(
                sql=generated_sql,
                template_id=template_id,
                parameters=parameters,
                database_type=database_type,
                validation_result=validation_result
            )
            
        except TemplateError as e:
            raise BusinessError(
                f"Template rendering error: {str(e)}",
                error_code="TEMPLATE_RENDER_ERROR",
                details={"template_id": template_id, "error": str(e)}
            )
        except Exception as e:
            raise SystemError(f"Failed to generate SQL: {str(e)}")
    
    def validate_parameters(self, template_id: str, parameters: Dict[str, Any]) -> ValidationResult:
        """Validate parameters against template requirements."""
        try:
            template_info = self.template_service.get_template_by_id(template_id)
            errors = []
            warnings = []
            
            # Get required parameters from template
            template_params = template_info.parameters or []
            required_param_names = {param.name for param in template_params if param.required}
            
            # Check for missing required parameters
            provided_param_names = set(parameters.keys())
            missing_params = required_param_names - provided_param_names
            
            for missing_param in missing_params:
                errors.append(f"Required parameter '{missing_param}' is missing")
            
            # Validate individual parameters
            for param in template_params:
                if param.name in parameters:
                    param_value = parameters[param.name]
                    param_errors = self._validate_parameter_value(param, param_value)
                    errors.extend(param_errors)
            
            # Check for unexpected parameters
            expected_param_names = {param.name for param in template_params}
            unexpected_params = provided_param_names - expected_param_names
            
            for unexpected_param in unexpected_params:
                warnings.append(f"Unexpected parameter '{unexpected_param}' provided")
            
            return ValidationResult(
                is_valid=len(errors) == 0,
                errors=errors,
                warnings=warnings
            )
            
        except Exception as e:
            raise SystemError(f"Failed to validate parameters: {str(e)}")
    
    def _validate_parameter_value(self, param: TemplateParameter, value: Any) -> List[str]:
        """Validate a single parameter value."""
        errors = []
        
        if value is None or (isinstance(value, str) and not value.strip()):
            if param.required:
                errors.append(f"Parameter '{param.name}' cannot be empty")
            return errors
        
        # Type-specific validation
        if param.parameter_type == "number":
            try:
                float(str(value))
            except ValueError:
                errors.append(f"Parameter '{param.name}' must be a valid number")
        
        elif param.parameter_type == "list":
            if isinstance(value, str):
                # Check if it's a comma-separated list
                if not value.strip():
                    errors.append(f"Parameter '{param.name}' cannot be empty")
            elif not isinstance(value, (list, tuple)):
                errors.append(f"Parameter '{param.name}' must be a list or comma-separated string")
        
        elif param.parameter_type == "enum":
            if param.name == "性别" and value not in ["男", "女"]:
                errors.append(f"Parameter '{param.name}' must be either '男' or '女'")
        
        return errors
    
    def _convert_to_jinja2_syntax(self, template_content: str) -> str:
        """Convert {parameter} syntax to {{parameter}} Jinja2 syntax."""
        # Replace {parameter} with {{parameter}} for Jinja2
        # But be careful not to replace SQL syntax like {ts '2023-01-01'}
        
        def replace_parameter(match):
            param_name = match.group(1)
            # Skip if it looks like SQL syntax (contains spaces or special chars)
            if ' ' in param_name or any(char in param_name for char in ["'", '"', ":"]):
                return match.group(0)
            return f"{{{{{param_name}}}}}"
        
        # Use regex to find and replace parameters
        pattern = r'\{([^}]+)\}'
        converted = re.sub(pattern, replace_parameter, template_content)
        
        return converted
    
    def _apply_database_dialect(self, template_content: str, database_type: str) -> str:
        """Apply database-specific SQL dialect transformations."""
        if database_type.lower() == "oracle":
            # Oracle-specific transformations
            # Convert PostgreSQL-style date casting to Oracle format
            template_content = re.sub(r'::DATE', '', template_content)
            template_content = re.sub(r'~\*', 'REGEXP_LIKE', template_content)
            
        elif database_type.lower() == "postgresql":
            # PostgreSQL-specific transformations (already in correct format)
            pass
        
        return template_content
    
    def _process_parameters(self, parameters: Dict[str, Any], 
                          template_params: List[TemplateParameter]) -> Dict[str, Any]:
        """Process parameters for SQL generation."""
        processed = {}
        
        for param_name, param_value in parameters.items():
            # Find parameter definition
            param_def = next((p for p in template_params if p.name == param_name), None)
            
            if param_def and param_def.parameter_type == "list":
                # Handle list parameters
                if isinstance(param_value, str):
                    # Split comma-separated string and format for SQL IN clause
                    items = [item.strip() for item in param_value.split(',') if item.strip()]
                    # Quote each item for SQL
                    quoted_items = [f"'{item}'" for item in items]
                    processed[param_name] = ','.join(quoted_items)
                elif isinstance(param_value, (list, tuple)):
                    # Quote each item for SQL
                    quoted_items = [f"'{item}'" for item in param_value]
                    processed[param_name] = ','.join(quoted_items)
                else:
                    processed[param_name] = f"'{param_value}'"
            
            elif param_def and param_def.parameter_type == "number":
                # Handle numeric parameters
                processed[param_name] = str(param_value)
            
            else:
                # Handle text parameters
                processed[param_name] = str(param_value)
        
        return processed
    
    def _clean_generated_sql(self, sql: str) -> str:
        """Clean up the generated SQL."""
        # Remove extra whitespace
        lines = sql.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Remove trailing whitespace
            cleaned_line = line.rstrip()
            # Skip empty lines at the beginning and end
            if cleaned_line or cleaned_lines:
                cleaned_lines.append(cleaned_line)
        
        # Remove trailing empty lines
        while cleaned_lines and not cleaned_lines[-1]:
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)
    
    def get_template_parameters(self, template_id: str) -> List[TemplateParameter]:
        """Get parameters for a specific template."""
        try:
            template_info = self.template_service.get_template_by_id(template_id)
            return template_info.parameters or []
        except Exception as e:
            raise SystemError(f"Failed to get template parameters: {str(e)}")
    
    def preview_sql(self, template_id: str, parameters: Dict[str, Any], 
                   database_type: str = "postgresql") -> str:
        """Preview generated SQL without full validation."""
        try:
            template_info = self.template_service.get_template_by_id(template_id)
            
            # Convert template to Jinja2 syntax
            jinja_template_content = self._convert_to_jinja2_syntax(template_info.sql)
            jinja_template_content = self._apply_database_dialect(jinja_template_content, database_type)
            
            # Create Jinja2 template
            jinja_template = self.jinja_env.from_string(jinja_template_content)
            
            # Process parameters
            processed_params = self._process_parameters(parameters, template_info.parameters or [])
            
            # Generate SQL
            generated_sql = jinja_template.render(**processed_params)
            
            return self._clean_generated_sql(generated_sql)
            
        except Exception as e:
            return f"-- Error generating preview: {str(e)}\n{template_info.sql if 'template_info' in locals() else ''}"