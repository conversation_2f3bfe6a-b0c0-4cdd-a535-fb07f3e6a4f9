﻿"""
Rule template selector service based on rule attributes.
"""
import os
import logging
from typing import Dict, Any, <PERSON><PERSON>, Tuple, List
from utils.config import config


class RuleTemplateSelector:
    """Service for selecting appropriate templates based on rule attributes."""
    
    def __init__(self):
        self.templates_base_dir = config.TEMPLATES_DIR
        self.logger = logging.getLogger(__name__)
        self._template_cache = {}
        self._load_all_templates()
    
    def _load_all_templates(self):
        """Load all templates from the templates directory."""
        try:
            if not os.path.exists(self.templates_base_dir):
                self.logger.warning(f"Templates directory not found: {self.templates_base_dir}")
                return
            
            for folder_name in os.listdir(self.templates_base_dir):
                folder_path = os.path.join(self.templates_base_dir, folder_name)
                if os.path.isdir(folder_path):
                    self._load_templates_from_folder(folder_name, folder_path)
                    
        except Exception as e:
            self.logger.error(f"Error loading templates: {str(e)}")
    
    def _load_templates_from_folder(self, folder_name: str, folder_path: str):
        """Load templates from a specific folder."""
        try:
            for filename in os.listdir(folder_path):
                if filename.endswith('.sql'):
                    file_path = os.path.join(folder_path, filename)
                    template_name = os.path.splitext(filename)[0]
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Store template with folder context
                    template_key = f"{folder_name}/{template_name}"
                    self._template_cache[template_key] = content
                    
                    # Also store by template name for backward compatibility
                    if template_name not in self._template_cache:
                        self._template_cache[template_name] = content
                        
        except Exception as e:
            self.logger.error(f"Error loading templates from folder {folder_name}: {str(e)}")
    
    def get_available_templates(self) -> Dict[str, str]:
        """Get all available templates."""
        return self._template_cache.copy()
    
    def select_template(self, rule: Dict[str, Any], db_type: str = "pg", 
                       code_type: str = "name", patient_type: str = "inpatient") -> Tuple[Optional[str], Optional[str]]:
        """Select template based on rule attributes."""
        try:
            rule_type = rule.get("类型", "")
            folder_name = f"rule_{db_type}_{code_type}_{patient_type}"
            
            # Simple template selection logic
            if rule_type == "重复收费":
                template_name = "重复收费"
            elif rule_type == "超住院天数":
                template_name = "超住院天数"
            else:
                template_name = rule_type
            
            # Try to get template from specific folder
            template_key = f"{folder_name}/{template_name}"
            if template_key in self._template_cache:
                return self._template_cache[template_key], template_name
            
            # Fallback to any template with same name
            if template_name in self._template_cache:
                return self._template_cache[template_name], template_name
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Error selecting template: {str(e)}")
            return None, None
    
    def _get_template_from_folder(self, template_name: str, folder_name: str) -> Tuple[Optional[str], Optional[str]]:
        """Get template from a specific folder."""
        try:
            template_key = f"{folder_name}/{template_name}"
            if template_key in self._template_cache:
                return self._template_cache[template_key], template_name
            
            # Try to load from file if not in cache
            folder_path = os.path.join(self.templates_base_dir, folder_name)
            if os.path.exists(folder_path):
                file_path = os.path.join(folder_path, f"{template_name}.sql")
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    self._template_cache[template_key] = content
                    return content, template_name
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Error getting template from folder: {str(e)}")
            return None, None
    
    def _get_template(self, template_name: str) -> Tuple[Optional[str], Optional[str]]:
        """Get template by name from any folder."""
        try:
            # First check if template exists in cache
            if template_name in self._template_cache:
                return self._template_cache[template_name], template_name
            
            # Search in all folders
            for folder_name in os.listdir(self.templates_base_dir):
                folder_path = os.path.join(self.templates_base_dir, folder_name)
                if os.path.isdir(folder_path):
                    file_path = os.path.join(folder_path, f"{template_name}.sql")
                    if os.path.exists(file_path):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        self._template_cache[template_name] = content
                        return content, template_name
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Error getting template: {str(e)}")
            return None, None
    
    def get_template_info(self, template_key: str) -> Dict[str, Any]:
        """Get template information."""
        try:
            content = self._template_cache.get(template_key, "")
            
            # Extract description from first line comment
            first_line = content.split('\n', 1)[0] if content else ""
            description = first_line.replace('--', '').strip() if first_line.startswith('--') else template_key
            
            return {
                'id': template_key,
                'name': template_key.split('/')[-1] if '/' in template_key else template_key,
                'description': description,
                'folder': template_key.split('/')[0] if '/' in template_key else 'unknown',
                'content': content
            }
            
        except Exception as e:
            self.logger.error(f"Error getting template info: {str(e)}")
            return {
                'id': template_key,
                'name': template_key,
                'description': 'Unknown template',
                'folder': 'unknown',
                'content': ''
            }