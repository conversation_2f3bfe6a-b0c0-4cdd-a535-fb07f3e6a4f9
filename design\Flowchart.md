# 用户操作流程图

本文档使用 Mermaid 语法描述了“临时规则编写工具”的核心用户操作流程。

## 核心流程

```mermaid
graph TD
    A[用户访问首页] --> B{选择操作};
    B --> C[规则管理];
    B --> D[规则SQL生成器];

    subgraph 规则管理流程
        C --> C1[查看规则列表];
        C1 --> C2{需要新规则?};
        C2 -- 是 --> D;
        C2 -- 否 --> C3[筛选/搜索规则];
        C3 --> C4[编辑或删除现有规则];
        C4 --> C1;
    end

    subgraph SQL生成器流程
        D --> D1[填写规则表单];
        D1 --> D2[生成SQL预览];
        D2 --> D3{SQL是否满意?};
        D3 -- 是 --> D4[添加到规则列表];
        D3 -- 否 --> D1;
        D4 --> C1;
    end
```

## 流程说明

1.  **起始点**: 用户通过 `index.html` 进入应用，可以看到“规则管理”和“规则SQL生成器”两个主要功能区的预览。
2.  **规则管理**: 
    *   用户可以直接进入“规则管理”界面查看、搜索、筛选、编辑或删除已有的规则。
    *   如果发现需要一个全新的规则，可以从“规则管理”界面跳转到“规则SQL生成器”。
3.  **规则SQL生成**: 
    *   用户在“规则SQL生成器”中通过表单定义新规则的参数。
    *   系统根据用户的输入和选择的模板实时生成SQL代码供预览。
    *   用户对生成的SQL满意后，可以将其一键“添加到规则列表”，这条新规则便会出现在“规则管理”界面中，完成闭环。