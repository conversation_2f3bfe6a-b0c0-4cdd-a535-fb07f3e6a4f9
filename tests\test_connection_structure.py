#!/usr/bin/env python3
"""
测试数据库连接信息结构
"""

import requests
import json

def test_connection_structure():
    """测试数据库连接信息结构"""
    base_url = "http://localhost:5000"
    
    print("🧪 测试数据库连接信息结构...")
    
    try:
        # 获取数据库连接
        response = requests.get(f"{base_url}/api/database/connections")
        if response.status_code == 200:
            data = response.json()
            connections = data.get('connections', [])
            print(f"   ✅ 成功获取 {len(connections)} 个数据库连接")
            
            if connections:
                # 显示第一个连接的结构
                connection = connections[0]
                print(f"   📋 连接ID: {connection.get('id')}")
                print(f"   📋 连接名称: {connection.get('name')}")
                print(f"   📋 主机: {connection.get('host')}")
                print(f"   📋 端口: {connection.get('port')}")
                print(f"   📋 数据库: {connection.get('database')}")
                print(f"   📋 用户名: {connection.get('username')}")
                print(f"   📋 密码: {connection.get('password', '***')}")
                print(f"   📋 数据库类型: {connection.get('database_type')}")
                print(f"   📋 描述: {connection.get('description')}")
                
                # 显示完整的连接信息
                print(f"   📄 完整连接信息: {json.dumps(connection, indent=2, ensure_ascii=False)}")
            else:
                print("   ⚠️  没有可用的数据库连接")
        else:
            print(f"   ❌ 获取数据库连接失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    test_connection_structure() 