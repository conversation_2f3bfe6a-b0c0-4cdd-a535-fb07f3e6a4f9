#!/usr/bin/env python3
"""
测试规则导入功能的脚本
"""
import requests
import json
import sys

# 测试配置
BASE_URL = "http://localhost:5000"

def test_database_connections():
    """测试获取数据库连接列表"""
    print("测试获取数据库连接列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/database/connections")
        data = response.json()
        
        if data.get('success'):
            print(f"✓ 成功获取 {len(data.get('connections', []))} 个数据库连接")
            return data.get('connections', [])
        else:
            print(f"✗ 获取数据库连接失败: {data.get('error')}")
            return []
    except Exception as e:
        print(f"✗ 请求失败: {str(e)}")
        return []

def test_connection_check():
    """测试数据库连接检查"""
    print("\n测试数据库连接检查...")
    
    # 先获取连接列表
    connections = test_database_connections()
    if not connections:
        print("没有可用的数据库连接，跳过连接检查测试")
        return
    
    # 使用第一个连接进行测试
    connection = connections[0]
    print(f"使用连接: {connection.get('name', 'Unknown')}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/rules/check-database-connection",
            json={
                'connection_id': connection['id']
            }
        )
        data = response.json()
        
        if data.get('success'):
            print("✓ 数据库连接检查成功")
        else:
            print(f"✗ 数据库连接检查失败: {data.get('error')}")
    except Exception as e:
        print(f"✗ 连接检查请求失败: {str(e)}")

def test_rule_export():
    """测试规则导出功能"""
    print("\n测试规则导出功能...")
    
    # 先获取连接列表
    connections = test_database_connections()
    if not connections:
        print("没有可用的数据库连接，跳过导出测试")
        return
    
    # 使用第一个连接进行测试
    connection = connections[0]
    print(f"使用连接: {connection.get('name', 'Unknown')}")
    
    # 模拟选中的规则
    selected_rules = ["测试规则1", "测试规则2"]
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/rules/export-to-database",
            json={
                'selected_rules': selected_rules,
                'connection_id': connection['id']
            }
        )
        data = response.json()
        
        if data.get('success'):
            print(f"✓ 规则导出成功")
            print(f"  导出数量: {data.get('exported_count', 0)}")
            print(f"  更新数量: {data.get('updated_count', 0)}")
            print(f"  失败数量: {data.get('failed_count', 0)}")
        else:
            print(f"✗ 规则导出失败: {data.get('error')}")
    except Exception as e:
        print(f"✗ 导出请求失败: {str(e)}")

def test_rule_list():
    """测试获取规则列表"""
    print("\n测试获取规则列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/rules")
        data = response.json()
        
        if 'rules' in data:
            print(f"✓ 成功获取 {len(data['rules'])} 个规则")
            if data['rules']:
                print("  示例规则:")
                for rule in data['rules'][:3]:  # 显示前3个规则
                    print(f"    - {rule.get('name', 'Unknown')}")
        else:
            print("✗ 获取规则列表失败")
    except Exception as e:
        print(f"✗ 获取规则列表请求失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试规则导入功能...")
    print("=" * 50)
    
    # 测试各个功能
    test_rule_list()
    test_database_connections()
    test_connection_check()
    test_rule_export()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main() 