#!/usr/bin/env python3
"""
测试医保搜索API是否正常工作
"""
import requests
import json

def test_medical_search_api():
    """测试医保搜索API"""
    print("=== 测试医保搜索API ===")
    
    # 测试数据
    test_data = {
        'search_term': '血常规',
        'database': 'pg',
        'host': 'default',
        'schema': 'NJS_YY_NJDYSGCYY_5KU',
        'limit': 10
    }
    
    try:
        print(f"发送请求到: http://localhost:5000/api/medical-insurance/search")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        
        # 发送请求
        response = requests.post(
            'http://localhost:5000/api/medical-insurance/search',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            
            if response.status_code == 200 and response_data.get('success'):
                print("✅ API调用成功")
                return True
            elif response.status_code == 404 and response_data.get('error') == '找不到医保对照表':
                print("✅ API正常工作（表不存在，返回404是正常的）")
                return True
            else:
                print(f"❌ API返回错误: {response_data.get('error', '未知错误')}")
                return False
        except json.JSONDecodeError as e:
            print(f"❌ 响应不是有效的JSON: {e}")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 无法连接到服务器，请确保应用程序正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def test_api_endpoint_exists():
    """测试API端点是否存在"""
    print("\n=== 测试API端点是否存在 ===")
    
    try:
        # 测试GET请求（应该返回405 Method Not Allowed）
        response = requests.get('http://localhost:5000/api/medical-insurance/search', timeout=5)
        print(f"GET请求状态码: {response.status_code}")
        
        if response.status_code == 405:
            print("✅ API端点存在（正确返回Method Not Allowed）")
            return True
        else:
            print(f"❌ 意外的GET响应: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试医保搜索API...")
    
    # 测试API端点是否存在
    endpoint_ok = test_api_endpoint_exists()
    
    # 测试API功能
    api_ok = test_medical_search_api()
    
    print("\n=== 测试结果总结 ===")
    print(f"API端点存在: {'✓' if endpoint_ok else '✗'}")
    print(f"API功能正常: {'✓' if api_ok else '✗'}")
    
    if endpoint_ok and api_ok:
        print("\n🎉 API测试通过！")
        print("如果前端仍然无法调用，请检查：")
        print("1. 浏览器控制台是否有JavaScript错误")
        print("2. 网络请求是否被阻止")
        print("3. CORS设置是否正确")
        return True
    else:
        print("\n❌ API测试失败，需要检查后端代码")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 