<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-900 text-gray-300 font-sans p-6">
    <div class="container mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-white">规则管理</h1>
            <div class="flex items-center space-x-4">
                <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i> 新建规则
                </button>
                <button id="importRulesBtn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-upload mr-2"></i> 导入规则
                </button>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-gray-800 p-4 rounded-lg mb-6 flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                    <input type="text" id="searchInput" placeholder="搜索规则名称、ID或描述..." class="bg-gray-700 border border-gray-600 rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500 w-80">
                </div>
                <select id="sourceFilter" class="bg-gray-700 border border-gray-600 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有来源</option>
                    <option value="manual">手动创建</option>
                    <option value="imported">导入规则</option>
                </select>
                <select id="statusFilter" class="bg-gray-700 border border-gray-600 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">所有状态</option>
                    <option value="active">已启用</option>
                    <option value="inactive">已禁用</option>
                </select>
            </div>
            <button id="clearFiltersBtn" class="text-gray-400 hover:text-white">
                <i class="fas fa-filter-circle-xmark mr-1"></i> 清除筛选
            </button>
        </div>

        <!-- Rules Table -->
        <div class="bg-gray-800 rounded-lg overflow-hidden">
            <table class="w-full text-sm text-left text-gray-400">
                <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                    <tr>
                        <th scope="col" class="p-4">
                            <input type="checkbox" id="selectAllCheckbox" class="bg-gray-600 border-gray-500 rounded">
                        </th>
                        <th scope="col" class="px-6 py-3">规则名称</th>
                        <th scope="col" class="px-6 py-3">来源</th>
                        <th scope="col" class="px-6 py-3">状态</th>
                        <th scope="col" class="px-6 py-3">创建日期</th>
                        <th scope="col" class="px-6 py-3">最后修改</th>
                        <th scope="col" class="px-6 py-3 text-center">操作</th>
                    </tr>
                </thead>
                <tbody id="rulesTableBody">
                    <!-- 规则数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="flex justify-between items-center mt-6">
            <span class="text-sm text-gray-400">显示 <span id="currentRange" class="font-semibold text-white">1-10</span> of <span id="totalCount" class="font-semibold text-white">0</span></span>
            <div class="inline-flex -space-x-px rounded-md shadow-sm" id="pagination">
                <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- Import Rules Modal -->
    <div id="importModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-white">导入规则到数据库</h3>
                        <button id="closeImportModal" class="text-gray-400 hover:text-white">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <!-- Step 1: Select Database Connection -->
                    <div id="step1" class="mb-6">
                        <h4 class="text-lg font-semibold text-white mb-4">步骤 1: 选择目标数据库</h4>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-300 mb-2">数据库连接</label>
                            <select id="databaseConnection" class="w-full bg-gray-700 border border-gray-600 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择数据库连接...</option>
                            </select>
                        </div>
                        <div class="flex space-x-4">
                            <button id="testConnectionBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                                <i class="fas fa-plug mr-2"></i> 测试连接
                            </button>
                            <button id="nextStepBtn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg">
                                下一步
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 2: Preview and Confirm -->
                    <div id="step2" class="mb-6 hidden">
                        <h4 class="text-lg font-semibold text-white mb-4">步骤 2: 预览和确认</h4>
                        <div class="mb-4">
                            <div class="bg-gray-700 rounded-lg p-4">
                                <h5 class="font-semibold text-white mb-2">选中的规则</h5>
                                <div id="selectedRulesPreview" class="space-y-2">
                                    <!-- 选中的规则预览 -->
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-4">
                            <button id="backToStep1Btn" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                                上一步
                            </button>
                            <button id="confirmImportBtn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg">
                                <i class="fas fa-upload mr-2"></i> 确认导入
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 3: Import Progress -->
                    <div id="step3" class="mb-6 hidden">
                        <h4 class="text-lg font-semibold text-white mb-4">步骤 3: 导入进度</h4>
                        <div class="mb-4">
                            <div class="bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-full bg-gray-600 rounded-full h-2 mr-2">
                                        <div id="importProgress" class="bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <span id="progressText" class="text-sm text-gray-300">0%</span>
                                </div>
                                <div id="importStatus" class="text-sm text-gray-300">准备导入...</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Import Results -->
                    <div id="importResults" class="hidden">
                        <h4 class="text-lg font-semibold text-white mb-4">导入结果</h4>
                        <div id="importResultsContent" class="bg-gray-700 rounded-lg p-4">
                            <!-- 导入结果内容 -->
                        </div>
                        <div class="flex justify-end mt-4">
                            <button id="closeImportResultsBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                                关闭
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/rule_management.js"></script>
</body>
</html> 