#!/usr/bin/env python3
"""
测试应用程序启动
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要的模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试配置模块
        from utils.config import config
        print("✓ utils.config 导入成功")
        
        # 测试错误处理模块
        from utils.error_handler import register_error_handlers
        print("✓ utils.error_handler 导入成功")
        
        # 测试控制器模块
        from controllers.database_controller import database_bp
        print("✓ controllers.database_controller 导入成功")
        
        from controllers.rule_controller import RuleController
        print("✓ controllers.rule_controller 导入成功")
        
        # 测试服务模块
        from services.rule_import_service import RuleImportService
        print("✓ services.rule_import_service 导入成功")
        
        # 测试模型模块
        from models.rule import RuleData
        print("✓ models.rule 导入成功")
        
        print("\n所有模块导入成功！")
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {str(e)}")
        return False

def test_flask_app():
    """测试Flask应用创建"""
    print("\n测试Flask应用创建...")
    
    try:
        from app import app
        print("✓ Flask应用创建成功")
        
        # 测试路由注册
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(rule.rule)
        
        print(f"✓ 注册了 {len(routes)} 个路由")
        
        # 检查关键API路由
        key_routes = [
            '/api/database/connections',
            '/api/rules/import',
            '/api/rules/import/execute'
        ]
        
        for route in key_routes:
            if any(route in r for r in routes):
                print(f"✓ 找到路由: {route}")
            else:
                print(f"✗ 缺少路由: {route}")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask应用创建失败: {str(e)}")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n测试配置加载...")
    
    try:
        from utils.config import config
        
        # 测试数据库配置加载
        db_config = config.load_database_config()
        print("✓ 数据库配置加载成功")
        
        # 测试数据库连接配置加载
        connections = config.load_database_connections()
        print(f"✓ 数据库连接配置加载成功，找到 {len(connections)} 个连接")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置加载失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试应用程序启动...")
    print("=" * 50)
    
    success = True
    
    # 测试模块导入
    if not test_imports():
        success = False
    
    # 测试Flask应用
    if not test_flask_app():
        success = False
    
    # 测试配置加载
    if not test_config_loading():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 所有测试通过！应用程序可以正常启动")
    else:
        print("✗ 部分测试失败，需要修复问题")
    
    return success

if __name__ == "__main__":
    main() 