<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端连接加载测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-8">
    <h1 class="text-2xl font-bold mb-6">前端连接加载测试</h1>
    
    <div class="space-y-6">
        <!-- 连接列表 -->
        <div>
            <h2 class="text-lg font-semibold mb-3">已保存的连接</h2>
            <div id="connections-list" class="bg-gray-800 rounded-lg p-4">
                <p class="text-gray-400">正在加载连接...</p>
            </div>
        </div>
        
        <!-- 连接选择器 -->
        <div>
            <h2 class="text-lg font-semibold mb-3">连接选择器</h2>
            <select id="connection-select" class="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                <option value="">选择保存的连接</option>
            </select>
        </div>
        
        <!-- 测试按钮 -->
        <div>
            <h2 class="text-lg font-semibold mb-3">测试操作</h2>
            <div class="space-x-4">
                <button onclick="loadConnections()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                    重新加载连接
                </button>
                <button onclick="testConnectionSelection()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors">
                    测试连接选择
                </button>
            </div>
        </div>
        
        <!-- 日志输出 -->
        <div>
            <h2 class="text-lg font-semibold mb-3">日志输出</h2>
            <div id="log-output" class="bg-gray-800 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
                <p class="text-gray-400">日志将在这里显示...</p>
            </div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : type === 'success' ? 'text-green-400' : 'text-gray-300';
            
            logOutput.innerHTML += `<p class="${color}">[${timestamp}] ${message}</p>`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // 加载连接列表
        async function loadConnections() {
            log('开始加载连接列表...');
            
            try {
                const response = await fetch('/api/database/connections');
                const data = await response.json();
                
                log(`API响应: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success && data.connections) {
                    const connections = data.connections;
                    log(`成功加载 ${connections.length} 个连接`);
                    
                    // 更新连接列表显示
                    updateConnectionsList(connections);
                    
                    // 更新连接选择器
                    updateConnectionSelect(connections);
                    
                    log('连接加载完成', 'success');
                } else {
                    log(`加载失败: ${data.error}`, 'error');
                }
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        // 更新连接列表显示
        function updateConnectionsList(connections) {
            const container = document.getElementById('connections-list');
            
            if (connections.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-gray-400">
                        <p>暂无保存的数据库连接</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = connections.map(connection => `
                <div class="border border-gray-700 rounded-lg p-3 mb-3">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-semibold">${connection.name}</h3>
                            <p class="text-sm text-gray-400">${connection.host}:${connection.port}</p>
                            <p class="text-sm text-gray-400">数据库: ${connection.database}</p>
                            <p class="text-sm text-gray-400">用户: ${connection.username}</p>
                        </div>
                        <div class="text-xs text-gray-500">
                            ID: ${connection.id}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 更新连接选择器
        function updateConnectionSelect(connections) {
            const select = document.getElementById('connection-select');
            select.innerHTML = '<option value="">选择保存的连接</option>';
            
            connections.forEach(connection => {
                const option = document.createElement('option');
                option.value = connection.id;
                option.textContent = `${connection.name} (${connection.host}:${connection.port})`;
                select.appendChild(option);
            });
            
            log(`更新选择器，添加了 ${connections.length} 个选项`);
        }

        // 测试连接选择
        function testConnectionSelection() {
            const select = document.getElementById('connection-select');
            const selectedValue = select.value;
            const selectedText = select.options[select.selectedIndex]?.text;
            
            if (selectedValue) {
                log(`选择了连接: ${selectedText} (ID: ${selectedValue})`, 'success');
            } else {
                log('未选择任何连接', 'error');
            }
        }

        // 页面加载时自动加载连接
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始加载连接...');
            loadConnections();
        });
    </script>
</body>
</html> 