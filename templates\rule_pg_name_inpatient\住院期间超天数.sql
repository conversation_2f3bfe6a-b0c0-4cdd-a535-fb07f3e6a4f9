WITH TAB1 AS (
SELECT 
    结算单据号, 
    COUNT(DISTINCT 项目使用日期) 使用天数,
    (COUNT(DISTINCT 项目使用日期) -{违规数量}) 违规天数
FROM (
    SELECT 
        结算单据号, 
        to_char(项目使用日期, 'YYYY-MM-DD') AS 项目使用日期  
    FROM 医保住院结算明细
    WHERE 医保项目名称 in ({医保名称1})
    GROUP BY 结算单据号, to_char(项目使用日期, 'YYYY-MM-DD')
    HAVING SUM(数量) > 0 
    ) B
GROUP BY 结算单据号
HAVING COUNT(DISTINCT 项目使用日期) > {违规数量}
)
SELECT
  A.病案号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.住院号,
  A.个人编码,
  A.患者社会保障号码,
  A.身份证号,
  A.险种类型,
  A.入院科室,
  A.出院科室,
  A.主诊医师姓名,
  A<PERSON>患者姓名,
  A.患者年龄,
  A.异地标志,
  A<PERSON>入院日期,
  A.出院日期,
 (a.出院日期 :: DATE) - (a.入院日期 :: DATE) as 住院天数,
  A.医疗总费用,
  A.基本统筹支付,
  A.个人自付,
  A.个人自费,
  A.符合基本医疗保险的费用,
  A.入院诊断编码,
  A.入院诊断名称,
  A.出院诊断编码,
  A.出院诊断名称,
  A.主手术及操作编码,
  A.主手术及操作名称,
  A.其他手术及操作编码,
  A.其他手术及操作名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.项目使用日期,
  B.结算日期,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例,
  --A.记账流水号,
  b.数量 AS 使用数量,
  b.金额 AS 使用金额,
  b.医保范围内金额 AS 医保范围内总金额,
  CASE WHEN ROW_NUMBER() OVER (PARTITION BY B.结算单据号,B.医保项目名称 ORDER BY B.结算单据号,B.医保项目名称) =1 THEN C.违规天数 ELSE 0 END AS 违规天数
FROM
  医保住院结算明细 b
  join tab1 c on b.结算单据号 = c.结算单据号  
 JOIN 医保住院结算主单 a ON a.结算单据号 = b.结算单据号
 WHERE
 b.医保项目名称 in ({医保名称1})
  and 1=1
  and 2=2
  and 3=3
  and 4=4  
ORDER BY
  A.结算单据号

