<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则SQL生成器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-900 text-gray-300 font-sans">
    <div class="flex h-screen">
        <!-- Left Panel: Form -->
        <div class="w-1/2 p-8 overflow-y-auto">
            <div class="mb-6 flex justify-between items-center">
                <h1 class="text-2xl font-bold text-white">规则SQL生成器</h1>
                <button class="text-gray-400 hover:text-white text-sm"><i class="fas fa-history mr-1"></i> 加载草稿</button>
            </div>

            <form class="space-y-6">
                <div>
                    <label for="rule-name" class="block mb-2 text-sm font-medium text-gray-300">规则名称</label>
                    <input type="text" id="rule-name" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" placeholder="例如：限定支付-限单人多次">
                </div>

                <div>
                    <label for="rule-type" class="block mb-2 text-sm font-medium text-gray-300">规则类型</label>
                    <select id="rule-type" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        <option selected>限定支付范围</option>
                        <option>项目内涵</option>
                        <option>分解住院</option>
                        <option>超标准收费</option>
                    </select>
                </div>

                <div>
                    <label for="rule-template" class="block mb-2 text-sm font-medium text-gray-300">SQL模板</label>
                    <select id="rule-template" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        <option selected>模板A：按病人分组</option>
                        <option>模板B：按就诊记录分组</option>
                        <option>模板C：跨表关联查询</option>
                    </select>
                </div>

                <div class="border-t border-gray-700 pt-6">
                    <h3 class="text-lg font-semibold text-white mb-4">规则条件</h3>
                    <div class="space-y-4" id="conditions-container">
                        <!-- Dynamic conditions will be added here -->
                        <div class="flex items-center space-x-2">
                            <select class="bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-1/3">
                                <option>项目编码</option>
                                <option>药品编码</option>
                                <option>诊断编码</option>
                            </select>
                            <select class="bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-1/4">
                                <option>等于</option>
                                <option>不等于</option>
                                <option>包含</option>
                            </select>
                            <input type="text" class="bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-1/3" placeholder="值">
                            <button type="button" class="text-gray-400 hover:text-red-500"><i class="fas fa-trash"></i></button>
                        </div>
                    </div>
                    <button type="button" id="add-condition" class="mt-4 text-blue-500 hover:text-blue-400 text-sm font-medium"><i class="fas fa-plus-circle mr-2"></i>添加条件</button>
                </div>

                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-700">
                    <button type="button" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">重置</button>
                    <button type="button" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg">生成SQL</button>
                </div>
            </form>
        </div>

        <!-- Right Panel: SQL Preview -->
        <div class="w-1/2 bg-gray-800 p-8 flex flex-col">
            <div class="flex justify-between items-center mb-4">
                 <h2 class="text-xl font-semibold text-white">SQL预览</h2>
                 <button id="copy-sql" class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm"><i class="fas fa-copy mr-2"></i> 复制</button>
            </div>
            <div class="bg-gray-900 rounded-lg flex-grow p-1 relative">
                <pre><code id="sql-output" class="language-sql h-full block">-- 在左侧表单中填写信息以生成SQL...</code></pre>
            </div>
             <div class="mt-6 flex justify-end">
                 <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg flex items-center"><i class="fas fa-check-circle mr-2"></i> 完成并添加到规则列表</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', (event) => {
            hljs.highlightAll();

            const sqlOutput = document.getElementById('sql-output');
            const generateBtn = document.querySelector('.bg-green-600');

            generateBtn.addEventListener('click', () => {
                const ruleName = document.getElementById('rule-name').value || '示例规则';
                const code = `
-- 规则名称: ${ruleName}
-- 生成时间: ${new Date().toISOString()}

SELECT
    patient_id,
    COUNT(DISTINCT visit_id) AS total_visits
FROM
    medical_records
WHERE
    record_type = 'outpatient'
    AND diagnosis_code LIKE 'J45%'
    AND record_date >= '2023-01-01'
GROUP BY
    patient_id
HAVING
    COUNT(DISTINCT visit_id) > 5;
                `;
                sqlOutput.textContent = code.trim();
                hljs.highlightElement(sqlOutput);
            });

            const copyBtn = document.getElementById('copy-sql');
            copyBtn.addEventListener('click', () => {
                navigator.clipboard.writeText(sqlOutput.textContent).then(() => {
                    copyBtn.innerHTML = '<i class="fas fa-check mr-2"></i> 已复制!';
                    setTimeout(() => {
                         copyBtn.innerHTML = '<i class="fas fa-copy mr-2"></i> 复制';
                    }, 2000);
                });
            });
        });
    </script>
</body>
</html>