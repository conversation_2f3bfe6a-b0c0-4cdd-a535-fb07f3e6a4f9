  WITH tab1 AS (
	SELECT 
		A.结算单据号,
		项目使用日期:: DATE 项目使用日期,
    SUM(金额) AS 使用总金额,
    (SUM(金额)-'{违规金额}')::NUMERIC AS 违规金额  
	FROM
		医保门诊结算明细 B
		JOIN 医保住院结算主单 A ON A.结算单据号 = B.结算单据号 
	WHERE 医保项目名称 in ({医保名称1})
	GROUP BY
		A.结算单据号,项目使用日期:: DATE 
	HAVING
		SUM (B.金额)>'{违规金额}'
	) 
  SELECT 
  A.门诊号,
  A.结算单据号,
  A.医疗机构编码,
  A.医疗机构名称,
  A.结算日期,
  A.个人编码,
  A.患者社会保障号码,
  A.险种类型,
  A.科室名称,
  A.医师名称,
  A.患者姓名,
  A.患者性别,
  A.患者出生日期,
  A.患者年龄,
  A.医疗类别,
  A.异地标志,
  A.医疗总费用,
  A.基本统筹支付,
  A.现金支付,
  A.个人账户支付,
  A.符合基本医疗保险的费用,
  A.诊断编码,
  A.诊断名称,
  B.开单科室名称,
  B.执行科室名称,
  B.开单医师姓名,
  B.费用类别,
  B.项目使用日期,
  B.医院项目编码,
  B.医院项目名称,
  B.医保项目编码,
  B.医保项目名称,
  B.规格,
  B.单价,
  B.支付类别,
  B.报销比例,
  B.自付比例,
  B.支付地点类别,
  B.记账流水号,
  B.规格,
  B.单价,
  B.数量,
  B.金额,
  B.医保范围内金额,
  CASE WHEN ROW_NUMBER() OVER (PARTITION BY B.结算单据号,B.医保项目名称,B.项目使用日期::DATE ORDER BY B.结算单据号,B.项目使用日期::DATE,B.医保项目名称) =1 THEN C.使用总金额 ELSE 0 END AS 使用总金额,
  CASE WHEN ROW_NUMBER() OVER (PARTITION BY B.结算单据号,B.医保项目名称,B.项目使用日期::DATE ORDER BY B.结算单据号,B.项目使用日期::DATE,B.医保项目名称) =1 THEN C.违规金额 ELSE 0 END AS 违规金额
FROM
	医保门诊结算明细 B
	JOIN 医保门诊结算主单 A ON A.结算单据号 = B.结算单据号
	JOIN tab1 C ON B.结算单据号 = C.结算单据号 
	AND C.项目使用日期 = B.项目使用日期 :: DATE 
WHERE  b.医保项目名称 in ({医保名称1})
  and 1=1
  and 2=2
  and 3=3
  and 4=4  
ORDER BY
  A.结算单据号,
  B.项目使用日期
 