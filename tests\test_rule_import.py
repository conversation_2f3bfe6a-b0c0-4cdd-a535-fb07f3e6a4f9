#!/usr/bin/env python3
"""
测试规则导入功能
"""
import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_rule_import_scan():
    """测试规则导入扫描功能"""
    print("=== 测试规则导入扫描功能 ===")
    
    test_data = {
        "host": "localhost",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/rules/import", 
                               json=test_data, timeout=30)
        result = response.json()
        print(f"扫描结果: {result}")
        
        if result.get('success'):
            print(f"找到 {result.get('count', 0)} 个可导入的规则")
            if result.get('rules'):
                print("规则列表:")
                for rule in result['rules'][:3]:  # 只显示前3个
                    print(f"  - ID: {rule.get('id')}, 名称: {rule.get('rule_name')}")
        else:
            print(f"扫描失败: {result.get('error')}")
        
        return result.get('success', False)
    except Exception as e:
        print(f"扫描失败: {e}")
        return False

def test_rule_import_execute():
    """测试规则导入执行功能"""
    print("\n=== 测试规则导入执行功能 ===")
    
    # 首先扫描可导入的规则
    scan_data = {
        "host": "localhost",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        scan_response = requests.post(f"{BASE_URL}/api/rules/import", 
                                    json=scan_data, timeout=30)
        scan_result = scan_response.json()
        
        if not scan_result.get('success'):
            print(f"扫描失败: {scan_result.get('error')}")
            return False
        
        rules = scan_result.get('rules', [])
        if not rules:
            print("没有找到可导入的规则")
            return True
        
        # 选择前2个规则进行导入测试
        selected_rules = [rule['id'] for rule in rules[:2]]
        
        execute_data = {
            "selected_rules": selected_rules,
            "overwrite": False,
            "keep_original": True,
            "host": "localhost",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        }
        
        execute_response = requests.post(f"{BASE_URL}/api/rules/import/execute", 
                                       json=execute_data, timeout=60)
        execute_result = execute_response.json()
        
        print(f"导入结果: {execute_result}")
        
        if execute_result.get('success'):
            print(f"成功导入 {execute_result.get('imported_count', 0)} 个规则")
            if execute_result.get('failed_count', 0) > 0:
                print(f"失败 {execute_result.get('failed_count')} 个规则")
                for failed_rule in execute_result.get('failed_rules', []):
                    print(f"  - {failed_rule}")
        else:
            print(f"导入失败: {execute_result.get('error')}")
        
        return execute_result.get('success', False)
        
    except Exception as e:
        print(f"导入执行失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试无效的数据库连接
    invalid_data = {
        "host": "invalid-host",
        "port": 5432,
        "database": "invalid-db",
        "username": "invalid-user",
        "password": "invalid-password"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/rules/import", 
                               json=invalid_data, timeout=10)
        result = response.json()
        print(f"错误处理测试结果: {result}")
        
        if not result.get('success'):
            print("✓ 错误处理正常")
            return True
        else:
            print("✗ 错误处理异常")
            return False
            
    except Exception as e:
        print(f"错误处理测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试规则导入功能...")
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(3)
    
    # 测试各项功能
    scan_ok = test_rule_import_scan()
    execute_ok = test_rule_import_execute()
    error_ok = test_error_handling()
    
    # 输出测试结果
    print("\n=== 测试结果汇总 ===")
    print(f"规则扫描测试: {'✓' if scan_ok else '✗'}")
    print(f"规则导入测试: {'✓' if execute_ok else '✗'}")
    print(f"错误处理测试: {'✓' if error_ok else '✗'}")
    
    if all([scan_ok, execute_ok, error_ok]):
        print("\n所有测试通过！规则导入功能已成功实现。")
    else:
        print("\n部分测试失败，请检查功能实现。")

if __name__ == "__main__":
    main() 