<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>数据库API测试页面</h1>
    
    <div class="test-section">
        <h2>1. 获取数据库Schema列表</h2>
        <div class="form-group">
            <label for="dbType">数据库类型:</label>
            <select id="dbType">
                <option value="pg">PostgreSQL</option>
                <option value="oracle">Oracle</option>
            </select>
        </div>
        <div class="form-group">
            <label for="hostInput">主机地址 (留空使用默认):</label>
            <input type="text" id="hostInput" placeholder="例如: *************">
        </div>
        <button onclick="testGetSchemas()">获取Schema列表</button>
        <div id="schemaResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>2. 执行SQL查询</h2>
        <div class="form-group">
            <label for="sqlInput">SQL语句:</label>
            <textarea id="sqlInput" rows="4" placeholder="SELECT * FROM your_table LIMIT 5">SELECT 1 as test_column, 'Hello World' as message</textarea>
        </div>
        <div class="form-group">
            <label for="execDbType">数据库类型:</label>
            <select id="execDbType">
                <option value="pg">PostgreSQL</option>
                <option value="oracle">Oracle</option>
            </select>
        </div>
        <div class="form-group">
            <label for="execHost">主机地址 (留空使用默认):</label>
            <input type="text" id="execHost" placeholder="例如: *************">
        </div>
        <div class="form-group">
            <label for="execSchema">Schema (可选):</label>
            <input type="text" id="execSchema" placeholder="例如: public">
        </div>
        <button onclick="testExecuteSQL()">执行SQL</button>
        <div id="sqlResult" class="result" style="display: none;"></div>
    </div>

    <script>
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
            element.textContent = JSON.stringify(data, null, 2);
        }

        function testGetSchemas() {
            const database = document.getElementById('dbType').value;
            const host = document.getElementById('hostInput').value.trim() || 'default';
            
            fetch('/api/database_schemas', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    database: database,
                    host: host
                })
            })
            .then(response => response.json())
            .then(data => {
                showResult('schemaResult', data, data.success);
            })
            .catch(error => {
                showResult('schemaResult', { error: error.message }, false);
            });
        }

        function testExecuteSQL() {
            const sql = document.getElementById('sqlInput').value.trim();
            const database = document.getElementById('execDbType').value;
            const host = document.getElementById('execHost').value.trim() || 'default';
            const schema = document.getElementById('execSchema').value.trim();
            
            if (!sql) {
                showResult('sqlResult', { error: 'SQL语句不能为空' }, false);
                return;
            }
            
            fetch('/api/rules/execute_sql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sql: sql,
                    database: database,
                    host: host,
                    schema: schema
                })
            })
            .then(response => response.json())
            .then(data => {
                showResult('sqlResult', data, data.success);
            })
            .catch(error => {
                showResult('sqlResult', { error: error.message }, false);
            });
        }
    </script>
</body>
</html> 