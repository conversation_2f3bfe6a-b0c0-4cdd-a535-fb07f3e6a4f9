"""
Template management service for handling SQL templates.
"""
import os
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from models.rule import TemplateInfo, TemplateParameter
from utils.config import config
from utils.error_handler import SystemError, BusinessError


@dataclass
class TemplateCategory:
    """Template category model."""
    id: str
    name: str
    description: str
    database_type: str
    patient_type: str  # inpatient, outpatient, general


class TemplateManagementService:
    """Service class for managing SQL templates with enhanced categorization and parameter parsing."""
    
    def __init__(self):
        self.templates_base_dir = config.TEMPLATES_DIR
        self.templates_rule_dir = config.TEMPLATES_RULE_DIR
        self._template_cache = {}
        self._template_list_cache = {}
        self._category_cache = {}
        self._cache_enabled = True
        self._category_mapping = self._build_category_mapping()
        self._ensure_default_template()
    
    def _build_category_mapping(self) -> Dict[str, TemplateCategory]:
        """Build mapping of template directory names to categories."""
        return {
            "rule": TemplateCategory(
                id="rule",
                name="通用规则模板",
                description="通用的医保规则模板",
                database_type="general",
                patient_type="general"
            ),
            "rule_pg_name_inpatient": TemplateCategory(
                id="rule_pg_name_inpatient",
                name="PostgreSQL住院规则(按名称)",
                description="PostgreSQL数据库住院患者规则模板，使用项目名称匹配",
                database_type="postgresql",
                patient_type="inpatient"
            ),
            "rule_pg_name_outpatient": TemplateCategory(
                id="rule_pg_name_outpatient",
                name="PostgreSQL门诊规则(按名称)",
                description="PostgreSQL数据库门诊患者规则模板，使用项目名称匹配",
                database_type="postgresql",
                patient_type="outpatient"
            ),
            "rule_pg_code_inpatient": TemplateCategory(
                id="rule_pg_code_inpatient",
                name="PostgreSQL住院规则(按编码)",
                description="PostgreSQL数据库住院患者规则模板，使用项目编码匹配",
                database_type="postgresql",
                patient_type="inpatient"
            ),
            "rule_pg_code_outpatient": TemplateCategory(
                id="rule_pg_code_outpatient",
                name="PostgreSQL门诊规则(按编码)",
                description="PostgreSQL数据库门诊患者规则模板，使用项目编码匹配",
                database_type="postgresql",
                patient_type="outpatient"
            ),
            "rule_oracle_name_inpatient": TemplateCategory(
                id="rule_oracle_name_inpatient",
                name="Oracle住院规则(按名称)",
                description="Oracle数据库住院患者规则模板，使用项目名称匹配",
                database_type="oracle",
                patient_type="inpatient"
            ),
            "rule_oracle_code_inpatient": TemplateCategory(
                id="rule_oracle_code_inpatient",
                name="Oracle住院规则(按编码)",
                description="Oracle数据库住院患者规则模板，使用项目编码匹配",
                database_type="oracle",
                patient_type="inpatient"
            ),
            "rule-general": TemplateCategory(
                id="rule-general",
                name="通用规则模板(新版)",
                description="新版通用医保规则模板",
                database_type="general",
                patient_type="general"
            ),
            "rule-pg-code": TemplateCategory(
                id="rule-pg-code",
                name="PostgreSQL规则(按编码)",
                description="PostgreSQL数据库规则模板，使用项目编码匹配",
                database_type="postgresql",
                patient_type="general"
            ),
            "rule_pg_national_bureau_inpatient": TemplateCategory(
                id="rule_pg_national_bureau_inpatient",
                name="PostgreSQL国家局住院规则",
                description="PostgreSQL数据库国家医保局住院规则模板",
                database_type="postgresql",
                patient_type="inpatient"
            ),
            "rule_pg_national_code_inpatient": TemplateCategory(
                id="rule_pg_national_code_inpatient",
                name="PostgreSQL国家局住院规则(按编码)",
                description="PostgreSQL数据库国家医保局住院规则模板，使用项目编码匹配",
                database_type="postgresql",
                patient_type="inpatient"
            ),
            "rule_pg_national_code_outpatient": TemplateCategory(
                id="rule_pg_national_code_outpatient",
                name="PostgreSQL国家局门诊规则(按编码)",
                description="PostgreSQL数据库国家医保局门诊规则模板，使用项目编码匹配",
                database_type="postgresql",
                patient_type="outpatient"
            ),
            "manual": TemplateCategory(
                id="manual",
                name="手动规则模板",
                description="手动创建的规则模板",
                database_type="general",
                patient_type="general"
            ),
            "excel": TemplateCategory(
                id="excel",
                name="Excel导入模板",
                description="从Excel文件导入的规则模板",
                database_type="general",
                patient_type="general"
            )
        }
    
    def _ensure_default_template(self):
        """Ensure at least one default template exists for testing."""
        if not os.path.exists(self.templates_rule_dir):
            os.makedirs(self.templates_rule_dir)
        
        # Create a default template if none exist
        template_files = [f for f in os.listdir(self.templates_rule_dir) if f.endswith('.sql')]
        if not template_files:
            default_template_path = os.path.join(self.templates_rule_dir, 'template1.sql')
            with open(default_template_path, 'w', encoding='utf-8') as f:
                f.write('-- This is a sample template\nSELECT * FROM your_table WHERE condition;')
    
    def get_template_categories(self) -> List[TemplateCategory]:
        """Get all available template categories."""
        return list(self._category_mapping.values())
    
    def get_templates_by_category(self, category_id: str) -> List[TemplateInfo]:
        """Get templates by category ID with caching."""
        try:
            # Check cache first
            if self._cache_enabled and category_id in self._category_cache:
                return self._category_cache[category_id]
            
            templates = []
            category_dir = os.path.join(self.templates_base_dir, category_id)
            
            if not os.path.exists(category_dir):
                return templates
            
            category = self._category_mapping.get(category_id)
            
            for filename in os.listdir(category_dir):
                if filename.endswith(".sql"):
                    file_path = os.path.join(category_dir, filename)
                    try:
                        template_info = self._load_template_from_file(file_path, category)
                        if template_info:
                            templates.append(template_info)
                    except Exception as e:
                        print(f"Error reading template {filename}: {e}")
            
            # Cache the result
            if self._cache_enabled:
                self._category_cache[category_id] = templates
            
            return templates
            
        except Exception as e:
            raise SystemError(f"Failed to load templates for category {category_id}: {str(e)}")
    
    def get_templates(self) -> List[TemplateInfo]:
        """Get all available SQL templates from all categories with caching."""
        try:
            # Check cache first
            cache_key = "all_templates"
            if self._cache_enabled and cache_key in self._template_list_cache:
                return self._template_list_cache[cache_key]
            
            all_templates = []
            
            # Load templates from all categories
            for category_id in self._category_mapping.keys():
                category_templates = self.get_templates_by_category(category_id)
                all_templates.extend(category_templates)
            
            # Also load from the default rule directory for backward compatibility
            if os.path.exists(self.templates_rule_dir):
                for filename in os.listdir(self.templates_rule_dir):
                    if filename.endswith(".sql"):
                        file_path = os.path.join(self.templates_rule_dir, filename)
                        try:
                            template_info = self._load_template_from_file(file_path)
                            if template_info:
                                all_templates.append(template_info)
                        except Exception as e:
                            print(f"Error reading template {filename}: {e}")
            
            # Cache the result
            if self._cache_enabled:
                self._template_list_cache[cache_key] = all_templates
            
            return all_templates
            
        except Exception as e:
            raise SystemError(f"Failed to load templates: {str(e)}")
    
    def clear_cache(self):
        """Clear all template caches."""
        self._template_cache.clear()
        self._template_list_cache.clear()
        self._category_cache.clear()
    
    def refresh_template_cache(self, template_id: str = None):
        """Refresh cache for a specific template or all templates."""
        if template_id:
            # Remove specific template from cache
            keys_to_remove = [key for key in self._template_cache.keys() if template_id in key]
            for key in keys_to_remove:
                del self._template_cache[key]
        else:
            # Clear all caches
            self.clear_cache()
    
    def get_template_content(self, template_id: str) -> str:
        """Get template content by ID with caching."""
        template_info = self.get_template_by_id(template_id)
        return template_info.sql
    
    def generate_dynamic_form_config(self, template_id: str) -> Dict[str, Any]:
        """Generate dynamic form configuration for template parameters."""
        try:
            template_info = self.get_template_by_id(template_id)
            parameters = template_info.parameters or []
            
            form_config = {
                "template_id": template_id,
                "template_name": template_info.description,
                "category": template_info.category,
                "database_type": template_info.database_type,
                "patient_type": template_info.patient_type,
                "parameters": []
            }
            
            for param in parameters:
                field_config = self._generate_field_config(param)
                form_config["parameters"].append(field_config)
            
            return form_config
            
        except Exception as e:
            raise SystemError(f"Failed to generate form config for template {template_id}: {str(e)}")
    
    def _generate_field_config(self, param: TemplateParameter) -> Dict[str, Any]:
        """Generate form field configuration for a parameter."""
        field_config = {
            "name": param.name,
            "label": param.description or param.name,
            "type": self._map_parameter_type_to_form_type(param.parameter_type),
            "required": param.required,
            "placeholder": self._generate_placeholder(param),
            "validation": self._generate_validation_rules(param)
        }
        
        if param.default_value:
            field_config["default_value"] = param.default_value
        
        # Add type-specific configurations
        if param.parameter_type == "enum":
            field_config["options"] = self._get_enum_options(param.name)
        elif param.parameter_type == "number":
            field_config["min"] = 0
            field_config["step"] = 1 if "数量" in param.name or "次数" in param.name else 0.01
        elif param.parameter_type == "list":
            field_config["multiple"] = True
            field_config["separator"] = ","
        
        return field_config
    
    def _map_parameter_type_to_form_type(self, param_type: str) -> str:
        """Map parameter type to HTML form input type."""
        type_mapping = {
            "text": "text",
            "number": "number",
            "list": "textarea",
            "enum": "select"
        }
        return type_mapping.get(param_type, "text")
    
    def _generate_placeholder(self, param: TemplateParameter) -> str:
        """Generate placeholder text for form field."""
        placeholders = {
            "医保名称1": "请输入医保项目名称，多个项目用逗号分隔",
            "医保编码1": "请输入医保项目编码，多个编码用逗号分隔",
            "违规数量": "请输入违规阈值数量",
            "违规金额": "请输入违规阈值金额",
            "排除诊断": "请输入需要排除的诊断名称",
            "排除科室": "请输入需要排除的科室名称",
            "性别": "请选择性别",
            "年龄": "请输入年龄限制",
            "天数": "请输入天数限制",
            "次数": "请输入次数限制"
        }
        
        return placeholders.get(param.name, f"请输入{param.name}")
    
    def _generate_validation_rules(self, param: TemplateParameter) -> Dict[str, Any]:
        """Generate validation rules for form field."""
        rules = {}
        
        if param.required:
            rules["required"] = True
        
        if param.parameter_type == "number":
            rules["type"] = "number"
            rules["min"] = 0
        elif param.parameter_type == "list":
            rules["pattern"] = r"^[^,]+(,[^,]+)*$"  # Comma-separated values
            rules["message"] = "请输入有效的列表，多个值用逗号分隔"
        
        return rules
    
    def _get_enum_options(self, param_name: str) -> List[Dict[str, str]]:
        """Get options for enum parameters."""
        if param_name == "性别":
            return [
                {"value": "男", "label": "男"},
                {"value": "女", "label": "女"}
            ]
        
        return []
    
    def _load_template_from_file(self, file_path: str, category: Optional[TemplateCategory] = None) -> Optional[TemplateInfo]:
        """Load template information from a file with caching."""
        try:
            # Check cache first
            if self._cache_enabled and file_path in self._template_cache:
                return self._template_cache[file_path]
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            filename = os.path.basename(file_path)
            template_id = os.path.splitext(filename)[0]
            
            # Create full template ID with category prefix if category exists
            if category:
                full_template_id = f"{category.id}/{template_id}"
            else:
                full_template_id = template_id
            
            # Extract description from first line comment
            first_line = content.split('\n', 1)[0]
            description = first_line.replace('--', '').strip() if first_line.startswith('--') else filename
            
            # Parse parameters from template content
            parameters = self.parse_template_parameters(content)
            
            template_info = TemplateInfo(
                id=full_template_id,
                description=description,
                sql=content,
                category=category.id if category else None,
                parameters=parameters,
                database_type=category.database_type if category else None,
                patient_type=category.patient_type if category else None
            )
            
            # Cache the result
            if self._cache_enabled:
                self._template_cache[file_path] = template_info
            
            return template_info
            
        except Exception as e:
            print(f"Error loading template from {file_path}: {e}")
            return None
    
    def get_template_by_id(self, template_id: str) -> TemplateInfo:
        """Get a specific template by ID."""
        if not template_id:
            raise BusinessError("Template ID is required")
        
        # Check if template_id contains category prefix
        if '/' in template_id:
            category_id, template_name = template_id.split('/', 1)
            file_path = os.path.join(self.templates_base_dir, category_id, f"{template_name}.sql")
            category = self._category_mapping.get(category_id)
        else:
            # Backward compatibility: check in default rule directory
            file_path = os.path.join(self.templates_rule_dir, f"{template_id}.sql")
            category = None
        
        if not os.path.exists(file_path):
            raise BusinessError(
                f"Template '{template_id}' not found",
                error_code="TEMPLATE_NOT_FOUND",
                details={"template_id": template_id}
            )
        
        try:
            template_info = self._load_template_from_file(file_path, category)
            if not template_info:
                raise SystemError(f"Failed to load template '{template_id}'")
            
            return template_info
            
        except Exception as e:
            raise SystemError(f"Failed to load template '{template_id}': {str(e)}")
    
    def get_templates_as_dict(self) -> Dict[str, str]:
        """Get templates as a dictionary for backward compatibility."""
        try:
            templates = {}
            template_list = self.get_templates()
            
            for template in template_list:
                templates[template.id] = template.sql
            
            return templates
            
        except Exception as e:
            raise SystemError(f"Failed to load templates as dictionary: {str(e)}")
    
    def parse_template_parameters(self, template_content: str) -> List[TemplateParameter]:
        """Parse template parameters from template content using {parameter} format."""
        # Find all parameters in the format {parameter_name}
        parameter_pattern = r'\{([^}]+)\}'
        matches = re.findall(parameter_pattern, template_content)
        
        # Remove duplicates and create parameter objects
        unique_params = list(set([match.strip() for match in matches]))
        parameters = []
        
        for param_name in unique_params:
            # Determine parameter type based on name patterns
            param_type = self._infer_parameter_type(param_name)
            
            parameters.append(TemplateParameter(
                name=param_name,
                description=self._generate_parameter_description(param_name),
                parameter_type=param_type,
                required=True
            ))
        
        return parameters
    
    def _infer_parameter_type(self, param_name: str) -> str:
        """Infer parameter type based on parameter name."""
        param_lower = param_name.lower()
        
        # Number type parameters
        if any(keyword in param_lower for keyword in ['数量', '金额', '天数', '次数', '年龄', '限制']):
            return "number"
        
        # List type parameters (multiple values)
        if any(keyword in param_lower for keyword in ['名称1', '编码1', '项目', '诊断', '科室']):
            return "list"
        
        # Enum type parameters
        if '性别' in param_lower:
            return "enum"
        
        # Default to text
        return "text"
    
    def _generate_parameter_description(self, param_name: str) -> str:
        """Generate user-friendly description for parameter."""
        descriptions = {
            '医保名称1': '医保项目名称列表，多个项目用逗号分隔',
            '医保编码1': '医保项目编码列表，多个编码用逗号分隔',
            '违规数量': '违规阈值数量，超过此数量视为违规',
            '违规金额': '违规阈值金额，超过此金额视为违规',
            '排除诊断': '需要排除的诊断名称，支持正则表达式',
            '排除科室': '需要排除的科室名称，支持正则表达式',
            '性别': '患者性别限制，可选值：男、女',
            '年龄': '年龄限制值',
            '天数': '天数限制值',
            '次数': '次数限制值'
        }
        
        return descriptions.get(param_name, f"请输入{param_name}的值")
    
    def load_template(self, template_path: str) -> TemplateInfo:
        """Load a template from the specified path."""
        if not os.path.exists(template_path):
            raise BusinessError(f"Template file not found: {template_path}")
        
        try:
            return self._load_template_from_file(template_path)
        except Exception as e:
            raise SystemError(f"Failed to load template from {template_path}: {str(e)}")
    
    def get_templates_by_database_type(self, database_type: str) -> List[TemplateInfo]:
        """Get templates filtered by database type."""
        all_templates = []
        
        for category_id, category in self._category_mapping.items():
            if category.database_type == database_type or category.database_type == "general":
                category_templates = self.get_templates_by_category(category_id)
                all_templates.extend(category_templates)
        
        return all_templates
    
    def get_templates_by_patient_type(self, patient_type: str) -> List[TemplateInfo]:
        """Get templates filtered by patient type (inpatient/outpatient)."""
        all_templates = []
        
        for category_id, category in self._category_mapping.items():
            if category.patient_type == patient_type or category.patient_type == "general":
                category_templates = self.get_templates_by_category(category_id)
                all_templates.extend(category_templates)
        
        return all_templates