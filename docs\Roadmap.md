# 产品路线图 (Roadmap) - 临时规则编写工具

## 1. 路线图概述

本路线图旨在规划“临时规则编写工具”从最小可行产品（MVP）到未来版本的演进路径。它将明确各阶段的核心目标、关键功能和预期交付时间，以指导团队的开发节奏和优先级排序。

## 2. 版本规划策略

- **MVP (v1.0)**: **核心价值验证**。聚焦于最核心的“模板化生成SQL”和“基础规则管理”功能，快速上线，验证产品核心假设，收集早期用户反馈。
- **v2.0**: **体验优化与功能完善**。在MVP基础上，根据用户反馈优化交互体验，并扩展更多规则模板和管理功能，提升产品的易用性和覆盖面。
- **v3.0 (未来)**: **智能化与集成**。引入更多AI能力，如规则推荐、参数智能填充，并探索与现有业务系统（如风控引擎）的集成，实现规则的“一键部署”。

## 3. 详细版本规划

### 3.1 MVP (v1.0) - 预计周期: 4周

- **核心目标**: 让业务人员能够独立、快速地通过模板创建和管理临时规则。
- **关键功能**:
  - **P0 (必须)**:
    - [ ] **规则SQL生成**: 
      - [ ] 支持从预设的规则类型和模板列表中选择。
      - [ ] 支持根据模板填写参数（文本、数字）。
      - [ ] 支持一键生成SQL语句并预览。
      - [ ] 内置不少于5个最常用的规则模板（如：超每日数量、限性别、限年龄、重复收费、项目组合）。
    - [ ] **规则管理**:
      - [ ] 支持创建、保存新规则。
      - [ ] 支持以列表形式查看已创建的规则。
      - [ ] 支持编辑已有的规则。
      - [ ] 支持（逻辑）删除规则。
    - [ ] **基础框架**:
      - [ ] 用户登录与认证。
      - [ ] 基础的页面布局和导航。
  - **P1 (期望)**:
    - [ ] 规则列表支持简单的文本搜索。
    - [ ] 参数输入时提供简单的合法性校验（如数字范围）。
  - **P2 (可选)**:
    - [ ] 提供规则内涵、政策依据等富文本描述字段。

### 3.2 v2.0 - 体验优化与功能完善 - 预计周期: 6周

- **核心目标**: 提升规则创建的灵活性和管理效率，优化用户体验。
- **关键功能**:
  - **P0 (必须)**:
    - [ ] **增强的SQL生成**: 
      - [ ] 扩展规则模板库至20个以上，覆盖更多业务场景。
      - [ ] 支持更复杂的参数类型，如日期选择器、下拉选择（从数据字典获取）。
    - [ ] **优化的规则管理**:
      - [ ] 规则列表支持多条件筛选（按规则类型、状态）和排序。
      - [ ] 增加规则“复制”功能，方便在已有规则基础上创建新规则。
  - **P1 (期望)**:
    - [ ] **规则测试模块 (初步)**: 支持将生成的SQL粘贴到指定环境执行，或提供连接测试数据库的功能。
    - [ ] 引入“规则状态”管理（待测试、已上线、已失效），并在列表中清晰展示。
  - **P2 (可选)**:
    - [ ] 提供规则的导入/导出功能（Excel格式）。

### 3.3 v3.0 (未来) - 智能化与集成

- **核心目标**: 让规则创建更智能，实现与业务系统的无缝对接。
- **关键功能**:
  - [ ] **智能规则推荐**: 根据用户输入的业务描述，智能推荐可能适用的规则模板。
  - [ ] **参数智能填充**: 基于历史数据或知识库，为某些参数提供智能填充建议。
  - [ ] **API集成**: 提供API接口，允许外部系统（如风控引擎、稽核系统）直接调用和执行本工具生成的规则。
  - [ ] **版本控制**: 对规则的每次修改进行版本记录，支持查看历史版本和回滚。

## 4. 风险管理

- **技术风险**: 模板生成SQL的逻辑需要非常严谨，以覆盖各种边界条件，避免生成有语法错误或逻辑漏洞的SQL。
  - **应对**: 加强代码审查和单元测试，针对每种模板设计全面的测试用例。
- **用户接受度风险**: 业务人员可能不习惯或不信任自己创建的规则。
  - **应对**: MVP阶段与种子用户紧密合作，提供充分的培训和支持，根据其反馈快速迭代，建立信任感。