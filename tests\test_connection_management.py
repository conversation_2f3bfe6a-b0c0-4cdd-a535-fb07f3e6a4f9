#!/usr/bin/env python3
"""
测试数据库连接管理功能
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:5000"

def test_connection_save_and_load():
    """测试连接保存和加载功能"""
    print("=== 测试连接保存和加载功能 ===")
    
    # 测试连接数据
    test_connections = [
        {
            "name": "测试连接1",
            "description": "************** 数据库连接",
            "database_type": "postgresql",
            "host": "**************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        },
        {
            "name": "测试连接2", 
            "description": "************* 数据库连接",
            "database_type": "postgresql",
            "host": "*************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        }
    ]
    
    saved_connections = []
    
    try:
        # 1. 保存连接
        print("1. 保存测试连接...")
        for i, connection in enumerate(test_connections, 1):
            print(f"   保存连接 {i}: {connection['name']} ({connection['host']})")
            
            response = requests.post(f"{BASE_URL}/api/database/connections", json=connection)
            result = response.json()
            
            if result.get('success'):
                connection_id = result['connection']['id']
                saved_connections.append(connection_id)
                print(f"   ✓ 连接保存成功，ID: {connection_id}")
            else:
                print(f"   ✗ 连接保存失败: {result.get('error')}")
        
        # 2. 获取所有连接
        print("\n2. 获取所有保存的连接...")
        response = requests.get(f"{BASE_URL}/api/database/connections")
        result = response.json()
        
        if result.get('success'):
            connections = result.get('connections', [])
            print(f"   ✓ 获取到 {len(connections)} 个连接")
            
            for conn in connections:
                print(f"   - {conn['name']}: {conn['host']}:{conn['port']} (ID: {conn['id']})")
        else:
            print(f"   ✗ 获取连接失败: {result.get('error')}")
        
        # 3. 测试特定连接获取
        print("\n3. 测试获取特定连接...")
        if saved_connections:
            connection_id = saved_connections[0]
            response = requests.get(f"{BASE_URL}/api/database/connections/{connection_id}")
            result = response.json()
            
            if result.get('success'):
                conn = result['connection']
                print(f"   ✓ 获取连接成功: {conn['name']} ({conn['host']}:{conn['port']})")
            else:
                print(f"   ✗ 获取特定连接失败: {result.get('error')}")
        
        # 4. 清理测试数据
        print("\n4. 清理测试数据...")
        for connection_id in saved_connections:
            response = requests.delete(f"{BASE_URL}/api/database/connections/{connection_id}")
            result = response.json()
            
            if result.get('success'):
                print(f"   ✓ 删除连接成功: {connection_id}")
            else:
                print(f"   ✗ 删除连接失败: {connection_id} - {result.get('error')}")
        
        print("\n✓ 连接管理功能测试完成！")
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")

def test_connection_api_endpoints():
    """测试连接管理API端点"""
    print("\n=== 测试连接管理API端点 ===")
    
    # 测试数据
    test_connection = {
        "name": "API测试连接",
        "description": "用于API测试的数据库连接",
        "database_type": "postgresql",
        "host": "**************",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        # 1. 测试POST /api/database/connections
        print("1. 测试保存连接API...")
        response = requests.post(f"{BASE_URL}/api/database/connections", json=test_connection)
        result = response.json()
        
        if result.get('success'):
            connection_id = result['connection']['id']
            print(f"   ✓ 连接保存成功，ID: {connection_id}")
            
            # 2. 测试GET /api/database/connections/{id}
            print("2. 测试获取特定连接API...")
            response = requests.get(f"{BASE_URL}/api/database/connections/{connection_id}")
            result = response.json()
            
            if result.get('success'):
                print(f"   ✓ 获取连接成功: {result['connection']['name']}")
            else:
                print(f"   ✗ 获取连接失败: {result.get('error')}")
            
            # 3. 测试PUT /api/database/connections/{id}
            print("3. 测试更新连接API...")
            updated_connection = test_connection.copy()
            updated_connection['name'] = "更新后的API测试连接"
            updated_connection['description'] = "已更新的测试连接"
            
            response = requests.put(f"{BASE_URL}/api/database/connections/{connection_id}", json=updated_connection)
            result = response.json()
            
            if result.get('success'):
                print(f"   ✓ 连接更新成功")
            else:
                print(f"   ✗ 连接更新失败: {result.get('error')}")
            
            # 4. 测试DELETE /api/database/connections/{id}
            print("4. 测试删除连接API...")
            response = requests.delete(f"{BASE_URL}/api/database/connections/{connection_id}")
            result = response.json()
            
            if result.get('success'):
                print(f"   ✓ 连接删除成功")
            else:
                print(f"   ✗ 连接删除失败: {result.get('error')}")
        else:
            print(f"   ✗ 连接保存失败: {result.get('error')}")
            
    except Exception as e:
        print(f"✗ API测试失败: {str(e)}")

def test_connection_persistence():
    """测试连接配置持久化"""
    print("\n=== 测试连接配置持久化 ===")
    
    # 保存一些测试连接
    test_connections = [
        {
            "name": "持久化测试连接1",
            "description": "************** 数据库",
            "database_type": "postgresql",
            "host": "**************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        },
        {
            "name": "持久化测试连接2",
            "description": "************* 数据库",
            "database_type": "postgresql",
            "host": "*************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        }
    ]
    
    saved_ids = []
    
    try:
        # 1. 保存连接
        print("1. 保存测试连接...")
        for conn in test_connections:
            response = requests.post(f"{BASE_URL}/api/database/connections", json=conn)
            result = response.json()
            
            if result.get('success'):
                saved_ids.append(result['connection']['id'])
                print(f"   ✓ 保存连接: {conn['name']}")
            else:
                print(f"   ✗ 保存连接失败: {conn['name']} - {result.get('error')}")
        
        # 2. 验证连接已保存
        print("2. 验证连接已保存...")
        response = requests.get(f"{BASE_URL}/api/database/connections")
        result = response.json()
        
        if result.get('success'):
            connections = result.get('connections', [])
            print(f"   ✓ 找到 {len(connections)} 个保存的连接")
            
            for conn in connections:
                print(f"   - {conn['name']}: {conn['host']}:{conn['port']}")
        else:
            print(f"   ✗ 获取连接失败: {result.get('error')}")
        
        # 3. 清理测试数据
        print("3. 清理测试数据...")
        for conn_id in saved_ids:
            requests.delete(f"{BASE_URL}/api/database/connections/{conn_id}")
            print(f"   ✓ 删除连接: {conn_id}")
        
        print("✓ 连接持久化测试完成！")
        
    except Exception as e:
        print(f"✗ 持久化测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试数据库连接管理功能...")
    print("=" * 60)
    
    try:
        # 测试连接保存和加载
        test_connection_save_and_load()
        
        # 测试API端点
        test_connection_api_endpoints()
        
        # 测试连接持久化
        test_connection_persistence()
        
        print("\n" + "=" * 60)
        print("✓ 所有连接管理功能测试完成！")
        print("\n功能验证总结：")
        print("✅ 连接保存功能正常")
        print("✅ 连接加载功能正常")
        print("✅ 连接更新功能正常")
        print("✅ 连接删除功能正常")
        print("✅ 连接配置持久化正常")
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保服务器正在运行")
        print("启动服务器命令: python app.py")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main() 