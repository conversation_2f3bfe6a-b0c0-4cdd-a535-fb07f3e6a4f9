"""
Controller for intelligent SQL generation workflow.
"""
from flask import Blueprint, request, jsonify, session
from typing import Dict, Any
from services.intelligent_sql_workflow_service import IntelligentSqlWorkflowService, WorkflowState
from utils.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Business<PERSON><PERSON>r, ValidationError


# Create blueprint
intelligent_workflow_bp = Blueprint('intelligent_workflow', __name__)

# Initialize service
workflow_service = IntelligentSqlWorkflowService()

# In-memory storage for workflow states (in production, use Redis or database)
workflow_states: Dict[str, WorkflowState] = {}


@intelligent_workflow_bp.route('/api/workflow/start', methods=['POST'])
def start_workflow():
    """
    Start a new intelligent SQL generation workflow.
    
    Request body:
    {
        "rule_name": "规则名称"
    }
    """
    try:
        data = request.get_json()
        if not data or 'rule_name' not in data:
            raise BusinessError("rule_name is required in request body")
        
        rule_name = data['rule_name']
        
        # Start workflow
        workflow_state = workflow_service.start_workflow(rule_name)
        
        # Store workflow state
        workflow_states[workflow_state.workflow_id] = workflow_state
        
        # Get workflow summary
        summary = workflow_service.get_workflow_summary(workflow_state)
        
        return jsonify({
            "status": "success",
            "data": {
                "workflow_id": workflow_state.workflow_id,
                "summary": summary,
                "current_step_data": workflow_state.steps[0].data
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/<workflow_id>/attributes', methods=['PUT'])
def update_rule_attributes(workflow_id: str):
    """
    Update rule attributes in the workflow.
    
    Request body:
    {
        "name": "规则名称",
        "rule_type": "超频",
        "patient_type": "inpatient",
        "match_method": "name",
        "database_type": "postgresql",
        "description": "规则描述",
        "category": "规则分类",
        "policy_basis": "政策依据"
    }
    """
    try:
        if workflow_id not in workflow_states:
            raise BusinessError(f"Workflow {workflow_id} not found")
        
        data = request.get_json()
        if not data:
            raise BusinessError("Request body is required")
        
        workflow_state = workflow_states[workflow_id]
        
        # Update attributes
        updated_state = workflow_service.update_rule_attributes(workflow_state, data)
        workflow_states[workflow_id] = updated_state
        
        # Get current step data
        current_step = next((step for step in updated_state.steps if step.step_id == updated_state.current_step), None)
        current_step_data = current_step.data if current_step else None
        
        return jsonify({
            "status": "success",
            "data": {
                "workflow_id": workflow_id,
                "current_step": updated_state.current_step,
                "current_step_data": current_step_data,
                "summary": workflow_service.get_workflow_summary(updated_state)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/<workflow_id>/template', methods=['PUT'])
def select_template(workflow_id: str):
    """
    Select a template in the workflow.
    
    Request body:
    {
        "template_id": "template_id"
    }
    """
    try:
        if workflow_id not in workflow_states:
            raise BusinessError(f"Workflow {workflow_id} not found")
        
        data = request.get_json()
        if not data or 'template_id' not in data:
            raise BusinessError("template_id is required in request body")
        
        template_id = data['template_id']
        workflow_state = workflow_states[workflow_id]
        
        # Select template
        updated_state = workflow_service.select_template(workflow_state, template_id)
        workflow_states[workflow_id] = updated_state
        
        # Get current step data
        current_step = next((step for step in updated_state.steps if step.step_id == updated_state.current_step), None)
        current_step_data = current_step.data if current_step else None
        
        return jsonify({
            "status": "success",
            "data": {
                "workflow_id": workflow_id,
                "current_step": updated_state.current_step,
                "current_step_data": current_step_data,
                "summary": workflow_service.get_workflow_summary(updated_state)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/<workflow_id>/parameters', methods=['PUT'])
def submit_parameters(workflow_id: str):
    """
    Submit template parameters and generate SQL.
    
    Request body:
    {
        "parameters": {
            "param1": "value1",
            "param2": "value2"
        }
    }
    """
    try:
        if workflow_id not in workflow_states:
            raise BusinessError(f"Workflow {workflow_id} not found")
        
        data = request.get_json()
        if not data or 'parameters' not in data:
            raise BusinessError("parameters are required in request body")
        
        parameters = data['parameters']
        workflow_state = workflow_states[workflow_id]
        
        # Submit parameters
        updated_state = workflow_service.submit_parameters(workflow_state, parameters)
        workflow_states[workflow_id] = updated_state
        
        # Get current step data
        current_step = next((step for step in updated_state.steps if step.step_id == updated_state.current_step), None)
        current_step_data = current_step.data if current_step else None
        
        return jsonify({
            "status": "success",
            "data": {
                "workflow_id": workflow_id,
                "current_step": updated_state.current_step,
                "current_step_data": current_step_data,
                "generated_sql": updated_state.generated_sql,
                "summary": workflow_service.get_workflow_summary(updated_state)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/<workflow_id>/sql', methods=['PUT'])
def update_sql(workflow_id: str):
    """
    Update the generated SQL content.
    
    Request body:
    {
        "sql_content": "SELECT * FROM ..."
    }
    """
    try:
        if workflow_id not in workflow_states:
            raise BusinessError(f"Workflow {workflow_id} not found")
        
        data = request.get_json()
        if not data or 'sql_content' not in data:
            raise BusinessError("sql_content is required in request body")
        
        sql_content = data['sql_content']
        workflow_state = workflow_states[workflow_id]
        
        # Update SQL
        updated_state = workflow_service.update_sql(workflow_state, sql_content)
        workflow_states[workflow_id] = updated_state
        
        return jsonify({
            "status": "success",
            "data": {
                "workflow_id": workflow_id,
                "updated_sql": updated_state.generated_sql,
                "summary": workflow_service.get_workflow_summary(updated_state)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/<workflow_id>/finalize', methods=['POST'])
def finalize_rule(workflow_id: str):
    """
    Finalize and save the rule.
    """
    try:
        if workflow_id not in workflow_states:
            raise BusinessError(f"Workflow {workflow_id} not found")
        
        workflow_state = workflow_states[workflow_id]
        
        # Finalize rule
        result = workflow_service.finalize_rule(workflow_state)
        
        # Update workflow state
        workflow_states[workflow_id] = workflow_state
        
        return jsonify({
            "status": "success",
            "data": result
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/<workflow_id>/summary', methods=['GET'])
def get_workflow_summary(workflow_id: str):
    """
    Get workflow summary.
    """
    try:
        if workflow_id not in workflow_states:
            raise BusinessError(f"Workflow {workflow_id} not found")
        
        workflow_state = workflow_states[workflow_id]
        summary = workflow_service.get_workflow_summary(workflow_state)
        
        return jsonify({
            "status": "success",
            "data": summary
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/<workflow_id>/step-data', methods=['GET'])
def get_current_step_data(workflow_id: str):
    """
    Get current step data.
    """
    try:
        if workflow_id not in workflow_states:
            raise BusinessError(f"Workflow {workflow_id} not found")
        
        workflow_state = workflow_states[workflow_id]
        current_step = next((step for step in workflow_state.steps if step.step_id == workflow_state.current_step), None)
        
        return jsonify({
            "status": "success",
            "data": {
                "workflow_id": workflow_id,
                "current_step": workflow_state.current_step,
                "step_data": current_step.data if current_step else None
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/preview-sql', methods=['POST'])
def preview_sql():
    """
    Preview SQL generation without full validation.
    
    Request body:
    {
        "template_id": "template_id",
        "parameters": {"param1": "value1"},
        "database_type": "postgresql"
    }
    """
    try:
        data = request.get_json()
        if not data:
            raise BusinessError("Request body is required")
        
        template_id = data.get('template_id')
        parameters = data.get('parameters', {})
        database_type = data.get('database_type', 'postgresql')
        
        if not template_id:
            raise BusinessError("template_id is required")
        
        preview_sql = workflow_service.preview_sql_with_parameters(
            template_id, parameters, database_type
        )
        
        return jsonify({
            "status": "success",
            "data": {
                "preview_sql": preview_sql,
                "template_id": template_id,
                "parameters": parameters,
                "database_type": database_type
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/template-recommendations', methods=['POST'])
def get_template_recommendations():
    """
    Get template recommendations for a rule.
    
    Request body:
    {
        "rule_name": "规则名称",
        "rule_attributes": {
            "rule_type": "超频",
            "patient_type": "inpatient",
            "match_method": "name",
            "database_type": "postgresql"
        }
    }
    """
    try:
        data = request.get_json()
        if not data or 'rule_name' not in data:
            raise BusinessError("rule_name is required in request body")
        
        rule_name = data['rule_name']
        rule_attributes = data.get('rule_attributes')
        
        recommendations = workflow_service.get_template_recommendations_for_rule(
            rule_name, rule_attributes
        )
        
        return jsonify({
            "status": "success",
            "data": {
                "rule_name": rule_name,
                "recommendations": recommendations,
                "total_count": len(recommendations)
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)


@intelligent_workflow_bp.route('/api/workflow/cleanup', methods=['POST'])
def cleanup_workflows():
    """
    Clean up old workflow states (for maintenance).
    """
    try:
        # In a real implementation, you would clean up based on timestamp
        # For now, just return the count
        active_workflows = len(workflow_states)
        
        return jsonify({
            "status": "success",
            "data": {
                "active_workflows": active_workflows,
                "message": "Workflow cleanup completed"
            }
        })
        
    except Exception as e:
        return ErrorHandler.handle_generic_error(e)
