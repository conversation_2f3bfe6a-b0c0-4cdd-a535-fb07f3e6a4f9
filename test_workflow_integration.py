#!/usr/bin/env python3
"""
Test script for intelligent workflow integration.
"""
import sys
import os
import json
import requests
import time
from threading import Thread

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app

def run_flask_app():
    """Run Flask app in a separate thread."""
    app.run(debug=False, port=5001, use_reloader=False)

def test_workflow_api():
    """Test the workflow API endpoints."""
    base_url = "http://127.0.0.1:5001"
    
    # Wait for server to start
    time.sleep(2)
    
    print("Testing Intelligent SQL Generation Workflow...")
    
    try:
        # Test 1: Start workflow
        print("\n1. Testing workflow start...")
        response = requests.post(f"{base_url}/api/workflow/start", 
                               json={"rule_name": "测试超频次规则"})
        
        if response.status_code == 200:
            result = response.json()
            workflow_id = result['data']['workflow_id']
            print(f"✓ Workflow started successfully. ID: {workflow_id}")
            print(f"  Current step: {result['data']['summary']['current_step']}")
            
            # Test 2: Update rule attributes
            print("\n2. Testing rule attributes update...")
            attributes = {
                "name": "测试超频次规则",
                "rule_type": "超频次",
                "patient_type": "inpatient",
                "match_method": "name",
                "database_type": "postgresql",
                "description": "测试规则描述",
                "category": "intelligent"
            }
            
            response = requests.put(f"{base_url}/api/workflow/{workflow_id}/attributes",
                                  json=attributes)
            
            if response.status_code == 200:
                result = response.json()
                print("✓ Rule attributes updated successfully")
                print(f"  Current step: {result['data']['current_step']}")
                print(f"  Recommendations count: {len(result['data']['current_step_data'].get('recommendations', []))}")
                
                # Test 3: Get workflow summary
                print("\n3. Testing workflow summary...")
                response = requests.get(f"{base_url}/api/workflow/{workflow_id}/summary")
                
                if response.status_code == 200:
                    summary = response.json()
                    print("✓ Workflow summary retrieved successfully")
                    print(f"  Progress: {summary['data']['progress']['percentage']}%")
                    print(f"  Rule info: {summary['data']['rule_info']}")
                else:
                    print(f"✗ Failed to get workflow summary: {response.status_code}")
                
                # Test 4: Template recommendations
                print("\n4. Testing template recommendations...")
                response = requests.post(f"{base_url}/api/workflow/template-recommendations",
                                       json={
                                           "rule_name": "测试超频次规则",
                                           "rule_attributes": attributes
                                       })
                
                if response.status_code == 200:
                    recommendations = response.json()
                    print("✓ Template recommendations retrieved successfully")
                    print(f"  Recommendations count: {recommendations['data']['total_count']}")
                    
                    if recommendations['data']['recommendations']:
                        first_rec = recommendations['data']['recommendations'][0]
                        print(f"  Top recommendation: {first_rec['template']['description']} (Score: {first_rec['score']:.2f})")
                else:
                    print(f"✗ Failed to get template recommendations: {response.status_code}")
                
                # Test 5: SQL Preview
                print("\n5. Testing SQL preview...")
                if recommendations['data']['recommendations']:
                    template_id = recommendations['data']['recommendations'][0]['template']['id']
                    response = requests.post(f"{base_url}/api/workflow/preview-sql",
                                           json={
                                               "template_id": template_id,
                                               "parameters": {"医保名称1": "测试项目", "违规数量": "3"},
                                               "database_type": "postgresql"
                                           })
                    
                    if response.status_code == 200:
                        preview = response.json()
                        print("✓ SQL preview generated successfully")
                        print(f"  Preview length: {len(preview['data']['preview_sql'])} characters")
                    else:
                        print(f"✗ Failed to generate SQL preview: {response.status_code}")
                
            else:
                print(f"✗ Failed to update rule attributes: {response.status_code}")
                print(f"  Error: {response.text}")
        else:
            print(f"✗ Failed to start workflow: {response.status_code}")
            print(f"  Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("✗ Failed to connect to Flask server")
    except Exception as e:
        print(f"✗ Test failed with error: {str(e)}")
    
    print("\nWorkflow integration test completed.")

if __name__ == "__main__":
    # Start Flask app in background thread
    flask_thread = Thread(target=run_flask_app, daemon=True)
    flask_thread.start()
    
    # Run tests
    test_workflow_api()