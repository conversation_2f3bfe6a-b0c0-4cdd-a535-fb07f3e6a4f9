"""
Template selector controller for handling template selection and dynamic display.
"""
from flask import Blueprint, request, jsonify
from services.rule_template_selector import RuleTemplateSelector
import logging

template_selector_bp = Blueprint('template_selector', __name__)
logger = logging.getLogger(__name__)

# Initialize template selector
template_selector = RuleTemplateSelector()


@template_selector_bp.route('/api/templates/folders', methods=['GET'])
def get_template_folders():
    """Get all available template folders."""
    try:
        folders = template_selector.get_template_folders()
        return jsonify({
            'success': True,
            'folders': folders
        })
    except Exception as e:
        logger.error(f"获取模板文件夹失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@template_selector_bp.route('/api/templates/folder/<folder_name>', methods=['GET'])
def get_templates_by_folder(folder_name):
    """Get templates from a specific folder."""
    try:
        templates = template_selector.get_templates_by_folder(folder_name)
        template_list = []
        
        for name, content in templates.items():
            template_list.append({
                'name': name,
                'content': content,
                'folder': folder_name
            })
        
        return jsonify({
            'success': True,
            'templates': template_list
        })
    except Exception as e:
        logger.error(f"获取文件夹模板失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@template_selector_bp.route('/api/templates/select', methods=['POST'])
def select_template():
    """Select template based on rule attributes."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供规则数据'
            }), 400
        
        rule = data.get('rule', {})
        db_type = data.get('db_type', 'pg')
        code_type = data.get('code_type', 'name')
        patient_type = data.get('patient_type', 'inpatient')
        
        template_content, template_name = template_selector.select_template(
            rule, db_type, code_type, patient_type
        )
        
        if template_content:
            return jsonify({
                'success': True,
                'template': {
                    'name': template_name,
                    'content': template_content,
                    'folder': f"rule_{db_type}_{code_type}_{patient_type}"
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': '未找到匹配的模板'
            })
            
    except Exception as e:
        logger.error(f"选择模板失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@template_selector_bp.route('/api/templates/manual-select', methods=['POST'])
def manual_select_template():
    """Manually select a template by name and folder."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供模板选择数据'
            }), 400
        
        template_name = data.get('template_name')
        folder_name = data.get('folder_name')
        
        if not template_name:
            return jsonify({
                'success': False,
                'error': '请提供模板名称'
            }), 400
        
        if folder_name:
            # Get template from specific folder
            template_content, _ = template_selector._get_template_from_folder(
                template_name, folder_name
            )
        else:
            # Get template from any folder
            template_content, _ = template_selector._get_template(template_name)
        
        if template_content:
            return jsonify({
                'success': True,
                'template': {
                    'name': template_name,
                    'content': template_content,
                    'folder': folder_name or 'auto'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': f'未找到模板: {template_name}'
            })
            
    except Exception as e:
        logger.error(f"手动选择模板失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@template_selector_bp.route('/api/templates/all', methods=['GET'])
def get_all_templates():
    """Get all available templates with their information."""
    try:
        all_templates = template_selector.get_available_templates()
        template_list = []
        
        for template_key, content in all_templates.items():
            template_info = template_selector.get_template_info(template_key)
            template_info['content'] = content
            template_list.append(template_info)
        
        return jsonify({
            'success': True,
            'templates': template_list
        })
    except Exception as e:
        logger.error(f"获取所有模板失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@template_selector_bp.route('/api/templates/preview', methods=['POST'])
def preview_template():
    """Preview template with rule attributes applied."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请提供预览数据'
            }), 400
        
        template_content = data.get('template_content', '')
        rule_attributes = data.get('rule_attributes', {})
        
        # Simple template variable replacement for preview
        preview_content = template_content
        for key, value in rule_attributes.items():
            placeholder = f"{{{key}}}"
            if placeholder in preview_content:
                preview_content = preview_content.replace(placeholder, str(value))
        
        return jsonify({
            'success': True,
            'preview': preview_content
        })
        
    except Exception as e:
        logger.error(f"预览模板失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500