# Requirements Document

## Introduction

临时规则编写工具是一款面向医保风控/稽核业务人员的低代码/无代码SQL规则生成与管理工具。该工具旨在让业务人员能够快速、准确地将业务需求转化为可执行的SQL规则，显著提升临时性、小批量规则的创建和测试效率，缩短规则上线周期。

## Requirements

### Requirement 1

**User Story:** 作为医保风控业务人员，我希望能够查看和管理我创建的所有临时规则，以便我能快速概览、编辑和维护这些规则。

#### Acceptance Criteria

1. WHEN 用户访问规则管理页面 THEN 系统 SHALL 显示用户创建的所有规则列表
2. WHEN 用户查看规则列表 THEN 系统 SHALL 显示规则ID、规则名称、规则类型、创建时间、状态等关键信息
3. WHEN 用户在规则列表中搜索 THEN 系统 SHALL 支持按规则名称进行模糊搜索
4. WHEN 规则数量超过页面显示限制 THEN 系统 SHALL 提供分页功能
5. WHEN 用户点击编辑按钮 THEN 系统 SHALL 跳转到规则编辑页面并加载现有规则数据
6. WHEN 用户点击删除按钮 THEN 系统 SHALL 显示确认对话框防止误操作
7. WHEN 用户确认删除规则 THEN 系统 SHALL 执行逻辑删除并从列表中移除该规则

### Requirement 2

**User Story:** 作为医保风控业务人员，我希望能够通过选择模板和填写参数的方式快速生成SQL规则，以便我无需学习SQL语法就能创建符合业务要求的规则。

#### Acceptance Criteria

1. WHEN 用户访问SQL生成页面 THEN 系统 SHALL 显示规则基本信息填写表单
2. WHEN 用户填写规则基本信息 THEN 系统 SHALL 要求填写规则名称、规则内涵、政策依据等必填字段
3. WHEN 用户选择规则类型 THEN 系统 SHALL 动态加载对应的模板列表
4. WHEN 用户选择具体模板 THEN 系统 SHALL 显示该模板所需的参数配置表单
5. WHEN 用户填写完所有参数并点击生成SQL THEN 系统 SHALL 根据模板和参数生成完整的SQL语句
6. WHEN SQL生成完成 THEN 系统 SHALL 在页面上显示生成的SQL供用户预览
7. WHEN 用户确认SQL无误并点击保存 THEN 系统 SHALL 将规则信息存入数据库
8. IF 必填字段未填写 THEN 系统 SHALL 显示明确的错误提示并阻止保存

### Requirement 3

**User Story:** 作为医保风控业务人员，我希望系统能够支持多种常见的医保规则类型模板，以便我能够覆盖大部分的临时规则创建场景。

#### Acceptance Criteria

1. WHEN 用户选择规则类型 THEN 系统 SHALL 提供超频次、重复收费、限性别等常见规则类型
2. WHEN 用户选择超频次类型 THEN 系统 SHALL 提供超每日数量、超每周数量等子模板
3. WHEN 用户使用超每日数量模板 THEN 系统 SHALL 要求填写医保项目编码/名称和每日限制数量参数
4. WHEN 用户填写模板参数 THEN 系统 SHALL 提供清晰的标签和提示说明
5. WHEN 系统生成SQL THEN 系统 SHALL 确保生成的SQL语法正确且符合业务逻辑

### Requirement 4

**User Story:** 作为医保风控业务人员，我希望系统能够同时支持Oracle和PostgreSQL数据库，以便我能够在不同的数据库环境中使用生成的规则。

#### Acceptance Criteria

1. WHEN 用户生成SQL规则 THEN 系统 SHALL 支持选择目标数据库类型（Oracle或PostgreSQL）
2. WHEN 用户选择Oracle数据库 THEN 系统 SHALL 生成符合Oracle SQL方言的查询语句
3. WHEN 用户选择PostgreSQL数据库 THEN 系统 SHALL 生成符合PostgreSQL SQL方言的查询语句
4. WHEN 系统生成不同数据库的SQL THEN 系统 SHALL 确保业务逻辑保持一致

### Requirement 5

**User Story:** 作为医保风控业务人员，我希望系统具有良好的性能和用户体验，以便我能够高效地完成规则创建和管理工作。

#### Acceptance Criteria

1. WHEN 用户访问任何页面 THEN 系统 SHALL 在2秒内完成页面加载
2. WHEN 用户点击生成SQL按钮 THEN 系统 SHALL 在1秒内返回生成结果
3. WHEN 用户进行任何操作 THEN 系统 SHALL 提供清晰的操作反馈和状态提示
4. WHEN 系统发生错误 THEN 系统 SHALL 显示友好的错误信息而非技术错误堆栈
5. WHEN 用户使用系统 THEN 系统 SHALL 确保界面简洁直观，无需培训即可上手使用

### Requirement 6

**User Story:** 作为医保风控业务人员，我希望系统能够根据规则的基本属性自动推荐合适的SQL模板，以便我能够更快速、准确地创建符合特定场景的规则。

#### Acceptance Criteria

1. WHEN 用户创建新规则时填写规则名称 THEN 系统 SHALL 基于规则名称关键词自动推荐相关模板
2. WHEN 用户选择规则类型（超频次、重复收费等） THEN 系统 SHALL 筛选出对应类型的所有可用模板
3. WHEN 用户选择适用范围（门诊、住院、通用） THEN 系统 SHALL 进一步筛选出适用于该范围的模板
4. WHEN 用户选择项目匹配方式（按名称、按编码） THEN 系统 SHALL 过滤出使用对应匹配方式的模板
5. WHEN 用户选择数据库类型（Oracle、PostgreSQL） THEN 系统 SHALL 只显示兼容该数据库的模板
6. WHEN 系统完成模板筛选 THEN 系统 SHALL 按匹配度排序并推荐最合适的模板
7. WHEN 用户选择推荐模板 THEN 系统 SHALL 自动加载模板参数表单
8. WHEN 用户需要时 THEN 系统 SHALL 仍然保持手动选择模板和自由编写SQL的功能

### Requirement 7

**User Story:** 作为系统管理员，我希望系统具备基本的安全性和数据完整性保障，以便确保规则数据的安全和系统的稳定运行。

#### Acceptance Criteria

1. WHEN 用户访问系统 THEN 系统 SHALL 要求进行身份认证
2. WHEN 用户操作规则数据 THEN 系统 SHALL 确保用户只能管理自己创建的规则
3. WHEN 系统处理用户输入 THEN 系统 SHALL 进行严格的输入验证防止SQL注入攻击
4. WHEN 系统生成SQL语句 THEN 系统 SHALL 使用参数化查询确保安全性
5. WHEN 系统发生异常 THEN 系统 SHALL 记录详细的日志信息便于问题排查