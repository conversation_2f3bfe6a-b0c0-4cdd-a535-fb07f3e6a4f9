"""
Unit tests for intelligent template service.
"""
import unittest
from unittest.mock import Mock, patch
from services.intelligent_template_service import IntelligentTemplateService, RuleAttributes
from models.rule import TemplateInfo, TemplateParameter


class TestIntelligentTemplateService(unittest.TestCase):
    """Test cases for IntelligentTemplateService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = IntelligentTemplateService()
        
        # Mock templates for testing
        self.mock_templates = [
            TemplateInfo(
                id="rule_pg_name_inpatient/超每日数量",
                description="超每日数量",
                sql="SELECT * FROM table WHERE condition",
                category="rule_pg_name_inpatient",
                database_type="postgresql",
                patient_type="inpatient",
                parameters=[
                    TemplateParameter(name="医保名称1", parameter_type="list", required=True),
                    TemplateParameter(name="违规数量", parameter_type="number", required=True)
                ]
            ),
            TemplateInfo(
                id="rule_oracle_name_inpatient/超每日数量",
                description="超每日数量",
                sql="SELECT * FROM table WHERE condition",
                category="rule_oracle_name_inpatient",
                database_type="oracle",
                patient_type="inpatient",
                parameters=[
                    TemplateParameter(name="医保名称1", parameter_type="list", required=True),
                    TemplateParameter(name="违规数量", parameter_type="number", required=True)
                ]
            ),
            TemplateInfo(
                id="rule_pg_code_outpatient/限性别",
                description="限性别",
                sql="SELECT * FROM table WHERE gender condition",
                category="rule_pg_code_outpatient",
                database_type="postgresql",
                patient_type="outpatient",
                parameters=[
                    TemplateParameter(name="医保编码1", parameter_type="list", required=True),
                    TemplateParameter(name="性别", parameter_type="enum", required=True)
                ]
            ),
            TemplateInfo(
                id="rule-general/重复收费",
                description="重复收费",
                sql="SELECT * FROM table WHERE duplicate condition",
                category="rule-general",
                database_type="general",
                patient_type="general",
                parameters=[
                    TemplateParameter(name="医保名称1", parameter_type="list", required=True)
                ]
            )
        ]
    
    @patch('services.intelligent_template_service.TemplateManagementService')
    def test_recommend_templates_by_database_type(self, mock_template_service):
        """Test template recommendation by database type."""
        # Mock the template service
        mock_template_service.return_value.get_templates.return_value = self.mock_templates
        service = IntelligentTemplateService()
        
        # Test PostgreSQL recommendation
        rule_attributes = RuleAttributes(
            rule_name="住院患者超每日数量检查",
            database_type="postgresql",
            patient_type="inpatient"
        )
        
        recommendations = service.recommend_templates(rule_attributes, max_recommendations=5)
        
        # Should recommend PostgreSQL templates first
        self.assertGreater(len(recommendations), 0)
        self.assertEqual(recommendations[0].template.database_type, "postgresql")
        self.assertIn("数据库类型匹配", recommendations[0].reasons[0])
    
    @patch('services.intelligent_template_service.TemplateManagementService')
    def test_recommend_templates_by_patient_type(self, mock_template_service):
        """Test template recommendation by patient type."""
        mock_template_service.return_value.get_templates.return_value = self.mock_templates
        service = IntelligentTemplateService()
        
        # Test outpatient recommendation
        rule_attributes = RuleAttributes(
            rule_name="门诊限性别检查",
            patient_type="outpatient"
        )
        
        recommendations = service.recommend_templates(rule_attributes, max_recommendations=5)
        
        # Should find outpatient templates
        outpatient_templates = [r for r in recommendations if r.template.patient_type == "outpatient"]
        self.assertGreater(len(outpatient_templates), 0)
    
    @patch('services.intelligent_template_service.TemplateManagementService')
    def test_filter_templates_by_attributes(self, mock_template_service):
        """Test template filtering by attributes."""
        mock_template_service.return_value.get_templates.return_value = self.mock_templates
        service = IntelligentTemplateService()
        
        rule_attributes = RuleAttributes(
            rule_name="测试规则",
            database_type="postgresql",
            patient_type="inpatient"
        )
        
        filtered_templates = service.filter_templates_by_attributes(rule_attributes)
        
        # Should only return PostgreSQL inpatient templates or general templates
        for template in filtered_templates:
            self.assertTrue(
                (template.database_type == "postgresql" or template.database_type == "general") and
                (template.patient_type == "inpatient" or template.patient_type == "general")
            )
    
    def test_extract_attributes_from_name(self):
        """Test attribute extraction from rule names."""
        # Test rule type extraction
        rule_name = "住院患者超每日数量检查"
        attributes = self.service._extract_attributes_from_name(rule_name)
        
        self.assertEqual(attributes.rule_type, "超频次")
        self.assertEqual(attributes.patient_type, "inpatient")
        
        # Test gender restriction rule
        rule_name = "门诊限性别检查"
        attributes = self.service._extract_attributes_from_name(rule_name)
        
        self.assertEqual(attributes.rule_type, "限性别")
        self.assertEqual(attributes.patient_type, "outpatient")
        
        # Test duplicate billing rule
        rule_name = "重复收费检查"
        attributes = self.service._extract_attributes_from_name(rule_name)
        
        self.assertEqual(attributes.rule_type, "重复收费")
    
    def test_calculate_template_score(self):
        """Test template scoring algorithm."""
        template = self.mock_templates[0]  # PostgreSQL inpatient template
        
        rule_attributes = RuleAttributes(
            rule_name="住院患者超每日数量检查",
            database_type="postgresql",
            patient_type="inpatient",
            rule_type="超频次"
        )
        
        score, reasons = self.service._calculate_template_score(template, rule_attributes)
        
        # Should have high score for exact matches
        self.assertGreater(score, 50.0)  # Database + patient type + rule type matches
        self.assertGreater(len(reasons), 2)
    
    def test_analyze_rule_attributes(self):
        """Test rule attribute analysis."""
        rule_name = "住院患者超每日数量检查按名称"
        
        analysis = self.service.analyze_rule_attributes(rule_name)
        
        self.assertEqual(analysis["rule_name"], rule_name)
        self.assertEqual(analysis["detected_rule_type"], "超频次")
        self.assertEqual(analysis["detected_patient_type"], "inpatient")
        self.assertEqual(analysis["detected_match_method"], "name")
        self.assertGreater(analysis["confidence"], 0.5)
    
    @patch('services.intelligent_template_service.TemplateManagementService')
    def test_get_filtered_templates(self, mock_template_service):
        """Test getting filtered templates with multiple criteria."""
        mock_template_service.return_value.get_templates.return_value = self.mock_templates
        service = IntelligentTemplateService()
        
        # Filter by database type and patient type
        filtered = service.get_filtered_templates(
            database_type="postgresql",
            patient_type="inpatient"
        )
        
        # Should return only PostgreSQL inpatient templates (and general ones)
        for template in filtered:
            self.assertTrue(
                template.database_type in ["postgresql", "general"] and
                template.patient_type in ["inpatient", "general"]
            )
    
    def test_rule_type_keywords_matching(self):
        """Test rule type keyword matching."""
        # Test various rule types
        test_cases = [
            ("超每日数量", "超频次"),
            ("重复收费检查", "重复收费"),
            ("限制性别", "限性别"),
            ("病例提取规则", "病例提取"),
            ("多项合并超天数", "多项合并")
        ]
        
        for rule_name, expected_type in test_cases:
            attributes = self.service._extract_attributes_from_name(rule_name)
            self.assertEqual(attributes.rule_type, expected_type, 
                           f"Failed for rule: {rule_name}")
    
    def test_patient_type_detection(self):
        """Test patient type detection from rule names."""
        test_cases = [
            ("住院患者检查", "inpatient"),
            ("门诊限制规则", "outpatient"),
            ("入院诊断检查", "inpatient"),
            ("出院后检查", "inpatient")
        ]
        
        for rule_name, expected_type in test_cases:
            attributes = self.service._extract_attributes_from_name(rule_name)
            self.assertEqual(attributes.patient_type, expected_type,
                           f"Failed for rule: {rule_name}")
    
    def test_match_method_detection(self):
        """Test match method detection from rule names."""
        test_cases = [
            ("按名称匹配规则", "name"),
            ("按编码检查", "code"),
            ("医保名称检查", "name"),
            ("项目编码验证", "code")
        ]
        
        for rule_name, expected_method in test_cases:
            attributes = self.service._extract_attributes_from_name(rule_name)
            self.assertEqual(attributes.match_method, expected_method,
                           f"Failed for rule: {rule_name}")


if __name__ == '__main__':
    unittest.main()