# 产品评估指标框架 (Metrics Framework) - 临时规则编写工具

## 1. 指标框架概述

本框架旨在定义一套清晰、可衡量的指标体系，用于评估“临时规则编写工具”的成功与否。通过持续追踪这些指标，我们可以量化产品表现，理解用户行为，并为未来的产品迭代和优化提供数据驱动的决策依据。

## 2. 北极星指标 (North Star Metric)

- **定义**: **成功生成的有效规则数**
- **选择依据**: 该指标直接反映了产品的核心价值——帮助用户高效、准确地创建可用的风控或稽核规则。每一个成功生成的有效规则都代表用户完成了一次核心任务，并为业务带来了实际价值。
- **目标**: MVP阶段，月均成功生成有效规则数达到 100 个。

## 3. 核心指标体系 (AARRR 模型)

我们将采用 AARRR 用户生命周期模型来构建核心指标体系。

#### 3.1 Acquisition (用户获取)
* **关键问题**: 用户从哪里来？
* **核心指标**:
  * **用户注册/首次访问量**: 衡量工具的触达范围和吸引力。
  * **渠道来源分布**: 分析不同推广或通知渠道的效果。

#### 3.2 Activation (用户激活)
* **关键问题**: 用户的首次体验是否良好？
* **核心指标**:
  * **新用户规则创建转化率**: (首次访问后7天内成功创建至少一条规则的用户数) / (新用户总数)。这是衡量产品“啊哈时刻”的关键指标。
  - **模板选用率**: (使用模板生成规则的用户数) / (所有创建规则的用户数)。反映模板功能的吸引力和实用性。

#### 3.3 Retention (用户留存)
* **关键问题**: 用户是否会持续使用？
* **核心指标**:
  * **次周留存率**: (本周创建过规则的用户中，下周仍创建规则的用户比例)。衡量产品的粘性。
  * **规则创建频率**: (单位时间内，活跃用户平均创建规则的数量)。反映产品的被依赖程度。

#### 3.4 Revenue (商业收入)
* **关键问题**: (当前阶段不适用) - 该工具为内部效率工具，不直接产生收入。

#### 3.5 Referral (用户推荐)
* **关键问题**: 用户是否愿意推荐他人使用？
* **核心指标**:
  * **净推荐值 (NPS)** (未来考虑): 通过调研询问用户向同事推荐的可能性。

## 4. 功能级评估指标

| 功能模块 | 关键指标 | 指标描述 | 成功标准 (初步) |
| :--- | :--- | :--- | :--- |
| **规则SQL生成** | **模板生成成功率** | (通过模板成功生成SQL的次数) / (所有尝试使用模板的次数) | > 95% |
| | **平均规则生成时长** | 从进入“规则SQL生成”页面到成功保存一条规则的平均时间 | < 3分钟 |
| **规则管理** | **规则查看/编辑频率** | 活跃用户平均每周查看或编辑已有规则的次数 | 衡量用户对规则进行迭代管理的频繁程度 |
| | **规则删除率** | (被删除的规则数) / (创建的总规则数) | 辅助判断规则的有效性和时效性 |

## 5. 指标监测计划

- **数据收集**: 
  - **前端埋点**: 关键的用户操作，如按钮点击（“生成SQL”、“保存规则”）、页面访问（规则列表、规则生成页）。
  - **后端日志**: 记录每一次规则的创建、修改、删除事件，包括规则ID、用户ID、时间戳、是否使用模板、模板类型等。
- **数据看板**: 使用内部数据分析工具（如 Superset, Metabase 或自研平台）创建产品指标看板。
- **报告频率**: 
  - **周报**: 核心指标（北极星、AARRR）的周度趋势报告。
  - **月报**: 进行深度分析，评估产品迭代效果，并为下月规划提供输入。