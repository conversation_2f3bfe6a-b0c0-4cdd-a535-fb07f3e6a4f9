# 技术方案设计文档 - 规则编辑器

## 1. 文档信息

| 版本 | 日期       | 作者 | 变更内容 |
| ---- | ---------- | ---- | -------- |
| 1.0  | 2023-10-27 | AI   | 初稿创建 |

### 1.2 文档目的

本文档旨在为“临时规则编辑器”模块提供详细的技术设计方案，指导后续的开发、测试和维护工作。它将明确功能需求、系统设计、接口定义和技术选型，确保最终交付的软件符合预期。

### 1.3 相关文档引用

*   [产品需求文档 (PRD)](PRD.md)
*   [系统架构设计文档](System_Architecture_Design.md)

## 2. 方案概述

### 2.1 设计目标与范围

**设计目标**：创建一个高效、易用的Web界面，用于动态生成、编辑和管理基于SQL模板的业务规则。该编辑器应能让非技术人员也能方便地调整规则参数，并实时预览生成的SQL代码。

**范围**：

*   **前端**：实现一个单页应用（SPA），包含规则模板选择、条件参数输入、SQL代码实时预览和语法高亮功能。
*   **后端**：提供API接口，用于获取SQL模板、解析模板内容，并支持未来的规则保存和管理功能。

### 2.2 功能需求回顾

*   **FR-1**: 用户可以选择一个SQL模板作为基础。
*   **FR-2**: 用户可以根据模板中的占位符，输入或选择相应的条件参数。
*   **FR-3**: 系统根据用户输入的参数，实时生成最终的SQL查询语句。
*   **FR-4**: 生成的SQL代码应进行语法高亮显示，便于阅读。
*   **FR-5**: 用户应能方便地复制生成的SQL代码。

### 2.3 非功能需求考量

*   **性能**: 前后端交互应快速响应，SQL生成过程不应有明显延迟。
*   **可用性**: 界面设计应直观、简洁，用户操作流程应尽可能简化。
*   **可维护性**: 代码结构应清晰，模块化程度高，便于未来扩展和维护。
*   **安全性**: 所有用户输入都应经过适当的验证和清理，防止SQL注入等安全风险。

### 2.4 技术栈与依赖

*   **后端**: Python, Flask
*   **前端**: HTML, CSS (Tailwind CSS), JavaScript
*   **代码高亮**: Highlight.js
*   **图标**: FontAwesome

## 3. 系统/模块设计

### 3.1 模块划分与职责

```mermaid
graph TD
    A[用户界面 (Frontend)] -- API请求 --> B[后端服务 (Backend)]
    B -- 读取模板 --> C[SQL模板存储 (templates/rule)]

    subgraph 前端
        A
    end

    subgraph 后端
        B
    end

    subgraph 文件系统
        C
    end
```

*   **用户界面 (Frontend)**: 负责渲染页面、处理用户交互、调用后端API并展示数据。
*   **后端服务 (Backend)**: 负责处理业务逻辑，提供API接口，如获取SQL模板列表和内容。
*   **SQL模板存储**: 以`.sql`文件形式存储在服务器的文件系统中。

### 3.4 接口设计

#### 3.4.1 获取SQL模板列表

*   **Endpoint**: `GET /api/sql-templates`
*   **描述**: 获取所有可用的SQL模板的元信息（ID、描述）和完整内容。
*   **请求**: 无
*   **响应 (200 OK)**:

    ```json
    [
        {
            "id": "template_id_1",
            "description": "模板1的描述",
            "content": "SELECT * FROM table WHERE condition = '{{placeholder}}';"
        },
        {
            "id": "template_id_2",
            "description": "模板2的描述",
            "content": "SELECT COUNT(*) FROM another_table;"
        }
    ]
    ```

## 4. 核心流程与逻辑设计

### 4.1 关键业务流程详述

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend

    User->>Frontend: 打开规则编辑器页面
    Frontend->>Backend: 请求SQL模板 (GET /api/sql-templates)
    Backend-->>Frontend: 返回模板列表 (JSON)
    Frontend->>User: 显示模板选择下拉框
    User->>Frontend: 选择一个模板
    Frontend->>Frontend: 根据模板内容，解析并渲染参数输入界面
    User->>Frontend: 输入规则参数
    Frontend->>Frontend: 实时更新SQL预览
    User->>Frontend: 点击“复制”按钮
    Frontend->>User: 将SQL复制到剪贴板
```

## 7. 测试方案 (开发自测)

### 7.1 单元测试计划

*   **后端**:
    *   测试 `/api/sql-templates` 接口能否正确返回模板数据。
    *   测试模板文件不存在或格式错误时的异常处理。
*   **前端**:
    *   测试模板加载和渲染逻辑。
    *   测试参数输入与SQL实时生成逻辑的正确性。
    *   测试特殊字符输入的处理。

## 9. 未来演进与扩展性考量

*   **规则保存**: 增加后端API和数据库支持，允许用户保存、加载和管理已配置的规则。
*   **参数类型扩展**: 支持更多参数类型，如日期选择器、多选框等。
*   **模板管理**: 开发一个独立的界面，用于上传、编辑和管理SQL模板文件。