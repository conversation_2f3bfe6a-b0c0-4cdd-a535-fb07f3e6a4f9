<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schema搜索功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { background-color: #1f2937; color: #d1d5db; }
    </style>
</head>
<body class="p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Schema搜索功能测试</h1>
        
        <div class="space-y-6">
            <!-- 主页面Schema选择器测试 -->
            <div class="bg-gray-800 p-4 rounded-lg">
                <h2 class="text-lg font-semibold mb-4">主页面Schema选择器</h2>
                <div>
                    <label class="block mb-2 text-sm font-medium">选择Schema:</label>
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                id="schemaSelect" onchange="onSchemaSelectChange()" onfocus="showSchemaSearchInput()">
                            <option value="">请先输入主机IP</option>
                        </select>
                    </div>
                    <!-- 搜索模式输入框（默认隐藏） -->
                    <div id="schemaSearchContainer" class="relative hidden mt-2">
                        <input type="text" 
                               class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                               id="schemaSearchInput" 
                               placeholder="输入Schema名称进行搜索..."
                               autocomplete="off"
                               oninput="filterSchemas(this.value)"
                               onfocus="showSchemaDropdown()"
                               onblur="setTimeout(hideSchemaDropdown, 200)">
                        <div id="schemaDropdown" class="absolute z-50 w-full bg-gray-700 border border-gray-600 rounded-lg mt-1 max-h-60 overflow-y-auto hidden">
                            <!-- Schema选项将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 医保Schema选择器测试 -->
            <div class="bg-gray-800 p-4 rounded-lg">
                <h2 class="text-lg font-semibold mb-4">医保Schema选择器</h2>
                <div>
                    <label class="block mb-2 text-sm font-medium">选择Schema:</label>
                    <div class="relative">
                        <select class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                id="medical-schema-select" onchange="onMedicalSchemaSelectChange()" onfocus="showMedicalSchemaSearchInput()">
                            <option value="">选择Schema</option>
                        </select>
                    </div>
                    <!-- 医保Schema搜索模式输入框（默认隐藏） -->
                    <div id="medicalSchemaSearchContainer" class="relative hidden mt-2">
                        <input type="text" 
                               class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                               id="medicalSchemaSearchInput" 
                               placeholder="输入Schema名称进行搜索..."
                               autocomplete="off"
                               oninput="filterMedicalSchemas(this.value)"
                               onfocus="showMedicalSchemaDropdown()"
                               onblur="setTimeout(hideMedicalSchemaDropdown, 200)">
                        <div id="medicalSchemaDropdown" class="absolute z-50 w-full bg-gray-700 border border-gray-600 rounded-lg mt-1 max-h-60 overflow-y-auto hidden">
                            <!-- Schema选项将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 测试按钮 -->
            <div class="bg-gray-800 p-4 rounded-lg">
                <h2 class="text-lg font-semibold mb-4">测试控制</h2>
                <div class="space-x-4">
                    <button onclick="loadTestSchemas()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                        加载测试Schema
                    </button>
                    <button onclick="testSchemaSearch()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                        测试Schema搜索
                    </button>
                    <button onclick="clearSchemas()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">
                        清空Schema
                    </button>
                </div>
            </div>
            
            <!-- 调试信息 -->
            <div class="bg-gray-800 p-4 rounded-lg">
                <h2 class="text-lg font-semibold mb-4">调试信息</h2>
                <div id="debug-info" class="text-sm text-gray-400">
                    等待测试...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let allSchemas = [];
        
        // 测试用的Schema列表
        const testSchemas = [
            'public', 'information_schema', 'pg_catalog', 'pg_toast', 'pg_temp_1',
            'medical_data', 'patient_info', 'treatment_records', 'billing_data',
            'admin_schema', 'user_schema', 'temp_schema', 'backup_schema'
        ];
        
        // 加载测试Schema
        function loadTestSchemas() {
            allSchemas = [...testSchemas];
            updateDebugInfo('已加载测试Schema: ' + allSchemas.length + ' 个');
        }
        
        // 清空Schema
        function clearSchemas() {
            allSchemas = [];
            updateDebugInfo('已清空Schema列表');
        }
        
        // 测试Schema搜索
        function testSchemaSearch() {
            updateDebugInfo('allSchemas: ' + JSON.stringify(allSchemas));
            updateDebugInfo('allSchemas length: ' + allSchemas.length);
        }
        
        // 更新调试信息
        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }
        
        // Schema选择器相关函数
        function onSchemaSelectChange() {
            console.log('Schema选择已改变:', document.getElementById('schemaSelect').value);
            updateDebugInfo('Schema选择已改变: ' + document.getElementById('schemaSelect').value);
        }
        
        function onMedicalSchemaSelectChange() {
            console.log('医保Schema选择已改变:', document.getElementById('medical-schema-select').value);
            updateDebugInfo('医保Schema选择已改变: ' + document.getElementById('medical-schema-select').value);
        }
        
        // 显示Schema搜索输入框
        function showSchemaSearchInput() {
            console.log('showSchemaSearchInput 被调用');
            updateDebugInfo('showSchemaSearchInput 被调用');
            
            const schemaSelect = document.getElementById('schemaSelect');
            const schemaSearchContainer = document.getElementById('schemaSearchContainer');
            const schemaSearchInput = document.getElementById('schemaSearchInput');
            
            console.log('allSchemas:', allSchemas);
            console.log('allSchemas length:', allSchemas ? allSchemas.length : 'undefined');
            updateDebugInfo('allSchemas length: ' + (allSchemas ? allSchemas.length : 'undefined'));
            
            // 如果Schema列表已加载且有数据，则显示搜索输入框
            if (allSchemas && allSchemas.length > 0) {
                console.log('显示搜索输入框');
                updateDebugInfo('显示搜索输入框');
                schemaSelect.style.display = 'none';
                schemaSearchContainer.classList.remove('hidden');
                schemaSearchInput.focus();
                
                // 显示所有Schema选项
                showSchemaDropdown();
                filterSchemas('');
            } else {
                console.log('Schema列表未加载或为空');
                updateDebugInfo('Schema列表未加载或为空');
            }
        }
        
        // 显示医保Schema搜索输入框
        function showMedicalSchemaSearchInput() {
            console.log('showMedicalSchemaSearchInput 被调用');
            updateDebugInfo('showMedicalSchemaSearchInput 被调用');
            
            const medicalSchemaSelect = document.getElementById('medical-schema-select');
            const medicalSchemaSearchContainer = document.getElementById('medicalSchemaSearchContainer');
            const medicalSchemaSearchInput = document.getElementById('medicalSchemaSearchInput');
            
            console.log('allSchemas:', allSchemas);
            console.log('allSchemas length:', allSchemas ? allSchemas.length : 'undefined');
            updateDebugInfo('allSchemas length: ' + (allSchemas ? allSchemas.length : 'undefined'));
            
            // 如果Schema列表已加载且有数据，则显示搜索输入框
            if (allSchemas && allSchemas.length > 0) {
                console.log('显示医保搜索输入框');
                updateDebugInfo('显示医保搜索输入框');
                medicalSchemaSelect.style.display = 'none';
                medicalSchemaSearchContainer.classList.remove('hidden');
                medicalSchemaSearchInput.focus();
                
                // 显示所有Schema选项
                showMedicalSchemaDropdown();
                filterMedicalSchemas('');
            } else {
                console.log('Schema列表未加载或为空');
                updateDebugInfo('Schema列表未加载或为空');
            }
        }
        
        // Schema下拉框相关函数
        function showSchemaDropdown() {
            const dropdown = document.getElementById('schemaDropdown');
            if (dropdown) {
                dropdown.classList.remove('hidden');
            }
        }
        
        function hideSchemaDropdown() {
            const dropdown = document.getElementById('schemaDropdown');
            if (dropdown) {
                dropdown.classList.add('hidden');
            }
        }
        
        function filterSchemas(searchText) {
            const dropdown = document.getElementById('schemaDropdown');
            if (!dropdown) return;
            
            const filteredSchemas = allSchemas.filter(schema => 
                schema.toLowerCase().includes(searchText.toLowerCase())
            );
            
            dropdown.innerHTML = '';
            
            if (filteredSchemas.length === 0) {
                dropdown.innerHTML = '<div class="px-4 py-2 text-gray-400 text-sm">无匹配的Schema</div>';
            } else {
                filteredSchemas.forEach(schema => {
                    const item = document.createElement('div');
                    item.className = 'px-4 py-2 text-white text-sm hover:bg-gray-600 cursor-pointer';
                    item.textContent = schema;
                    item.onclick = () => selectSchema(schema);
                    dropdown.appendChild(item);
                });
            }
            
            if (searchText && filteredSchemas.length > 0) {
                showSchemaDropdown();
            } else {
                hideSchemaDropdown();
            }
        }
        
        function selectSchema(schema) {
            const schemaSelect = document.getElementById('schemaSelect');
            const schemaSearchInput = document.getElementById('schemaSearchInput');
            const schemaSearchContainer = document.getElementById('schemaSearchContainer');
            
            // 设置下拉框的值
            schemaSelect.value = schema;
            
            // 切换回下拉模式
            schemaSelect.style.display = 'block';
            schemaSearchContainer.classList.add('hidden');
            schemaSearchInput.value = '';
            hideSchemaDropdown();
            
            updateDebugInfo('已选择Schema: ' + schema);
        }
        
        // 医保Schema相关函数
        function showMedicalSchemaDropdown() {
            const dropdown = document.getElementById('medicalSchemaDropdown');
            if (dropdown) {
                dropdown.classList.remove('hidden');
            }
        }
        
        function hideMedicalSchemaDropdown() {
            const dropdown = document.getElementById('medicalSchemaDropdown');
            if (dropdown) {
                dropdown.classList.add('hidden');
            }
        }
        
        function filterMedicalSchemas(searchText) {
            const dropdown = document.getElementById('medicalSchemaDropdown');
            if (!dropdown) return;
            
            const filteredSchemas = allSchemas.filter(schema => 
                schema.toLowerCase().includes(searchText.toLowerCase())
            );
            
            dropdown.innerHTML = '';
            
            if (filteredSchemas.length === 0) {
                dropdown.innerHTML = '<div class="px-4 py-2 text-gray-400 text-sm">无匹配的Schema</div>';
            } else {
                filteredSchemas.forEach(schema => {
                    const item = document.createElement('div');
                    item.className = 'px-4 py-2 text-white text-sm hover:bg-gray-600 cursor-pointer';
                    item.textContent = schema;
                    item.onclick = () => selectMedicalSchema(schema);
                    dropdown.appendChild(item);
                });
            }
            
            if (searchText && filteredSchemas.length > 0) {
                showMedicalSchemaDropdown();
            } else {
                hideMedicalSchemaDropdown();
            }
        }
        
        function selectMedicalSchema(schema) {
            const medicalSchemaSelect = document.getElementById('medical-schema-select');
            const medicalSchemaSearchInput = document.getElementById('medicalSchemaSearchInput');
            const medicalSchemaSearchContainer = document.getElementById('medicalSchemaSearchContainer');
            
            // 设置下拉框的值
            medicalSchemaSelect.value = schema;
            
            // 切换回下拉模式
            medicalSchemaSelect.style.display = 'block';
            medicalSchemaSearchContainer.classList.add('hidden');
            medicalSchemaSearchInput.value = '';
            hideMedicalSchemaDropdown();
            
            updateDebugInfo('已选择医保Schema: ' + schema);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('页面加载完成，可以开始测试');
        });
    </script>
</body>
</html> 