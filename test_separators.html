<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分隔符测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-section {
            background-color: #2d2d2d;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            background-color: #444;
            border: 1px solid #666;
            border-radius: 4px;
            color: #fff;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #1e1e1e;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .separator-info {
            background-color: #2a4a2a;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <h1>分隔符功能测试</h1>
    
    <div class="separator-info">
        <h3>支持的分隔符：</h3>
        <ul>
            <li><strong>逗号</strong>：, （英文逗号）</li>
            <li><strong>中文逗号</strong>：，（中文逗号）</li>
            <li><strong>顿号</strong>：、（中文顿号）</li>
            <li><strong>竖线</strong>：| （管道符）</li>
            <li><strong>分号</strong>：; （英文分号）</li>
            <li><strong>中文分号</strong>：；（中文分号）</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试1：医保项目名称处理</h2>
        <p>输入多个医保项目名称，使用不同分隔符：</p>
        <input type="text" id="medical-names" placeholder="例如：阿司匹林,布洛芬、对乙酰氨基酚|维生素C；钙片">
        <button onclick="testMedicalNames()">测试医保名称处理</button>
        <div id="medical-names-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>测试2：IN操作符处理</h2>
        <p>测试IN操作符的多值处理：</p>
        <input type="text" id="in-values" placeholder="例如：值1,值2、值3|值4；值5">
        <button onclick="testInOperator()">测试IN操作符</button>
        <div id="in-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>测试3：ILIKE ANY数组处理</h2>
        <p>测试诊断或科室的ILIKE ANY处理：</p>
        <input type="text" id="ilike-values" placeholder="例如：内科,外科、儿科|妇科；骨科">
        <button onclick="testIlikeAny()">测试ILIKE ANY</button>
        <div id="ilike-result" class="result"></div>
    </div>

    <script>
        // 复制临时规则编写工具中的分隔符处理函数
        function processMedicalNames(value) {
            if (value.includes(',') || value.includes('，') || value.includes('、') || value.includes('|')) {
                const items = value.split(/[,，;；、|\s\n]+/).map(v => v.trim()).filter(v => v);
                return items.map(item => `'${item}'`).join(', ');
            } else {
                return `'${value}'`;
            }
        }

        function processInOperator(value) {
            const values = value.split(/[,，；;、|]+/).map(v => `'${v.trim()}'`).join(', ');
            return `(${values})`;
        }

        function buildIlikeAnyArray(keywords) {
            if (!keywords) {
                return "ARRAY[]::text[]";
            }
            // 支持多种分隔符：逗号、顿号、竖线、分号等
            let processedKeywords = keywords;
            for (const sep of ['，', ',', '、', '|', ';', '；']) {
                processedKeywords = processedKeywords.replace(new RegExp(sep.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '|');
            }
            // 分割并去除空白
            const items = processedKeywords.split('|').map(kw => kw.trim()).filter(kw => kw);
            if (items.length === 0) {
                return "ARRAY[]::text[]";
            }
            // 拼接成SQL数组
            const arrayStr = items.map(kw => `'%${kw}%'`).join(',');
            return `ARRAY[${arrayStr}]`;
        }

        function testMedicalNames() {
            const input = document.getElementById('medical-names').value;
            const result = processMedicalNames(input);
            document.getElementById('medical-names-result').textContent = 
                `输入: ${input}\n处理结果: ${result}`;
        }

        function testInOperator() {
            const input = document.getElementById('in-values').value;
            const result = processInOperator(input);
            document.getElementById('in-result').textContent = 
                `输入: ${input}\n处理结果: ${result}`;
        }

        function testIlikeAny() {
            const input = document.getElementById('ilike-values').value;
            const result = buildIlikeAnyArray(input);
            document.getElementById('ilike-result').textContent = 
                `输入: ${input}\n处理结果: ${result}`;
        }
    </script>
</body>
</html>
