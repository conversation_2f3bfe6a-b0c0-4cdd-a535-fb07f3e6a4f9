#!/usr/bin/env python3
"""
测试API端点功能
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:5000"

def test_database_connections_api():
    """测试数据库连接管理API"""
    print("=== 测试数据库连接管理API ===")
    
    # 1. 测试获取连接列表
    print("1. 测试获取连接列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/database/connections")
        result = response.json()
        
        if result.get('success'):
            connections = result.get('connections', [])
            print(f"✓ 获取连接列表成功，找到 {len(connections)} 个连接")
        else:
            print(f"✗ 获取连接列表失败: {result.get('error')}")
    except Exception as e:
        print(f"✗ 请求失败: {str(e)}")
    
    # 2. 测试保存连接
    print("2. 测试保存连接...")
    test_connection = {
        "name": "测试连接",
        "description": "用于测试的数据库连接",
        "database_type": "postgresql",
        "host": "**************",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/database/connections", json=test_connection)
        result = response.json()
        
        if result.get('success'):
            connection_id = result['connection']['id']
            print(f"✓ 保存连接成功，ID: {connection_id}")
            
            # 3. 测试删除连接
            print("3. 测试删除连接...")
            response = requests.delete(f"{BASE_URL}/api/database/connections/{connection_id}")
            result = response.json()
            
            if result.get('success'):
                print("✓ 删除连接成功")
            else:
                print(f"✗ 删除连接失败: {result.get('error')}")
        else:
            print(f"✗ 保存连接失败: {result.get('error')}")
    except Exception as e:
        print(f"✗ 请求失败: {str(e)}")

def test_rule_import_api():
    """测试规则导入API"""
    print("\n=== 测试规则导入API ===")
    
    # 1. 测试扫描规则（使用手动连接）
    print("1. 测试扫描规则...")
    connection_data = {
        "host": "**************",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/rules/import", json={
            "connection_data": connection_data
        })
        result = response.json()
        
        if result.get('success'):
            rules = result.get('rules', [])
            print(f"✓ 扫描规则成功，找到 {len(rules)} 个可导入规则")
            
            if rules:
                # 2. 测试执行导入
                print("2. 测试执行导入...")
                selected_rules = [rules[0]['id']]  # 选择第一个规则
                
                response = requests.post(f"{BASE_URL}/api/rules/import/execute", json={
                    "selected_rules": selected_rules,
                    "overwrite": False,
                    "connection_data": connection_data
                })
                result = response.json()
                
                if result.get('success'):
                    print(f"✓ 导入成功: {result.get('imported_count', 0)} 个规则")
                else:
                    print(f"✗ 导入失败: {result.get('error')}")
            else:
                print("ℹ 没有找到可导入的规则")
        else:
            print(f"✗ 扫描规则失败: {result.get('error')}")
    except Exception as e:
        print(f"✗ 请求失败: {str(e)}")

def test_database_test_connection_api():
    """测试数据库连接测试API"""
    print("\n=== 测试数据库连接测试API ===")
    
    test_data = {
        "host": "**************",
        "port": 5432,
        "database": "databasetools",
        "username": "postgres",
        "password": "P@ssw0rd"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/database/test-connection", json=test_data)
        result = response.json()
        
        if result.get('success'):
            print("✓ 数据库连接测试成功")
        else:
            print(f"✗ 数据库连接测试失败: {result.get('error')}")
    except Exception as e:
        print(f"✗ 请求失败: {str(e)}")

def main():
    """主测试函数"""
    print("开始测试API端点...")
    print("=" * 50)
    
    try:
        # 测试数据库连接管理API
        test_database_connections_api()
        
        # 测试规则导入API
        test_rule_import_api()
        
        # 测试数据库连接测试API
        test_database_test_connection_api()
        
        print("\n" + "=" * 50)
        print("API测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("✗ 无法连接到服务器，请确保服务器正在运行")
        print("启动服务器命令: python app.py")
    except Exception as e:
        print(f"✗ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main() 