"""
Template management API controllers.
"""
from flask import jsonify, request
from services.template_service import TemplateManagementService
from services.sql_generation_service import SqlGenerationService
from utils.error_handler import <PERSON>rror<PERSON><PERSON>ler


class TemplateController:
    """Controller for template management endpoints."""
    
    def __init__(self):
        self.template_service = TemplateManagementService()
        self.sql_generation_service = SqlGenerationService()
    
    def get_sql_templates(self):
        """Handle GET /api/sql-templates - Get all SQL templates."""
        try:
            templates = self.template_service.get_templates()
            
            # Convert to the format expected by the frontend
            templates_data = [
                {
                    'id': template.id,
                    'description': template.description,
                    'sql': template.sql
                }
                for template in templates
            ]
            
            return jsonify(templates_data)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_templates_dict(self):
        """Get templates as dictionary for backward compatibility."""
        try:
            templates_dict = self.template_service.get_templates_as_dict()
            return jsonify(templates_dict)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_template_categories(self):
        """Handle GET /api/template-categories - Get all template categories."""
        try:
            categories = self.template_service.get_template_categories()
            
            categories_data = [
                {
                    'id': category.id,
                    'name': category.name,
                    'description': category.description,
                    'database_type': category.database_type,
                    'patient_type': category.patient_type
                }
                for category in categories
            ]
            
            return jsonify(categories_data)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_templates_by_category(self, category_id: str):
        """Handle GET /api/template-categories/{category_id}/templates - Get templates by category."""
        try:
            templates = self.template_service.get_templates_by_category(category_id)
            
            templates_data = [
                {
                    'id': template.id,
                    'description': template.description,
                    'category': template.category,
                    'parameters': [
                        {
                            'name': param.name,
                            'description': param.description,
                            'parameter_type': param.parameter_type,
                            'required': param.required,
                            'default_value': param.default_value
                        }
                        for param in (template.parameters or [])
                    ]
                }
                for template in templates
            ]
            
            return jsonify(templates_data)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def generate_sql(self):
        """Handle POST /api/sql/generate - Generate SQL from template."""
        try:
            data = request.get_json()
            
            if not data:
                return jsonify({'error': 'Request body is required'}), 400
            
            template_id = data.get('template_id')
            parameters = data.get('parameters', {})
            database_type = data.get('database_type', 'postgresql')
            
            if not template_id:
                return jsonify({'error': 'template_id is required'}), 400
            
            # Generate SQL
            result = self.sql_generation_service.generate_sql(
                template_id=template_id,
                parameters=parameters,
                database_type=database_type
            )
            
            response_data = {
                'sql': result.sql,
                'template_id': result.template_id,
                'parameters': result.parameters,
                'database_type': result.database_type
            }
            
            if result.validation_result and result.validation_result.warnings:
                response_data['warnings'] = result.validation_result.warnings
            
            return jsonify(response_data)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def preview_sql(self):
        """Handle POST /api/sql/preview - Preview SQL generation."""
        try:
            data = request.get_json()
            
            if not data:
                return jsonify({'error': 'Request body is required'}), 400
            
            template_id = data.get('template_id')
            parameters = data.get('parameters', {})
            database_type = data.get('database_type', 'postgresql')
            
            if not template_id:
                return jsonify({'error': 'template_id is required'}), 400
            
            # Preview SQL
            sql = self.sql_generation_service.preview_sql(
                template_id=template_id,
                parameters=parameters,
                database_type=database_type
            )
            
            return jsonify({'sql': sql})
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_template_parameters(self, template_id: str):
        """Handle GET /api/templates/{template_id}/parameters - Get template parameters."""
        try:
            parameters = self.sql_generation_service.get_template_parameters(template_id)
            
            parameters_data = [
                {
                    'name': param.name,
                    'description': param.description,
                    'parameter_type': param.parameter_type,
                    'required': param.required,
                    'default_value': param.default_value
                }
                for param in parameters
            ]
            
            return jsonify(parameters_data)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_template_form_config(self, template_id: str):
        """Handle GET /api/templates/{template_id}/form-config - Get dynamic form configuration."""
        try:
            form_config = self.template_service.generate_dynamic_form_config(template_id)
            return jsonify(form_config)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_template_content(self, template_id: str):
        """Handle GET /api/templates/{template_id}/content - Get template content."""
        try:
            content = self.template_service.get_template_content(template_id)
            return jsonify({'content': content})
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def clear_template_cache(self):
        """Handle POST /api/templates/cache/clear - Clear template cache."""
        try:
            self.template_service.clear_cache()
            return jsonify({'message': 'Template cache cleared successfully'})
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_templates_by_database_type(self, database_type: str):
        """Handle GET /api/templates/database/{database_type} - Get templates by database type."""
        try:
            templates = self.template_service.get_templates_by_database_type(database_type)
            
            templates_data = [
                {
                    'id': template.id,
                    'description': template.description,
                    'category': template.category,
                    'database_type': template.database_type,
                    'patient_type': template.patient_type,
                    'parameters': [
                        {
                            'name': param.name,
                            'description': param.description,
                            'parameter_type': param.parameter_type,
                            'required': param.required
                        }
                        for param in (template.parameters or [])
                    ]
                }
                for template in templates
            ]
            
            return jsonify(templates_data)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_templates_by_patient_type(self, patient_type: str):
        """Handle GET /api/templates/patient/{patient_type} - Get templates by patient type."""
        try:
            templates = self.template_service.get_templates_by_patient_type(patient_type)
            
            templates_data = [
                {
                    'id': template.id,
                    'description': template.description,
                    'category': template.category,
                    'database_type': template.database_type,
                    'patient_type': template.patient_type,
                    'parameters': [
                        {
                            'name': param.name,
                            'description': param.description,
                            'parameter_type': param.parameter_type,
                            'required': param.required
                        }
                        for param in (template.parameters or [])
                    ]
                }
                for template in templates
            ]
            
            return jsonify(templates_data)
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)
    
    def get_template_recommendations(self):
        """Handle POST /api/templates/recommend - Get template recommendations based on rule attributes."""
        try:
            from services.intelligent_template_service import IntelligentTemplateService, RuleAttributes
            
            data = request.get_json()
            
            if not data:
                return jsonify({'error': 'Request body is required'}), 400
            
            rule_name = data.get('rule_name')
            if not rule_name:
                return jsonify({'error': 'rule_name is required'}), 400
            
            # Create rule attributes from request data
            rule_attributes = RuleAttributes(
                rule_name=rule_name,
                rule_type=data.get('rule_type'),
                patient_type=data.get('patient_type'),
                match_method=data.get('match_method'),
                database_type=data.get('database_type', 'postgresql')
            )
            
            # Get recommendations
            intelligent_service = IntelligentTemplateService()
            recommendations = intelligent_service.recommend_templates(rule_attributes)
            
            # Convert to response format
            recommendations_data = [
                {
                    'template': {
                        'id': rec.template.id,
                        'description': rec.template.description,
                        'category': rec.template.category,
                        'database_type': rec.template.database_type,
                        'patient_type': rec.template.patient_type
                    },
                    'score': rec.score,
                    'reasons': rec.reasons,
                    'category_match': rec.category_match,
                    'name_match': rec.name_match,
                    'type_match': rec.type_match
                }
                for rec in recommendations
            ]
            
            return jsonify({
                'status': 'success',
                'data': recommendations_data
            })
            
        except Exception as e:
            return ErrorHandler.handle_generic_error(e)