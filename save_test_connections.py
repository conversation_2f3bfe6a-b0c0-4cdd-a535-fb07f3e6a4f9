#!/usr/bin/env python3
"""
保存测试连接
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:5000"

def save_test_connections():
    """保存测试连接"""
    print("保存测试连接...")
    
    # 测试连接数据
    test_connections = [
        {
            "name": "**************数据库",
            "description": "************** 数据库连接",
            "database_type": "postgresql",
            "host": "**************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        },
        {
            "name": "*************数据库",
            "description": "************* 数据库连接",
            "database_type": "postgresql",
            "host": "*************",
            "port": 5432,
            "database": "databasetools",
            "username": "postgres",
            "password": "P@ssw0rd"
        }
    ]
    
    try:
        for i, connection in enumerate(test_connections, 1):
            print(f"保存连接 {i}: {connection['name']} ({connection['host']})")
            
            response = requests.post(f"{BASE_URL}/api/database/connections", json=connection)
            result = response.json()
            
            if result.get('success'):
                connection_id = result['connection']['id']
                print(f"✓ 连接保存成功，ID: {connection_id}")
            else:
                print(f"✗ 连接保存失败: {result.get('error')}")
        
        print("\n✓ 测试连接保存完成！")
        print("现在可以访问 http://localhost:5000/test_frontend_connection.html 来测试前端连接加载功能")
        
    except Exception as e:
        print(f"✗ 保存失败: {str(e)}")

if __name__ == "__main__":
    save_test_connections() 