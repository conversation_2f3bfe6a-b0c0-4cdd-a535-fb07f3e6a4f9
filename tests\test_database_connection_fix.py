#!/usr/bin/env python3
"""
测试数据库连接修复的脚本
"""
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_connection import init_db_pool, get_postgresql_connection, close_postgresql_connection
from utils.config import Config

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_loading():
    """测试配置文件加载"""
    print("=== 测试配置文件加载 ===")
    try:
        config = Config()
        db_configs = config.load_database_config()
        print(f"成功加载配置: {db_configs}")
        return True
    except Exception as e:
        print(f"配置加载失败: {str(e)}")
        return False

def test_database_pool_initialization():
    """测试数据库连接池初始化"""
    print("\n=== 测试数据库连接池初始化 ===")
    try:
        oracle_pool, postgresql_pool = init_db_pool()
        print(f"Oracle连接池: {oracle_pool is not None}")
        print(f"PostgreSQL连接池: {postgresql_pool is not None}")
        return postgresql_pool is not None
    except Exception as e:
        print(f"连接池初始化失败: {str(e)}")
        return False

def test_postgresql_connection():
    """测试PostgreSQL连接"""
    print("\n=== 测试PostgreSQL连接 ===")
    try:
        connection = get_postgresql_connection()
        if connection:
            # 执行简单查询测试连接
            with connection.cursor() as cursor:
                cursor.execute("SELECT version();")
                version = cursor.fetchone()
                print(f"PostgreSQL版本: {version[0]}")
            close_postgresql_connection(connection)
            print("PostgreSQL连接测试成功")
            return True
        else:
            print("无法获取PostgreSQL连接")
            return False
    except Exception as e:
        print(f"PostgreSQL连接测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试数据库连接修复...")
    
    # 测试配置加载
    config_ok = test_config_loading()
    
    # 测试连接池初始化
    pool_ok = test_database_pool_initialization()
    
    # 测试实际连接
    connection_ok = test_postgresql_connection()
    
    print("\n=== 测试结果总结 ===")
    print(f"配置加载: {'✓' if config_ok else '✗'}")
    print(f"连接池初始化: {'✓' if pool_ok else '✗'}")
    print(f"实际连接测试: {'✓' if connection_ok else '✗'}")
    
    if config_ok and pool_ok and connection_ok:
        print("\n🎉 所有测试通过！数据库连接修复成功。")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 