#!/usr/bin/env python3
"""
Debug script to check template parameter parsing.
"""
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.template_service import TemplateManagementService
from services.intelligent_template_service import IntelligentTemplateService, RuleAttributes

def debug_template_parameters():
    """Debug template parameter parsing."""
    print("=== Template Parameter Debug ===\n")
    
    try:
        template_service = TemplateManagementService()
        intelligent_service = IntelligentTemplateService()
        
        # Test rule attributes
        rule_attrs = RuleAttributes(
            rule_name="智能向导测试规则",
            rule_type="超频次",
            patient_type="inpatient",
            match_method="name",
            database_type="postgresql"
        )
        
        # Get recommendations
        recommendations = intelligent_service.recommend_templates(rule_attrs, max_recommendations=3)
        
        if recommendations:
            first_rec = recommendations[0]
            template_id = first_rec.template.id
            
            print(f"Testing template: {template_id}")
            print(f"Description: {first_rec.template.description}")
            print(f"Score: {first_rec.score:.2f}")
            print()
            
            # Get template info
            template_info = template_service.get_template_by_id(template_id)
            
            print("Template SQL (first 200 chars):")
            print(template_info.sql[:200] + "..." if len(template_info.sql) > 200 else template_info.sql)
            print()
            
            print("Parsed Parameters:")
            if template_info.parameters:
                for i, param in enumerate(template_info.parameters):
                    print(f"  {i+1}. Name: '{param.name}'")
                    print(f"     Type: {param.parameter_type}")
                    print(f"     Required: {param.required}")
                    print(f"     Description: {param.description}")
                    print()
            else:
                print("  No parameters found!")
            
            # Test form config generation
            print("Form Configuration:")
            form_config = template_service.generate_dynamic_form_config(template_id)
            print(f"  Template ID: {form_config['template_id']}")
            print(f"  Parameters count: {len(form_config['parameters'])}")
            
            for param in form_config['parameters']:
                print(f"    - {param['name']}: {param['type']} ({'required' if param['required'] else 'optional'})")
            
        else:
            print("No template recommendations found!")
            
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_template_parameters()