# 产品需求文档 (PRD) - 临时规则编写工具

## 1. 文档信息

| 版本 | 日期 | 作者 | 变更内容 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2023-10-27 | 产品经理 | 初始草案，定义产品核心功能与需求。 |

### 1.1 文档目的

本文档旨在明确“临时规则编写工具”的产品需求，作为产品团队（包括设计、开发、测试）的工作依据，确保各方对产品有统一的理解，并高效协同完成产品目标。

### 1.2 相关文档引用

- [产品路线图 (Roadmap)](./Roadmap.md)
- [用户故事地图 (User Story Map)](./User_Story_Map.md)
- [产品评估指标框架 (Metrics Framework)](./Metrics_Framework.md)

## 2. 产品概述

### 2.1 产品名称与定位

- **产品名称**: 临时规则编写工具
- **产品定位**: 一款面向医保风控/稽核业务人员的、低代码/无代码的SQL规则生成与管理工具，旨在提高临时性、小批量规则的创建和测试效率。

### 2.2 产品愿景与使命

- **愿景**: 成为业务人员首选的、最易用的医保风控规则生产力工具。
- **使命**: 通过智能化的模板和引导，让不熟悉SQL的业务人员也能快速、准确地将业务需求转化为可执行的规则，缩短规则上线周期，提升风控覆盖率和准确性。

### 2.3 价值主张与独特卖点 (USP)

- **核心价值**: **降本增效**。将原先需要技术人员介入、耗时数小时甚至数天的规则开发工作，缩短为业务人员几分钟内即可完成的自助服务。
- **独特卖点**: 
  - **模板化生成**: 内置丰富的、贴合医保业务场景的规则模板，用户只需填写关键参数即可生成高质量SQL。
  - **一体化管理**: 集规则创建、编辑、查看、删除于一体，提供规则的全生命周期管理。
  - **实时预览与测试**: （远期）支持连接测试数据库，实时验证规则有效性。

### 2.4 目标平台列表

- **Web**: 核心平台，通过浏览器访问。

### 2.5 产品核心假设

- 业务人员有大量临时性的规则创建需求，现有流程（提需求给IT）效率低下，是业务痛点。
- 通过预设模板的方式可以覆盖80%以上的常见临时规则场景。
- 业务人员愿意并能够通过简单的表单和选项来配置规则，而无需编写代码。

### 2.6 商业模式概述

- 内部效率工具，不涉及直接的商业变现。其价值通过提升业务效率、降低人力成本来体现。

## 3. 用户研究

### 3.1 目标用户画像

- **画像名称**: 李慧 (Li Hui)
- **人口统计特征**: 32岁，医保局风控稽核科室业务骨干，拥有5年医保业务经验。
- **行为习惯与偏好**: 
  - 熟练使用Excel、Word等办公软件。
  - 对业务逻辑非常熟悉，但对数据库、SQL等技术概念了解有限。
  - 工作中需要频繁根据新的政策或发现的违规行为，制定新的筛查规则。
  - 倾向于使用有清晰指引、操作直观的软件工具。
- **核心需求与痛点**:
  - **需求**: 快速验证一个新想到的稽核点子是否有效，不想走漫长的IT需求流程。
  - **痛点1**: “我发现一种新的骗保手段，想要立刻在数据里筛一下看看有多少类似的，但等IT排期开发规则，黄花菜都凉了。”
  - **痛点2**: “每次给IT提需求，都要反复沟通业务细节，他们还是可能理解错，来回沟通成本太高。”
- **动机与目标**: 
  - **动机**: 提高工作效率，更主动、更精准地发现医保基金使用中的不合规行为。
  - **目标**: 实现常见规则的“自助生产”，独立完成规则的创建和初步验证。

### 3.2 用户场景分析

- **核心使用场景**: 
  - **场景**: 李慧在审核一个病例时，发现某医院将一个常规日间手术的所有费用都按住院进行结算，她怀疑这可能是一个普遍的违规操作。她需要立刻创建一个规则，筛查出所有“在住院费用中包含了特定日间手术项目”的记录。
  - **步骤**: 
    1. 李慧打开“临时规则编写工具”。
    2. 在“规则SQL生成”模块，她选择“重复收费”或“项目组合”类的模板。
    3. 在模板的表单中，她输入或选择了关键信息，如“项目代码A”、“项目代码B”、“费用类型=住院”等。
    4. 点击“生成SQL”按钮，系统自动生成了对应的SQL查询语句。
    5. 她为这个规则命名为“XX日间手术套用住院费用”，并填写了规则内涵和政策依据。
    6. 点击“保存”，这条新规则被添加到了“规则管理”列表中，状态为“待测试”。

## 4. 市场与竞品分析

- **市场分析**: 内部工具，主要服务于本机构的业务需求，无外部市场竞争。
- **竞品分析**: 
  - **直接竞争对手**: **“Excel + IT部门”** 的现有工作模式。
    - **优势**: 灵活，理论上可以实现任何复杂的逻辑。
    - **劣势**: 效率极低、沟通成本高、依赖特定技术人员、无法规模化。
  - **间接竞争对手**: 商业BI工具（如Tableau, PowerBI）中的数据查询功能。
    - **优势**: 可视化程度高，探索性强。
    - **劣势**: 对用户的数据分析能力要求高，通常不直接生成面向业务系统的、标准化的“规则”。

## 5. 产品功能需求

### 5.1 功能架构与模块划分

```mermaid
graph TD
    A[临时规则编写工具] --> B[规则管理模块]
    A --> C[规则SQL生成模块]

    B --> B1[规则列表展示]
    B --> B2[规则详情查看]
    B --> B3[规则编辑]
    B --> B4[规则删除]

    C --> C1[规则基本信息填写]
    C --> C2[规则类型与模板选择]
    C --> C3[规则参数配置]
    C --> C4[SQL预览与生成]
    C --> C5[保存至规则列表]
```

### 5.2 核心功能详述

#### 5.2.1 [功能模块1] 规则管理

- **用户故事**: 
  - 作为业务人员，我想要在一个列表中看到所有我创建的临时规则，以便我能快速概览和管理它们。
  - 作为业务人员，我想要修改一个已保存规则的参数或描述，以便根据测试结果进行调整。
  - 作为业务人员，我想要删除一个无效或过时的规则，以保持规则库的整洁。
- **功能逻辑与规则**:
  - **规则列表**: 
    - 默认显示用户自己创建的规则。
    - 列表应包含关键信息：规则ID、规则名称、规则类型、创建时间、状态（待测试、已上线、已失效）。
    - 支持按规则名称进行搜索。
    - 支持分页。
  - **规则编辑**: 
    - 点击“编辑”按钮，进入类似“规则SQL生成”的页面，但加载的是已有规则的数据。
    - 用户可以修改所有字段，并重新生成SQL。
  - **规则删除**: 
    - 点击“删除”按钮，进行二次确认，防止误操作。
    - 删除为逻辑删除，数据库中标记为“已删除”状态。
- **验收标准**:
  - 能够成功展示规则列表，且信息准确无误。
  - 能够成功保存对规则的修改。
  - 能够成功删除规则，且列表中不再显示。

#### 5.2.2 [功能模块2] 规则SQL生成

- **用户故事**: 作为业务人员，我想要通过选择模板和填写几个关键信息，就能自动生成一条符合业务要求的SQL规则，以便我能快速创建规则而无需学习SQL。
- **功能逻辑与规则**:
  - **基本信息**: 用户需填写规则名称、规则内涵、政策依据等描述性信息。
  - **规则类型与模板**: 
    - 用户首先选择一个大的规则类型（如：超频次、重复收费、限性别等）。
    - 根据所选类型，系统动态加载对应的模板列表（如：超每日数量、超每周数量）。
  - **参数配置**: 
    - 选择模板后，展示一个清晰的表单，让用户填写该模板所需的全部参数。
    - 例如，“超每日数量”模板需要填写：【医保项目编码/名称】、【每日限制数量】。
    - 参数输入框应有清晰的标签和提示说明。
  - **SQL生成与预览**: 
    - 用户填写完参数后，点击“生成SQL”按钮。
    - 后端根据模板和参数，生成完整的SQL语句，并在页面上展示给用户预览。
  - **保存**: 
    - 用户确认SQL无误后，点击“保存”按钮。
    - 系统将规则的所有信息（包括描述、类型、参数、生成的SQL）存入数据库，并跳转到规则管理列表。
- **验收标准**:
  - 能够根据选择的模板和输入的参数，正确生成预期的SQL语句。
  - 生成的规则能够成功保存到数据库。
  - 所有必填项未填写时，应有明确提示，无法保存。

## 6. 用户流程与交互设计指导

### 6.1 核心用户旅程地图

```mermaid
journey
    title 创建一条新规则
    section 发现需求
      李慧: 发现新的违规嫌疑点: 2
    section 创建规则
      打开工具: 3
      选择模板: 5
      填写参数: 5
      生成并预览SQL: 4
    section 保存与后续
      保存规则: 5
      在列表中查看新规则: 3
```

### 6.3 对设计师 (UI/UX Agent) 的界面原型参考说明和要求

- **规则SQL生成页面**: 
  - 布局建议采用“三段式”：左侧为规则类型和模板选择区，中间为基本信息和参数配置区，右侧为SQL预览区。
  - **操作焦点**: “生成SQL”和“保存”按钮需要非常醒目。
  - 参数配置表单应清晰、简洁，分组明确。
- **规则管理页面**: 
  - 以标准的数据表格（Table）形式展示。
  - 每行末尾提供“编辑”和“删除”的操作按钮。

## 7. 非功能需求

- **性能需求**: 页面加载时间 < 2秒，SQL生成响应时间 < 1秒。
- **安全需求**: 用户身份认证，确保用户只能管理自己创建的规则。
- **可用性需求**: 无需培训，具备基础电脑操作和业务知识的用户即可上手使用。

## 8. 技术架构考量

- **技术栈建议**: 
  - 前端: Vue.js / React
  - 后端: Python (Flask / Django) + SQLAlchemy
  - 数据库: PostgreSQL / Oracle (根据现有环境)
- **数据模型建议**: 
  - **Rule表**: rule_id, rule_name, description, policy_basis, rule_type, template_name, params (JSON类型), generated_sql, status, created_by, created_at, updated_at.

## 9. 验收标准汇总

- **功能验收**: 所有在 5.2 节中定义的功能点均可按预期工作。
- **性能验收**: 符合 7.1 节中定义的性能指标。

## 10. 产品成功指标

- **北极星指标**: **成功生成的有效规则数**。
- **核心KPIs**: 
  - 新用户规则创建转化率
  - 平均规则生成时长
  - 次周留存率
  - (详见 [Metrics Framework](./Metrics_Framework.md))