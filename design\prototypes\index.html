<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时规则编写工具 - 原型预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            background-color: #1e1e1e;
        }
    </style>
</head>
<body class="bg-gray-800 text-gray-200 font-sans p-8">

    <h1 class="text-4xl font-bold text-center mb-4">临时规则编写工具 - 高保真原型</h1>
    <p class="text-center text-gray-400 mb-12">所有核心界面平铺展示</p>

    <div class="space-y-16">

        <!-- 规则管理界面 -->
        <div>
            <h2 class="text-2xl font-semibold mb-4 ml-2"><i class="fas fa-tasks mr-2"></i>1. 规则管理界面 (rule_management.html)</h2>
            <div class="w-full h-[720px] bg-gray-900 rounded-lg shadow-2xl overflow-hidden border border-gray-700">
                <div class="h-10 bg-gray-700 flex items-center px-4">
                    <div class="flex space-x-2">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div class="flex-grow text-center text-sm text-gray-400">临时规则编写工具 - 规则管理</div>
                </div>
                <iframe src="rule_management.html" class="w-full h-full" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 规则SQL生成器界面 -->
        <div>
            <h2 class="text-2xl font-semibold mb-4 ml-2"><i class="fas fa-magic-wand-sparkles mr-2"></i>2. 规则SQL生成器 (sql_generator.html)</h2>
            <div class="w-full h-[720px] bg-gray-900 rounded-lg shadow-2xl overflow-hidden border border-gray-700">
                <div class="h-10 bg-gray-700 flex items-center px-4">
                    <div class="flex space-x-2">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div class="flex-grow text-center text-sm text-gray-400">临时规则编写工具 - SQL生成器</div>
                </div>
                <iframe src="sql_generator.html" class="w-full h-full" frameborder="0"></iframe>
            </div>
        </div>

    </div>

    <footer class="text-center text-gray-500 mt-16">
        <p>技术栈: HTML, Tailwind CSS, FontAwesome</p>
    </footer>

</body>
</html>