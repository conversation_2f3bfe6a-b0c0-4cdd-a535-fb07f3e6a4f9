<!DOCTYPE html>
<html lang="zh-CN" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库连接管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
    
    <style>
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        }

        .toast {
            background: #1f2937;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 8px;
            color: white;
            font-size: 14px;
            pointer-events: auto;
            transition: all 0.3s ease;
            max-width: 400px;
        }

        .toast.success {
            border-color: #10b981;
            background: #064e3b;
        }

        .toast.error {
            border-color: #ef4444;
            background: #7f1d1d;
        }

        .toast.warning {
            border-color: #f59e0b;
            background: #78350f;
        }

        .toast.info {
            border-color: #3b82f6;
            background: #1e3a8a;
        }
    </style>
</head>

<body class="bg-gray-900 text-white min-h-screen">
    <!-- Toast Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Header -->
    <header class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <h1 class="text-2xl font-bold text-white">数据库连接管理</h1>
                <button onclick="window.history.back()" class="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>返回
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Add Connection Button -->
        <div class="mb-6">
            <button onclick="openAddConnectionModal()" class="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg transition-colors">
                <i class="fas fa-plus mr-2"></i>添加数据库连接
            </button>
        </div>

        <!-- Connections List -->
        <div class="bg-gray-800 rounded-lg shadow-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h2 class="text-lg font-semibold">已保存的连接</h2>
            </div>
            <div id="connections-list" class="divide-y divide-gray-700">
                <!-- Connections will be loaded here -->
            </div>
        </div>
    </main>

    <!-- Add/Edit Connection Modal -->
    <div id="connection-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
                <div class="flex justify-between items-center p-6 border-b border-gray-700">
                    <h3 id="modal-title" class="text-lg font-semibold">添加数据库连接</h3>
                    <button onclick="closeConnectionModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="connection-form" class="p-6 space-y-4">
                    <input type="hidden" id="connection-id">
                    
                    <div>
                        <label for="connection-name" class="block text-sm font-medium text-gray-300 mb-2">连接名称</label>
                        <input type="text" id="connection-name" required class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="connection-description" class="block text-sm font-medium text-gray-300 mb-2">描述（可选）</label>
                        <textarea id="connection-description" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500" rows="2"></textarea>
                    </div>
                    
                    <div>
                        <label for="connection-type" class="block text-sm font-medium text-gray-300 mb-2">数据库类型</label>
                        <select id="connection-type" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                            <option value="postgresql">PostgreSQL</option>
                            <option value="oracle">Oracle</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="connection-host" class="block text-sm font-medium text-gray-300 mb-2">主机地址</label>
                        <input type="text" id="connection-host" required class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="connection-port" class="block text-sm font-medium text-gray-300 mb-2">端口</label>
                        <input type="number" id="connection-port" required class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="connection-database" class="block text-sm font-medium text-gray-300 mb-2">数据库名称</label>
                        <input type="text" id="connection-database" required class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="connection-username" class="block text-sm font-medium text-gray-300 mb-2">用户名</label>
                        <input type="text" id="connection-username" required class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                    </div>
                    
                    <div>
                        <label for="connection-password" class="block text-sm font-medium text-gray-300 mb-2">密码</label>
                        <input type="password" id="connection-password" required class="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500">
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeConnectionModal()" class="bg-gray-600 hover:bg-gray-500 px-4 py-2 rounded-lg transition-colors">
                            取消
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4">确认删除</h3>
                    <p class="text-gray-300 mb-6">确定要删除这个数据库连接吗？此操作无法撤销。</p>
                    <div class="flex justify-end space-x-3">
                        <button onclick="closeDeleteModal()" class="bg-gray-600 hover:bg-gray-500 px-4 py-2 rounded-lg transition-colors">
                            取消
                        </button>
                        <button onclick="confirmDeleteConnection()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors">
                            删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let connections = [];
        let currentConnectionId = null;

        // 页面加载时获取连接列表
        document.addEventListener('DOMContentLoaded', function() {
            loadConnections();
        });

        // 加载连接列表
        async function loadConnections() {
            try {
                const response = await fetch('/api/database/connections');
                const data = await response.json();
                
                if (data.success) {
                    connections = data.connections;
                    renderConnections();
                } else {
                    showToast('加载连接列表失败: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('加载连接列表失败: ' + error.message, 'error');
            }
        }

        // 渲染连接列表
        function renderConnections() {
            const container = document.getElementById('connections-list');
            
            if (connections.length === 0) {
                container.innerHTML = `
                    <div class="p-6 text-center text-gray-400">
                        <i class="fas fa-database text-4xl mb-4"></i>
                        <p>暂无保存的数据库连接</p>
                        <p class="text-sm mt-2">点击"添加数据库连接"开始使用</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = connections.map(connection => `
                <div class="p-6 hover:bg-gray-700 transition-colors">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <h3 class="text-lg font-semibold">${connection.name}</h3>
                                <span class="ml-2 px-2 py-1 text-xs rounded-full ${connection.database_type === 'postgresql' ? 'bg-blue-600' : 'bg-orange-600'}">
                                    ${connection.database_type.toUpperCase()}
                                </span>
                            </div>
                            ${connection.description ? `<p class="text-gray-400 text-sm mb-2">${connection.description}</p>` : ''}
                            <div class="text-sm text-gray-400">
                                <p><i class="fas fa-server mr-2"></i>${connection.host}:${connection.port}</p>
                                <p><i class="fas fa-database mr-2"></i>${connection.database}</p>
                                <p><i class="fas fa-user mr-2"></i>${connection.username}</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="editConnection('${connection.id}')" class="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm transition-colors">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteConnection('${connection.id}')" class="bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm transition-colors">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 打开添加连接模态框
        function openAddConnectionModal() {
            document.getElementById('modal-title').textContent = '添加数据库连接';
            document.getElementById('connection-form').reset();
            document.getElementById('connection-id').value = '';
            document.getElementById('connection-port').value = '5432';
            document.getElementById('connection-type').value = 'postgresql';
            document.getElementById('connection-modal').classList.remove('hidden');
        }

        // 编辑连接
        function editConnection(connectionId) {
            const connection = connections.find(c => c.id === connectionId);
            if (!connection) return;

            document.getElementById('modal-title').textContent = '编辑数据库连接';
            document.getElementById('connection-id').value = connection.id;
            document.getElementById('connection-name').value = connection.name;
            document.getElementById('connection-description').value = connection.description || '';
            document.getElementById('connection-type').value = connection.database_type;
            document.getElementById('connection-host').value = connection.host;
            document.getElementById('connection-port').value = connection.port;
            document.getElementById('connection-database').value = connection.database;
            document.getElementById('connection-username').value = connection.username;
            document.getElementById('connection-password').value = connection.password;
            
            document.getElementById('connection-modal').classList.remove('hidden');
        }

        // 关闭连接模态框
        function closeConnectionModal() {
            document.getElementById('connection-modal').classList.add('hidden');
        }

        // 删除连接
        function deleteConnection(connectionId) {
            currentConnectionId = connectionId;
            document.getElementById('delete-modal').classList.remove('hidden');
        }

        // 关闭删除模态框
        function closeDeleteModal() {
            document.getElementById('delete-modal').classList.add('hidden');
            currentConnectionId = null;
        }

        // 确认删除连接
        async function confirmDeleteConnection() {
            if (!currentConnectionId) return;

            try {
                const response = await fetch(`/api/database/connections/${currentConnectionId}`, {
                    method: 'DELETE'
                });
                const data = await response.json();

                if (data.success) {
                    showToast(data.message, 'success');
                    loadConnections();
                } else {
                    showToast('删除失败: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('删除失败: ' + error.message, 'error');
            }

            closeDeleteModal();
        }

        // 表单提交处理
        document.getElementById('connection-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const connectionId = document.getElementById('connection-id').value;
            const formData = {
                name: document.getElementById('connection-name').value,
                description: document.getElementById('connection-description').value,
                database_type: document.getElementById('connection-type').value,
                host: document.getElementById('connection-host').value,
                port: document.getElementById('connection-port').value,
                database: document.getElementById('connection-database').value,
                username: document.getElementById('connection-username').value,
                password: document.getElementById('connection-password').value
            };

            try {
                let response;
                if (connectionId) {
                    // 更新现有连接
                    response = await fetch(`/api/database/connections/${connectionId}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(formData)
                    });
                } else {
                    // 添加新连接
                    response = await fetch('/api/database/connections', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(formData)
                    });
                }

                const data = await response.json();

                if (data.success) {
                    showToast(data.message, 'success');
                    closeConnectionModal();
                    loadConnections();
                } else {
                    showToast('保存失败: ' + data.error, 'error');
                }
            } catch (error) {
                showToast('保存失败: ' + error.message, 'error');
            }
        });

        // Toast 通知
        function showToast(message, type = 'info') {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    container.removeChild(toast);
                }, 300);
            }, 3000);
        }
    </script>
</body>

</html> 