<!DOCTYPE html>
<html lang="en" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时规则编写工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">

    <!-- Stagewise Toolbar - 仅在开发模式下加载 -->
    <script>
        // 检查是否为开发模式
        const isDevelopment = window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1' ||
            window.location.port === '5000' ||
            window.location.search.includes('dev=true');

        if (isDevelopment) {
            // 创建工具栏容器
            const toolbarContainer = document.createElement('div');
            toolbarContainer.id = 'stagewise-toolbar';
            toolbarContainer.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 9999;
                background: #1f2937;
                border: 1px solid #374151;
                border-radius: 8px;
                padding: 12px;
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                cursor: pointer;
                transition: all 0.2s;
            `;
            toolbarContainer.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="color: #10b981;">⚡</span>
                    <span>Stagewise</span>
                    <span style="color: #6b7280; font-size: 12px;">DEV</span>
                </div>
            `;

            // 添加点击事件
            toolbarContainer.addEventListener('click', function () {
                alert('Stagewise 工具栏已激活！\n\n请确保您已安装 VSCode 的 Stagewise 扩展。\n\n如果工具栏没有正常工作，请访问: https://discord.gg/gkdGsDYaKA');
            });

            // 添加到页面
            document.body.appendChild(toolbarContainer);

            console.log('Stagewise toolbar loaded in development mode');
        }
    </script>

    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'gray-900': '#121212',
                        'gray-800': '#1e1e1e',
                        'gray-700': '#2d2d2d',
                        'gray-600': '#444444',
                    }
                }
            }
        }
    </script>
    <style>
        /* For modal transitions */
        .modal-backdrop {
            transition: opacity 0.3s ease;
        }

        .modal-content {
            transition: transform 0.3s ease;
        }

        /* Toast notification styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        }

        .toast {
            background: #1f2937;
            border: 1px solid #374151;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 8px;
            color: white;
            font-size: 14px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            pointer-events: auto;
            max-width: 400px;
            word-wrap: break-word;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-left: 4px solid #10b981;
        }

        .toast.error {
            border-left: 4px solid #ef4444;
        }

        .toast.warning {
            border-left: 4px solid #f59e0b;
        }

        .toast.info {
            border-left: 4px solid #3b82f6;
        }

        .toast-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .toast-title {
            font-weight: 600;
            font-size: 13px;
        }

        .toast-close {
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            padding: 0;
            font-size: 16px;
            line-height: 1;
        }

        .toast-close:hover {
            color: white;
        }

        .toast-message {
            color: #d1d5db;
            font-size: 13px;
        }
    </style>
</head>

<body class="bg-gray-900 text-gray-300 font-sans p-8">

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Main Container -->
    <div class="container mx-auto space-y-8">

        <!-- Part 1: SQL Generator -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white"><i class="fas fa-magic-wand-sparkles mr-2"></i>规则SQL生成器</h1>
                <div class="flex space-x-2">
                    <button id="db-monitor-config-btn" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                        <i class="fas fa-database mr-2"></i> 数据库监控
                    </button>
                </div>
            </div>

            <div class="flex space-x-8">
                <!-- Left Panel: Form -->
                <div class="w-1/2 space-y-6">
                    <div>
                        <label for="rule-name" class="block mb-2 text-sm font-medium text-gray-300">规则名称</label>
                        <input type="text" id="rule-name"
                            class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                            placeholder="例如：限定支付-限单人多次">
                    </div>

                    <!-- Rule Attributes Section -->
                    <div class="border border-gray-600 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-semibold text-white mb-4">规则属性</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="rule-type" class="block mb-2 text-sm font-medium text-gray-300">规则类型</label>
                                <select id="rule-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="">自动检测</option>
                                    <option value="病例提取">病例提取</option>
                                    <option value="超频次">超频次</option>
                                    <option value="重复收费">重复收费</option>
                                    <option value="限性别">限性别</option>
                                    <option value="限年龄">限年龄</option>
                                    <option value="超金额">超金额</option>
                                    <option value="限定支付">限定支付</option>
                                    <option value="超住院天数">超住院天数</option>
                                    <option value="组套收费">组套收费</option>
                                    <option value="禁忌药物">禁忌药物</option>
                                    <option value="超合理用药疗程">超合理用药疗程</option>

                                </select>
                                <div id="rule-type-suggestion" class="text-xs text-green-400 mt-1"
                                    style="display: none;"></div>
                            </div>
                            <div>
                                <label for="patient-type"
                                    class="block mb-2 text-sm font-medium text-gray-300">适用范围</label>
                                <select id="patient-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="inpatient">住院</option>
                                    <option value="outpatient">门诊</option>
                                    <option value="general">通用</option>
                                </select>
                                <div id="patient-type-suggestion" class="text-xs text-green-400 mt-1"
                                    style="display: none;"></div>
                            </div>
                            <div>
                                <label for="match-method"
                                    class="block mb-2 text-sm font-medium text-gray-300">匹配方式</label>
                                <select id="match-method"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="name">按名称</option>
                                    <option value="code">按编码</option>
                                </select>
                                <div id="match-method-suggestion" class="text-xs text-green-400 mt-1"
                                    style="display: none;"></div>
                            </div>
                            <div>
                                <label for="database-type"
                                    class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                                <select id="database-type"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                    <option value="postgresql">PostgreSQL</option>
                                    <option value="oracle">Oracle</option>
                                </select>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 gap-4 mt-4">
                            <div>
                                <label for="rule-description"
                                    class="block mb-2 text-sm font-medium text-gray-300">规则描述</label>
                                <textarea id="rule-description"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                    rows="2" placeholder="描述规则的业务逻辑和用途"></textarea>
                            </div>
                            <div>
                                <label for="rule-policy-basis"
                                    class="block mb-2 text-sm font-medium text-gray-300">政策依据</label>
                                <input type="text" id="rule-policy-basis"
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                    placeholder="相关政策文件或条款"
                                    value="">
                            </div>
                        </div>
                        <div class="mt-4 flex space-x-2">
                            <button id="get-recommendations-btn"
                            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                            <i class="fas fa-lightbulb mr-2"></i> 获取推荐
                             </button>
                            <button id="start-wizard-btn"
                                class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-magic mr-2"></i> 智能向导
                            </button>
                            <button id="import-rules-btn"
                                class="bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-download mr-2"></i> 导入规则
                            </button>
                            <button id="manage-connections-btn"
                                class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-database mr-2"></i> 连接管理
                            </button>
                        </div>
                    </div>

                    <!-- Template Selection Section -->
                    <div class="border border-gray-600 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-semibold text-white mb-4">模板选择</h3>
                        <div id="template-recommendations" class="space-y-2 mb-4">
                            <p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>
                        </div>
                        <div>
                            <label for="rule-template" class="block mb-2 text-sm font-medium text-gray-300">选择模板</label>
                            <select id="rule-template"
                                class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="">请先获取推荐或手动选择模板</option>
                            </select>
                            <div class="mt-2 flex space-x-2">
                                <button id="use-recommended-template-btn"
                                    class="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded flex items-center"
                                    style="display: none;">
                                    <i class="fas fa-check mr-1"></i> 使用推荐模板
                                </button>
                                <button id="show-all-templates-btn"
                                    class="bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded flex items-center">
                                    <i class="fas fa-list mr-1"></i> 显示所有模板
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="border-t border-gray-700 pt-6">
                        <h3 class="text-lg font-semibold text-white mb-4">规则条件 (由模板生成)</h3>
                        <div class="space-y-4" id="conditions-container">
                            <p class="text-gray-500">请先选择一个SQL模板</p>
                        </div>
                        <div class="mt-4 flex justify-end">
                            <button id="generate-sql-btn"
                                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                                <i class="fas fa-magic mr-2"></i> 生成SQL
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Right Panel: SQL Editor -->
                <div class="w-1/2 flex flex-col">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-white">SQL编辑器</h2>
                        <div class="flex space-x-2">
                            <button id="toggle-edit-mode"
                                class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-edit mr-2"></i> <span id="edit-mode-text">编辑模式</span>
                            </button>
                            <button id="copy-sql"
                                class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-copy mr-2"></i> 复制
                            </button>
                        </div>
                    </div>
                    <!-- 数据库连接配置区域 -->
                    <div class="bg-gray-800 border border-gray-600 rounded-lg p-4 mb-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-white">数据库连接配置</h3>
                            <div class="flex items-center space-x-2">
                                <div id="connection-status" class="flex items-center text-sm">
                                    <div class="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                                    <span class="text-gray-400">未连接</span>
                                </div>
                                <button id="test-connection-btn" class="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded">
                                    <i class="fas fa-plug mr-1"></i> 测试连接
                                </button>
                                <button id="load-config-btn" class="bg-yellow-600 hover:bg-yellow-700 text-white text-xs px-3 py-1 rounded">
                                    <i class="fas fa-download mr-1"></i> 加载配置
                                </button>
                                <button id="save-config-btn" class="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded">
                                    <i class="fas fa-save mr-1"></i> 保存配置
                                </button>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="databaseSelect" class="block mb-2 text-sm font-medium text-gray-300">选择数据库:</label>
                                <select class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                        id="databaseSelect" onchange="onDatabaseChange()">
                                    <option value="pg" selected>PostgreSQL</option>
                                    <option value="oracle">Oracle</option>
                                </select>
                            </div>
                            <div>
                                <label for="hostInput" class="block mb-2 text-sm font-medium text-gray-300">数据库主机IP:</label>
                                <input type="text" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                       id="hostInput" placeholder="输入IP地址" onchange="loadDatabaseSchemas()">
                                <small class="text-gray-400 text-xs">留空使用默认主机</small>
                            </div>
                            <div>
                                <label for="schemaSelect" class="block mb-2 text-sm font-medium text-gray-300">选择Schema:</label>
                                <select class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                        id="schemaSelect">
                                    <option value="">请先输入主机IP</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 rounded-lg p-1 relative" style="height: 600px;">
                        <!-- 预览模式 -->
                        <pre id="sql-preview" class="language-sql h-full block p-4 overflow-auto"
                            style="margin: 0;"><code id="sql-output" class="language-sql">-- 在左侧表单中填写信息以生成SQL...</code></pre>

                        <!-- 编辑模式 -->
                        <textarea id="sql-editor"
                            class="w-full h-full bg-gray-900 text-gray-300 font-mono text-sm p-4 border-none outline-none resize-none"
                            style="display: none;"
                            placeholder="-- 在此处编写或编辑SQL代码...">-- 在左侧表单中填写信息以生成SQL...</textarea>
                    </div>

                    <!-- SQL执行控制区域 -->
                    <div class="bg-gray-800 border border-gray-600 rounded-lg p-4 mt-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-white">SQL执行控制</h3>
                            <div class="flex space-x-2">
                                <button id="copy-sql-btn" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                    <i class="fas fa-copy mr-2"></i> 复制SQL
                                </button>
                                                            <button id="validate-sql-btn" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-check mr-2"></i> 验证SQL
                            </button>
                            <button id="execute-sql-btn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center text-sm">
                                <i class="fas fa-play mr-2"></i> 执行查询
                            </button>
                            </div>
                        </div>
                        
                        <!-- 执行结果显示区域 -->
                        <div id="sqlResult" class="mt-3" style="display: none;">
                            <h4 class="text-md font-semibold text-white mb-3">执行结果:</h4>
                            <div id="resultContent" class="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto"></div>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-between">
                        <button id="new-rule-btn"
                            class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i> 新建规则
                        </button>
                        <button id="save-rule-btn"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg flex items-center">
                            <i class="fas fa-check-circle mr-2"></i> 保存规则
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Part 2: Rule Management -->
        <div class="bg-gray-800 p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold text-white"><i class="fas fa-tasks mr-2"></i>规则管理</h1>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-400">
                    <thead class="text-xs text-gray-300 uppercase bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3">规则名称</th>
                            <th scope="col" class="px-6 py-3">规则描述</th>
                            <th scope="col" class="px-6 py-3">政策依据</th>
                            <th scope="col" class="px-6 py-3">来源</th>
                            <th scope="col" class="px-6 py-3">状态</th>
                            <th scope="col" class="px-6 py-3">创建日期</th>
                            <th scope="col" class="px-6 py-3 text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody id="rules-table-body">
                        <!-- Rules will be loaded here dynamically -->
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- Database Monitor Config Modal -->
    <div id="db-monitor-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-4xl transform scale-95">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-white">数据库监控配置</h2>
                <button onclick="closeModal('db-monitor-modal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Monitor Status -->
            <div class="mb-6 p-4 bg-gray-700 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div id="monitor-status-indicator" class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                        <span id="monitor-status-text" class="text-white">监控已停止</span>
                    </div>
                    <div class="flex space-x-2">
                        <button id="start-monitor-btn" onclick="startDatabaseMonitor()" 
                                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg">
                            <i class="fas fa-play mr-2"></i>启动监控
                        </button>
                        <button id="stop-monitor-btn" onclick="stopDatabaseMonitor()" 
                                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg" disabled>
                            <i class="fas fa-stop mr-2"></i>停止监控
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Database Connections -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-white">数据库连接配置</h3>
                    <button onclick="addDatabaseConnection()" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                        <i class="fas fa-plus mr-2"></i>添加连接
                    </button>
                </div>
                
                <div id="db-connections-list" class="space-y-4">
                    <!-- 数据库连接列表将在这里动态加载 -->
                </div>
            </div>
            
            <!-- Notification Settings -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">通知设置</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="sound-notification" class="mr-2" checked>
                            <span class="text-gray-300">声音提醒</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="popup-notification" class="mr-2" checked>
                            <span class="text-gray-300">弹窗提醒</span>
                        </label>
                    </div>
                </div>
                <div class="mt-4">
                    <label for="monitor-interval" class="block mb-2 text-sm font-medium text-gray-300">监控间隔 (秒)</label>
                    <input type="number" id="monitor-interval" value="30" min="10" max="300"
                           class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-32 p-2.5">
                </div>
            </div>
            
            <!-- Recent Alerts -->
            <div>
                <h3 class="text-lg font-semibold text-white mb-4">最近提醒</h3>
                <div id="recent-alerts" class="max-h-40 overflow-y-auto space-y-2">
                    <p class="text-gray-500 text-sm">暂无提醒记录</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Rules Modal -->
    <div id="import-rules-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-6xl transform scale-95">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-white">导入规则</h2>
                <button onclick="closeModal('import-rules-modal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Step Indicator -->
            <div class="mb-6">
                <div class="flex items-center justify-center space-x-4">
                    <div class="flex items-center">
                        <div id="step-1-indicator" class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">1</div>
                        <span class="ml-2 text-white">连接数据库</span>
                    </div>
                    <div class="w-8 h-1 bg-gray-600"></div>
                    <div class="flex items-center">
                        <div id="step-2-indicator" class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white font-bold">2</div>
                        <span class="ml-2 text-gray-400">选择规则</span>
                    </div>
                </div>
            </div>
            
            <!-- Step 1: Database Connection -->
            <div id="step-1" class="mb-6">
                <h3 class="text-lg font-semibold text-white mb-4">第一步：数据库连接</h3>
                
                <!-- Connection Method Selection -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-white mb-3">连接方式</h4>
                    <div class="flex space-x-4 mb-4">
                        <label class="flex items-center">
                            <input type="radio" name="connection-method" value="saved" class="mr-2" checked>
                            <span class="text-gray-300">使用保存的连接</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="connection-method" value="manual" class="mr-2">
                            <span class="text-gray-300">手动输入连接信息</span>
                        </label>
                    </div>
                </div>
                
                <!-- Saved Connections -->
                <div id="saved-connections-section" class="mb-6">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-md font-medium text-white">选择保存的连接</h4>
                        <button onclick="openDatabaseConnectionsPage()" class="bg-indigo-600 hover:bg-indigo-700 text-white text-xs px-3 py-1 rounded">
                            <i class="fas fa-cog mr-1"></i> 管理连接
                        </button>
                    </div>
                    <select id="saved-connection-select" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                        <option value="">加载中...</option>
                    </select>
                </div>
                
                <!-- Manual Connection Form -->
                <div id="manual-connection-section" class="mb-6 hidden">
                    <h4 class="text-md font-medium text-white mb-3">手动输入连接信息</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="import-db-host" class="block mb-2 text-sm font-medium text-gray-300">数据库主机</label>
                            <input type="text" id="import-db-host" value="localhost"
                                   class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                        </div>
                        <div>
                            <label for="import-db-port" class="block mb-2 text-sm font-medium text-gray-300">端口</label>
                            <input type="number" id="import-db-port" value="5432"
                                   class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                        </div>
                        <div>
                            <label for="import-db-name" class="block mb-2 text-sm font-medium text-gray-300">数据库名称</label>
                            <input type="text" id="import-db-name" value="databasetools"
                                   class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                        </div>
                        <div>
                            <label for="import-db-user" class="block mb-2 text-sm font-medium text-gray-300">用户名</label>
                            <input type="text" id="import-db-user" value="postgres"
                                   class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                        </div>
                        <div>
                            <label for="import-db-password" class="block mb-2 text-sm font-medium text-gray-300">密码</label>
                            <input type="password" id="import-db-password" value="P@ssw0rd"
                                   class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block w-full p-2.5">
                        </div>
                        <div class="flex items-end">
                            <button onclick="testImportConnection()" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                                <i class="fas fa-plug mr-2"></i> 测试连接
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Connection Test Result -->
                <div id="connection-test-result" class="mb-6 hidden">
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div id="connection-test-message" class="text-sm"></div>
                    </div>
                </div>
                
                <!-- Step 1 Action Button -->
                <div class="flex justify-end">
                    <button onclick="scanImportableRules()" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                        <i class="fas fa-search mr-2"></i> 扫描可导入规则
                    </button>
                </div>
            </div>
            
            <!-- Step 2: Rule Selection -->
            <div id="step-2" class="mb-6 hidden">
                <h3 class="text-lg font-semibold text-white mb-4">第二步：选择要导入的规则</h3>
                
                <!-- Connection Info -->
                <div id="connection-info-display" class="mb-4 p-3 bg-gray-700 rounded-lg">
                    <div class="text-sm text-gray-300">
                        <span id="connection-name-display"></span>
                        <span id="connection-details-display"></span>
                    </div>
                </div>
                
                <!-- Rules List -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-md font-medium text-white">可导入的规则</h4>
                        <div class="flex space-x-2">
                            <button onclick="selectAllRules()" class="bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded">
                                全选
                            </button>
                            <button onclick="deselectAllRules()" class="bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded">
                                取消全选
                            </button>
                        </div>
                    </div>
                    <div id="rules-list" class="bg-gray-700 rounded-lg p-4 max-h-80 overflow-y-auto">
                        <div class="text-center text-gray-400 py-8">
                            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                            <p>正在扫描可导入的规则...</p>
                        </div>
                    </div>
                </div>
                
                <!-- Import Options -->
                <div class="mb-6">
                    <h4 class="text-md font-medium text-white mb-3">导入选项</h4>
                    <div class="flex items-center mb-4">
                        <input type="checkbox" id="import-overwrite" class="mr-2">
                        <label for="import-overwrite" class="text-gray-300">覆盖已存在的规则</label>
                    </div>
                </div>
                
                <!-- Step 2 Action Buttons -->
                <div class="flex justify-between">
                    <button onclick="backToStep1()" 
                            class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                        <i class="fas fa-arrow-left mr-2"></i> 返回上一步
                    </button>
                    <button onclick="executeRuleImport()" 
                            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg">
                        <i class="fas fa-download mr-2"></i> 执行导入
                    </button>
                </div>
            </div>
            
            <!-- Import Progress -->
            <div id="import-progress" class="mb-6 hidden">
                <h3 class="text-lg font-semibold text-white mb-4">导入进度</h3>
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-300">正在导入规则...</span>
                        <span id="import-progress-text" class="text-blue-400">0%</span>
                    </div>
                    <div class="w-full bg-gray-600 rounded-full h-2">
                        <div id="import-progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="import-status" class="text-sm text-gray-400 mt-2"></div>
                </div>
            </div>
            
            <!-- Import Results -->
            <div id="import-results" class="mb-6 hidden">
                <h3 class="text-lg font-semibold text-white mb-4">导入结果</h3>
                <div id="import-results-content" class="bg-gray-700 rounded-lg p-4 max-h-60 overflow-y-auto">
                    <!-- 导入结果将在这里显示 -->
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4">
                <button onclick="closeModal('import-rules-modal')" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- Search Modal -->
    <div id="search-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
        <div class="modal-content bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-4xl transform scale-95">
            <!-- 标题和连接信息区域 -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-white">从[医保对照表]检索</h2>
                <div id="medical-connection-info" class="text-sm text-gray-300 bg-gray-700 px-3 py-2 rounded-lg">
                    <i class="fas fa-database mr-2"></i>
                    <span id="medical-db-type">未连接</span>
                    <span id="medical-db-host" class="ml-2"></span>
                </div>
            </div>
            
            <!-- 搜索控制区域 -->
            <div class="flex gap-4 mb-4">
                <input type="text" id="medical-search-input"
                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg block flex-1 p-2.5"
                    placeholder="输入医保编码、医保名称、医院编码或医院名称进行搜索...">
                <select id="medical-schema-select" 
                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg px-3 py-2.5 min-w-[150px]">
                    <option value="">选择Schema</option>
                </select>
                <button onclick="searchMedicalInsurance()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                    <i class="fas fa-search mr-2"></i>搜索
                </button>
            </div>
            
            
            <!-- 搜索结果表格 -->
            <div class="max-h-80 overflow-y-auto">
                <table class="w-full text-sm text-left text-gray-400">
                    <thead class="text-xs text-gray-300 uppercase bg-gray-700 sticky top-0">
                        <tr>
                            <th class="px-4 py-2">
                                <input type="checkbox" id="select-all-medical" onchange="toggleSelectAllMedical()">
                            </th>
                            <th class="px-4 py-2">医保项目编码</th>
                            <th class="px-4 py-2">医保项目名称</th>
                            <th class="px-4 py-2">医院项目编码</th>
                            <th class="px-4 py-2">医院项目名称</th>
                            <th class="px-4 py-2">费用类别</th>
                            <th class="px-4 py-2">操作</th>
                        </tr>
                    </thead>
                    <tbody id="medical-search-results">
                        <!-- 搜索结果将在这里动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 选中项目显示区域 -->
            <div id="selected-medical-items" class="mt-4 p-3 bg-gray-700 rounded-lg hidden">
                <h4 class="text-white font-semibold mb-2">已选项目：</h4>
                <div id="selected-items-list" class="text-gray-300 text-sm"></div>
            </div>
            
            <!-- 操作按钮区域 -->
            <div class="flex justify-between items-center mt-6">
                <div class="text-gray-300 text-sm">
                    <span id="search-result-count">共找到 0 个项目</span>
                </div>
                <div class="flex gap-2">
                    <button onclick="confirmMedicalSelection()" 
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg">
                        确认选择
                    </button>
                <button onclick="closeModal('search-modal')"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Intelligent Workflow Wizard Modal -->
    <div id="workflow-wizard-modal"
        class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center hidden">
            <div
                class="modal-content bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 transform scale-95 flex flex-col">
            <!-- Header -->
            <div class="flex justify-between items-center p-6 border-b border-gray-700">
                <h2 class="text-2xl font-bold text-white">智能规则创建向导</h2>
                <button onclick="closeWorkflowWizard()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Progress Bar -->
            <div class="px-6 py-4 border-b border-gray-700">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm text-gray-300">进度</span>
                    <span id="workflow-progress-text" class="text-sm text-gray-300">0/5</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                        <div id="workflow-progress-bar"
                            class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <div class="flex justify-between mt-2 text-xs text-gray-400">
                    <span>规则属性</span>
                    <span>模板选择</span>
                    <span>参数填写</span>
                    <span>SQL预览</span>
                    <span>规则创建</span>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="flex-1 flex overflow-hidden">
                <!-- Step Content -->
                <div class="flex-1 p-6 overflow-y-auto">
                    <div id="workflow-content">
                        <!-- Dynamic content will be loaded here -->
                    </div>
                </div>
                
                <!-- Summary Panel -->
                <div class="w-80 bg-gray-900 p-6 border-l border-gray-700 overflow-y-auto">
                    <h3 class="text-lg font-semibold text-white mb-4">规则摘要</h3>
                    <div id="workflow-summary" class="space-y-3 text-sm">
                        <div class="text-gray-400">开始创建规则...</div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="flex justify-between items-center p-6 border-t border-gray-700">
                <button id="workflow-prev-btn" onclick="workflowPrevStep()" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg flex items-center"
                        disabled>
                    <i class="fas fa-arrow-left mr-2"></i> 上一步
                </button>
                <div class="flex space-x-2">
                    <button id="workflow-cancel-btn" onclick="closeWorkflowWizard()" 
                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                        取消
                    </button>
                    <button id="workflow-next-btn" onclick="workflowNextStep()" 
                        class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        下一步 <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- Global Variables ---
        let sqlTemplates = {};
        let isEditMode = false;
        let isEditingExistingRule = false;
        let originalRuleName = null;
        let currentWorkflow = null;
        let currentWorkflowStep = 0;
        const workflowSteps = [
            'rule_attributes',
            'template_selection', 
            'parameter_input',
            'sql_preview',
            'rule_creation'
        ];
        
        // Database Monitor Variables
        let monitorInterval = null;
        let isMonitoring = false;
        let databaseConnections = [];
        let recentAlerts = [];

        // --- DOM Elements ---
        const ruleTemplateSelect = document.getElementById('rule-template');
        const conditionsContainer = document.getElementById('conditions-container');
        const sqlOutput = document.getElementById('sql-output');
            const generateSqlBtn = document.getElementById('generate-sql-btn');

            // --- Toast Notification System ---
            function showToast(message, type = 'info', title = null, duration = 4000) {
                const container = document.getElementById('toast-container');
                const toast = document.createElement('div');
                const toastId = 'toast-' + Date.now();
                
                toast.id = toastId;
                toast.className = `toast ${type}`;
                
                const iconMap = {
                    success: 'fas fa-check-circle',
                    error: 'fas fa-exclamation-circle',
                    warning: 'fas fa-exclamation-triangle',
                    info: 'fas fa-info-circle'
                };
                
                const titleMap = {
                    success: '成功',
                    error: '错误',
                    warning: '警告',
                    info: '提示'
                };
                
                const displayTitle = title || titleMap[type] || '提示';
                
                toast.innerHTML = `
                    <div class="toast-header">
                        <div class="toast-title">
                            <i class="${iconMap[type]} mr-2"></i>${displayTitle}
                        </div>
                        <button class="toast-close" onclick="removeToast('${toastId}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="toast-message">${message}</div>
                `;
                
                container.appendChild(toast);
                
                // Trigger animation
                setTimeout(() => {
                    toast.classList.add('show');
                }, 10);
                
                // Auto remove after duration
                if (duration > 0) {
                    setTimeout(() => {
                        removeToast(toastId);
                    }, duration);
                }
                
                return toastId;
            }
            
            function removeToast(toastId) {
                const toast = document.getElementById(toastId);
                if (toast) {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }
            }
            
            function clearAllToasts() {
                const container = document.getElementById('toast-container');
                container.innerHTML = '';
            }

        // --- Modal Logic ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.remove('hidden');
            setTimeout(() => {
                modal.querySelector('.modal-content').classList.remove('scale-95');
                modal.classList.remove('opacity-0');
            }, 10);
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.querySelector('.modal-content').classList.add('scale-95');
            modal.classList.add('opacity-0');
            setTimeout(() => modal.classList.add('hidden'), 300);
        }

        // --- Template & Conditions Logic ---
        async function fetchSqlTemplates() {
            try {
                const response = await fetch('/api/sql-templates');
                if (!response.ok) {
                    throw new Error('Failed to fetch SQL templates');
                }
                sqlTemplates = await response.json();
                populateTemplateOptions();
            } catch (error) {
                console.error(error);
                ruleTemplateSelect.innerHTML = '<option value="">无法加载模板</option>';
            }
        }

        function populateTemplateOptions() {
            ruleTemplateSelect.innerHTML = '<option value="">选择一个模板</option>';
            for (const key in sqlTemplates) {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = key;
                ruleTemplateSelect.appendChild(option);
            }
        }

        // 获取模板推荐
        async function getTemplateRecommendations() {
            const ruleName = document.getElementById('rule-name').value.trim();
            const ruleType = document.getElementById('rule-type').value;
            const patientType = document.getElementById('patient-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const databaseType = document.getElementById('database-type').value;

            if (!ruleName && !ruleType) {
                alert('请先输入规则名称或选择规则类型！');
                return;
            }

            try {
                // 构建规则对象
                const rule = {
                    name: ruleName,
                    类型: ruleType,
                    适用范围: patientType,
                    匹配方式: matchMethod
                };

                // 检查是否为通用规则
                if (patientType === 'general') {
                    // 通用规则：同时获取门诊和住院模板
                    const [outpatientResponse, inpatientResponse] = await Promise.all([
                        fetch('/api/templates/select', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                rule: rule,
                                db_type: databaseType === 'oracle' ? 'oracle' : 'pg',
                                code_type: matchMethod === 'code' ? 'code' : 'name',
                                patient_type: 'outpatient'
                            }),
                        }),
                        fetch('/api/templates/select', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                rule: rule,
                                db_type: databaseType === 'oracle' ? 'oracle' : 'pg',
                                code_type: matchMethod === 'code' ? 'code' : 'name',
                                patient_type: 'inpatient'
                            }),
                        })
                    ]);

                    const outpatientResult = await outpatientResponse.json();
                    const inpatientResult = await inpatientResponse.json();

                    // 合并两个结果
                    const templates = [];
                    if (outpatientResult.success && outpatientResult.template) {
                        templates.push({
                            ...outpatientResult.template,
                            type: 'outpatient',
                            displayName: `[门诊] ${outpatientResult.template.name}`
                        });
                    }
                    if (inpatientResult.success && inpatientResult.template) {
                        templates.push({
                            ...inpatientResult.template,
                            type: 'inpatient',
                            displayName: `[住院] ${inpatientResult.template.name}`
                        });
                    }

                    if (templates.length > 0) {
                        // 显示多个模板推荐
                        displayMultipleTemplateRecommendations(templates);
                    } else {
                        document.getElementById('template-recommendations').innerHTML = 
                            '<p class="text-yellow-500 text-sm">未找到匹配的模板推荐</p>';
                    }
                } else {
                    // 非通用规则：使用原有逻辑
                    const response = await fetch('/api/templates/select', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            rule: rule,
                            db_type: databaseType === 'oracle' ? 'oracle' : 'pg',
                            code_type: matchMethod === 'code' ? 'code' : 'name',
                            patient_type: patientType === 'outpatient' ? 'outpatient' : 'inpatient'
                        }),
                    });

                    const result = await response.json();
                    
                    if (result.success && result.template) {
                        // 显示推荐的模板
                        displayTemplateRecommendation(result.template);
                        
                        // 自动选择推荐的模板
                        document.getElementById('rule-template').value = result.template.name;
                        setSqlContent(result.template.content);
                        parseAndRenderConditions(result.template.content);
                    } else {
                        document.getElementById('template-recommendations').innerHTML = 
                            '<p class="text-yellow-500 text-sm">未找到匹配的模板推荐</p>';
                    }
                }
            } catch (error) {
                console.error('Error getting template recommendations:', error);
                document.getElementById('template-recommendations').innerHTML = 
                    '<p class="text-red-500 text-sm">获取模板推荐失败: ' + error.message + '</p>';
            }
        }

        // 显示模板推荐
        function displayTemplateRecommendation(template) {
            const recommendationsDiv = document.getElementById('template-recommendations');
            recommendationsDiv.innerHTML = `
                <div class="bg-green-900 border border-green-600 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-green-300 font-semibold">推荐模板</h4>
                        <span class="text-xs text-green-400">${template.folder}</span>
                    </div>
                    <p class="text-green-200 text-sm mb-2">${template.name}</p>
                    <button onclick="useRecommendedTemplate('${template.name}')" 
                            class="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded">
                        使用此模板
                    </button>
                </div>
            `;
        }

        // 显示多个模板推荐（用于通用规则）
        function displayMultipleTemplateRecommendations(templates) {
            const recommendationsDiv = document.getElementById('template-recommendations');
            
            let html = '<div class="space-y-3">';
            html += '<h4 class="text-green-300 font-semibold mb-3">通用规则推荐模板</h4>';
            
            templates.forEach((template, index) => {
                const bgColor = template.type === 'outpatient' ? 'blue' : 'purple';
                const typeLabel = template.type === 'outpatient' ? '门诊' : '住院';
                const typeColor = template.type === 'outpatient' ? 'blue' : 'purple';
                
                html += `
                    <div class="bg-${bgColor}-900 border border-${bgColor}-600 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <h5 class="text-${bgColor}-300 font-semibold">${template.displayName}</h5>
                            <span class="text-xs text-${bgColor}-400 bg-${typeColor}-800 px-2 py-1 rounded">${typeLabel}</span>
                        </div>
                        <p class="text-${bgColor}-200 text-sm mb-2">${template.name}</p>
                        <div class="flex space-x-2">
                            <button onclick="useRecommendedTemplate('${template.name}', '${template.type}')" 
                                    class="bg-${bgColor}-600 hover:bg-${bgColor}-700 text-white text-xs px-3 py-1 rounded">
                                使用此模板
                            </button>
                            <button onclick="previewTemplateContent('${template.name}', '${template.content}')" 
                                    class="bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded">
                                预览内容
                            </button>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            recommendationsDiv.innerHTML = html;
        }

        // 使用推荐的模板
        function useRecommendedTemplate(templateName, templateType = null) {
            document.getElementById('rule-template').value = templateName;
            const sqlContent = sqlTemplates[templateName] || '';
            setSqlContent(sqlContent);
            parseAndRenderConditions(sqlContent);
            
            // 如果是通用规则且指定了模板类型，更新适用范围
            if (templateType && document.getElementById('patient-type').value === 'general') {
                const patientTypeSelect = document.getElementById('patient-type');
                patientTypeSelect.value = templateType;
                
                // 显示提示信息
                showToast(`已选择${templateType === 'outpatient' ? '门诊' : '住院'}模板，适用范围已自动调整`, 'info');
            }
        }

        // 预览模板内容
        function previewTemplateContent(templateName, content) {
            // 创建模态框显示模板内容
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-gray-800 border border-gray-600 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-white font-semibold">模板预览: ${templateName}</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-white">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="bg-gray-900 border border-gray-700 rounded p-4">
                        <pre class="text-green-400 text-sm overflow-x-auto">${content}</pre>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <button onclick="useRecommendedTemplate('${templateName}')" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                            使用此模板
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 显示保存选择对话框（用于通用规则）
        function showSaveChoiceDialog() {
            return new Promise((resolve) => {
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-gray-800 border border-gray-600 rounded-lg p-6 max-w-md w-full mx-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-white font-semibold">保存通用规则</h3>
                            <button onclick="this.closest('.fixed').remove(); resolve(null)" class="text-gray-400 hover:text-white">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <p class="text-gray-300 mb-4">请选择要保存的规则版本：</p>
                        <div class="space-y-3">
                            <button onclick="this.closest('.fixed').remove(); resolve('outpatient')" 
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-left">
                                <i class="fas fa-user-md mr-2"></i>保存为门诊规则
                            </button>
                            <button onclick="this.closest('.fixed').remove(); resolve('inpatient')" 
                                    class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-left">
                                <i class="fas fa-bed mr-2"></i>保存为住院规则
                            </button>
                            <button onclick="this.closest('.fixed').remove(); resolve('both')" 
                                    class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-left">
                                <i class="fas fa-save mr-2"></i>保存为通用规则
                            </button>
                        </div>
                        <div class="mt-4 flex justify-end">
                            <button onclick="this.closest('.fixed').remove(); resolve(null)" 
                                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                                取消
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
            });
        }

        // 重置表单（保存后）
        function resetFormAfterSave() {
            document.getElementById('rule-name').value = '';
            document.getElementById('rule-template').value = '';
            setSqlContent('-- 在左侧表单中填写信息以生成SQL...');
            document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">请先选择一个SQL模板</p>';

            // 重置编辑状态
            resetEditingState();

            // 如果在编辑模式，切换回预览模式
            if (isEditMode) {
                toggleEditMode();
            }
        }

        // 手动选择模板时的动态显示
        async function onTemplateSelectionChange() {
            const templateName = document.getElementById('rule-template').value;
            const databaseType = document.getElementById('database-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const patientType = document.getElementById('patient-type').value;

            if (!templateName) {
                setSqlContent('-- 在左侧表单中填写信息以生成SQL...');
                parseAndRenderConditions('');
                return;
            }

            try {
                // 首先尝试从推荐模板中获取内容
                // 构建查询参数
                const params = new URLSearchParams();
                params.append('rule_name', document.getElementById('rule-name').value.trim() || '测试规则');
                params.append('rule_type', document.getElementById('rule-type').value || '');
                params.append('patient_type', document.getElementById('patient-type').value || '');
                params.append('match_method', document.getElementById('match-method').value || '');
                params.append('database_type', document.getElementById('database-type').value || '');
                params.append('max_recommendations', '50'); // 增加推荐数量以包含更多模板
                
                const recommendationsResponse = await fetch(`/api/templates/recommend?${params.toString()}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (recommendationsResponse.ok) {
                    const recommendationsData = await recommendationsResponse.json();
                    console.log('Recommendations response:', recommendationsData);
                    
                    if (recommendationsData.status === 'success' && recommendationsData.data && recommendationsData.data.recommendations) {
                        const recommendations = recommendationsData.data.recommendations;
                        console.log('Looking for template:', templateName);
                        console.log('Available templates:', recommendations.map(r => r.template.id));
                        
                        const selectedTemplate = recommendations.find(rec => rec.template && rec.template.id === templateName);
                        
                        if (selectedTemplate && selectedTemplate.template && selectedTemplate.template.content) {
                            console.log('Found template with content:', selectedTemplate.template.id);
                            setSqlContent(selectedTemplate.template.content);
                            parseAndRenderConditions(selectedTemplate.template.content);
                            
                            // 显示模板信息
                            showTemplateInfo(selectedTemplate.template);
                            return;
                        } else {
                            console.log('Template not found in recommendations or no content');
                        }
                    }
                }

                // 如果推荐模板中没有找到，尝试从特定文件夹获取模板
                const response = await fetch('/api/templates/manual-select', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        template_name: templateName,
                        folder_name: `rule_${databaseType === 'oracle' ? 'oracle' : 'pg'}_${matchMethod === 'code' ? 'code' : 'name'}_${patientType === 'outpatient' ? 'outpatient' : 'inpatient'}`
                    }),
                });

                const result = await response.json();
                
                if (result.success && result.template) {
                    setSqlContent(result.template.content);
                    parseAndRenderConditions(result.template.content);
                    
                    // 显示模板信息
                    showTemplateInfo(result.template);
                } else {
                    // 尝试从所有可能的文件夹中查找模板
                    console.log('Trying to find template in all folders...');
                    const allFolders = [
                        `rule_${databaseType === 'oracle' ? 'oracle' : 'pg'}_${matchMethod === 'code' ? 'code' : 'name'}_${patientType === 'outpatient' ? 'outpatient' : 'inpatient'}`,
                        'rule_pg_name_inpatient',
                        'rule_pg_name_outpatient',
                        'rule_pg_code_inpatient',
                        'rule_pg_code_outpatient',
                        'rule_oracle_name_inpatient',
                        'rule_oracle_name_outpatient',
                        'rule_oracle_code_inpatient',
                        'rule_oracle_code_outpatient'
                    ];
                    
                    for (const folder of allFolders) {
                        try {
                            const folderResponse = await fetch('/api/templates/manual-select', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    template_name: templateName,
                                    folder_name: folder
                                }),
                            });
                            
                            const folderResult = await folderResponse.json();
                            if (folderResult.success && folderResult.template) {
                                console.log(`Found template in folder: ${folder}`);
                                setSqlContent(folderResult.template.content);
                                parseAndRenderConditions(folderResult.template.content);
                                showTemplateInfo(folderResult.template);
                                return;
                            }
                        } catch (folderError) {
                            console.log(`Error searching in folder ${folder}:`, folderError);
                        }
                    }
                    
                    // 如果所有方法都失败，回退到原有逻辑
                    const sqlContent = sqlTemplates[templateName] || '-- 模板未找到';
                    setSqlContent(sqlContent);
                    parseAndRenderConditions(sqlContent);
                    showToast(`模板 "${templateName}" 未找到，请检查模板名称或联系管理员`, 'warning');
                }
            } catch (error) {
                console.error('Error selecting template:', error);
                // 回退到原有逻辑
                const sqlContent = sqlTemplates[templateName] || '-- 模板加载失败';
                setSqlContent(sqlContent);
                parseAndRenderConditions(sqlContent);
            }
        }

        // 显示模板信息
        function showTemplateInfo(template) {
            const recommendationsDiv = document.getElementById('template-recommendations');
            recommendationsDiv.innerHTML = `
                <div class="bg-blue-900 border border-blue-600 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-blue-300 font-semibold">当前模板</h4>
                        <span class="text-xs text-blue-400">${template.category || '未知分类'}</span>
                    </div>
                    <p class="text-blue-200 text-sm">${template.description || template.id || '未知模板'}</p>
                    <p class="text-xs text-blue-300 mt-1">${template.database_type || '未知数据库'} | ${template.patient_type || '未知类型'}</p>
                </div>
            `;
        }

        ruleTemplateSelect.addEventListener('change', onTemplateSelectionChange);

        function parseAndRenderConditions(sql) {
            conditionsContainer.innerHTML = '';
            const regex = /\{([^}]+)\}/g;
            let match;
            const foundConditions = new Set();

            while ((match = regex.exec(sql)) !== null) {
                const paramName = match[1].trim();
                // 跳过特殊占位符
                if (['1=1', '2=2', '3=3', '4=4'].includes(paramName)) {
                    continue;
                }
                if (foundConditions.has(paramName)) continue;
                foundConditions.add(paramName);

                const isSearchable = paramName.includes('编码') || paramName.includes('名称');
                const isNumberType = paramName.includes('数量') || paramName.includes('天数') || 
                                   paramName.includes('小时数') || paramName.includes('金额') || 
                                   paramName.includes('年龄');
                const isGenderType = paramName === '性别';

                // 根据参数类型设置不同的操作符选项
                let operatorOptions = '';
                if (paramName.includes('诊断') || paramName.includes('科室')) {
                    operatorOptions = `
                        <option value="包含">包含</option>
                        <option value="等于">等于</option>
                        <option value="不等于">不等于</option>
                    `;
                } else if (isNumberType) {
                    operatorOptions = `
                        <option value="等于">等于</option>
                        <option value="大于">大于</option>
                        <option value="小于">小于</option>
                        <option value="大于等于">大于等于</option>
                        <option value="小于等于">小于等于</option>
                    `;
                } else if (isGenderType) {
                    operatorOptions = `
                        <option value="等于">等于</option>
                    `;
                } else {
                    operatorOptions = `
                        <option value="等于">等于</option>
                        <option value="不等于">不等于</option>
                        <option value="包含">包含</option>
                        <option value="IN">IN</option>
                    `;
                }

                const conditionHtml = `
                    <div class="flex items-center space-x-2 mb-3" data-param-name="${paramName}">
                        <label class="w-1/4 text-sm font-medium text-gray-300">${getParameterLabel(paramName)}</label>
                        <select class="condition-op bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-1/4">
                            ${operatorOptions}
                        </select>
                        <div class="relative w-1/2" data-param-name="${paramName}">
                            <input type="${isNumberType ? 'number' : 'text'}" 
                                   class="condition-value bg-gray-700 border border-gray-600 rounded-lg p-2.5 text-sm w-full pr-10"
                                   placeholder="${getParameterPlaceholder(paramName)}"
                                   data-param-name="${paramName}">
                            ${isSearchable ? `<button onclick="openSearchModal('${paramName}')" class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white"><i class="fas fa-search"></i></button>` : ''}
                        </div>
                    </div>
                `;
                conditionsContainer.insertAdjacentHTML('beforeend', conditionHtml);
            }
            if (foundConditions.size === 0) {
                conditionsContainer.innerHTML = '<p class="text-gray-500">该模板没有可配置的条件。</p>';
            }
                
            // 更新生成SQL按钮状态
            updateGenerateSqlButtonState();
        }
            
            // --- SQL Generation Logic ---
            function updateGenerateSqlButtonState() {
                const hasConditions = conditionsContainer.querySelectorAll('[data-param-name]').length > 0;
                const hasTemplate = document.getElementById('rule-template').value;
                
                if (hasConditions && hasTemplate) {
                    generateSqlBtn.disabled = false;
                    generateSqlBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                } else {
                    generateSqlBtn.disabled = true;
                    generateSqlBtn.classList.add('opacity-50', 'cursor-not-allowed');
                }
            }
            
            function generateSqlFromConditions() {
                try {
                    const templateContent = getCurrentSqlContent();
                    if (!templateContent || templateContent.startsWith('-- 在左侧表单中填写信息以生成SQL...')) {
                        showToast('请先选择一个SQL模板', 'warning');
                        return;
                    }
                    
                    const conditions = conditionsContainer.querySelectorAll('[data-param-name]');
                    if (conditions.length === 0) {
                        showToast('没有可配置的条件', 'warning');
                        return;
                    }
                    
                    let generatedSql = templateContent;
                    const replacements = {};
                    
                    // 收集所有条件值
                    conditions.forEach(condition => {
                        const paramName = condition.getAttribute('data-param-name');
                        const valueInput = condition.querySelector('.condition-value');
                        const operatorSelect = condition.querySelector('.condition-op');
                        
                        if (valueInput && valueInput.value.trim()) {
                            const value = valueInput.value.trim();
                            const operator = operatorSelect ? operatorSelect.value : '等于';
                            
                            // 根据操作符和参数类型格式化值
                            let formattedValue = value;
                            
                            // 特殊处理医保项目名称、编码等参数
                            if (paramName.includes('医保名称') || paramName.includes('医保编码') || 
                                paramName.includes('国家编码') || paramName.includes('国家名称')) {
                                // 处理多个项目的情况
                                if (value.includes(',') || value.includes('，')) {
                                    const items = value.split(/[,，;；、\s\n]+/).map(v => v.trim()).filter(v => v);
                                    formattedValue = items.map(item => `'${item}'`).join(', ');
                                } else {
                                    formattedValue = `'${value}'`;
                                }
                            } else if (operator === 'IN') {
                                // 处理IN操作符，将逗号分隔的值转换为括号格式
                                const values = value.split(/[,，；;、|]+/).map(v => `'${v.trim()}'`).join(', ');
                                formattedValue = `(${values})`;
                            } else if (operator === '包含') {
                                formattedValue = `'%${value}%'`;
                            } else if (operator === '等于') {
                                formattedValue = `'${value}'`;
                            } else if (operator === '不等于') {
                                formattedValue = `'${value}'`;
                            } else if (operator === '大于' || operator === '小于' || 
                                     operator === '大于等于' || operator === '小于等于') {
                                // 数字类型参数不需要加引号
                                formattedValue = value;
                            }
                            
                            replacements[paramName] = formattedValue;
                        }
                    });
                    
                    // 智能处理特殊占位符
                    const patientType = document.getElementById('patient-type').value;
                    const isOutpatient = patientType === 'outpatient';
                    
                    // 处理排除诊断
                    const excludeDiagnosis = replacements['排除诊断'];
                    if (excludeDiagnosis) {
                        const excludeArray = buildIlikeAnyArray(excludeDiagnosis.replace(/['"]/g, ''));
                        if (isOutpatient) {
                            replacements['1=1'] = `not (COALESCE(A.诊断名称,'') ILIKE ANY (${excludeArray}))`;
                        } else {
                            replacements['1=1'] = `not (COALESCE(A.出院诊断名称,'') ILIKE ANY (${excludeArray}) OR COALESCE(A.入院诊断名称,'') ILIKE ANY (${excludeArray}))`;
                        }
                    }
                    
                    // 处理排除科室
                    const excludeDepartment = replacements['排除科室'];
                    if (excludeDepartment) {
                        const excludeArray = buildIlikeAnyArray(excludeDepartment.replace(/['"]/g, ''));
                        replacements['2=2'] = `not COALESCE(B.开单科室名称,'') ILIKE ANY (${excludeArray})`;
                    }
                    
                    // 处理包含科室
                    const includeDepartment = replacements['包含科室'];
                    if (includeDepartment) {
                        const includeArray = buildIlikeAnyArray(includeDepartment.replace(/['"]/g, ''));
                        replacements['3=3'] = `COALESCE(B.开单科室名称,'') ILIKE ANY (${includeArray})`;
                    }
                    
                    // 处理包含诊断
                    const includeDiagnosis = replacements['包含诊断'];
                    if (includeDiagnosis) {
                        const includeArray = buildIlikeAnyArray(includeDiagnosis.replace(/['"]/g, ''));
                        if (isOutpatient) {
                            replacements['4=4'] = `COALESCE(A.诊断名称,'') ILIKE ANY (${includeArray})`;
                        } else {
                            replacements['4=4'] = `(COALESCE(A.出院诊断名称,'') ILIKE ANY (${includeArray}) OR COALESCE(A.入院诊断名称,'') ILIKE ANY (${includeArray}))`;
                        }
                    }
                    
                    // 替换模板中的占位符
                    Object.keys(replacements).forEach(paramName => {
                        const placeholder = `{${paramName}}`;
                        generatedSql = generatedSql.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacements[paramName]);
                    });
                    
                    // 更新SQL内容
                    setSqlContent(generatedSql);
                    
                    // 显示成功消息
                    const replacedCount = Object.keys(replacements).length;
                    showToast(`成功生成SQL！已替换 ${replacedCount} 个变量`, 'success');
                    
                    // 保持在预览模式，不自动切换到编辑模式
                    if (isEditMode) {
                        toggleEditMode();
                    }
                    
                } catch (error) {
                    console.error('Error generating SQL:', error);
                    showToast('生成SQL时发生错误: ' + error.message, 'error');
                }
            }
            
            // 构建ILIKE ANY数组格式
            function buildIlikeAnyArray(keywords) {
                if (!keywords) {
                    return "ARRAY[]::text[]";
                }
                // 替换所有分隔符为统一的|
                for (const sep of ['，', ',', '|']) {
                    keywords = keywords.replace(sep, '|');
                }
                // 分割并去除空白
                const items = keywords.split('|').map(kw => kw.trim()).filter(kw => kw);
                if (items.length === 0) {
                    return "ARRAY[]::text[]";
                }
                // 拼接成SQL数组
                const arrayStr = items.map(kw => `'%${kw}%'`).join(',');
                return `ARRAY[${arrayStr}]`;
            }
            
            // 获取参数标签
            function getParameterLabel(paramName) {
                const labels = {
                    '医保名称1': '医保项目名称1',
                    '医保名称2': '医保项目名称2',
                    '医保编码1': '医保项目编码1',
                    '医保编码2': '医保项目编码2',
                    '国家编码1': '国家医保编码1',
                    '国家编码2': '国家医保编码2',
                    '国家名称1': '国家医保名称1',
                    '国家名称2': '国家医保名称2',
                    '违规数量': '违规数量限制',
                    '违规天数': '违规天数限制',
                    '违规小时数': '违规小时数限制',
                    '违规金额': '违规金额限制',
                    '年龄': '年龄限制',
                    '性别': '性别限制',
                    '时间类型': '时间类型',
                    '排除诊断': '排除的诊断',
                    '排除科室': '排除的科室',
                    '包含科室': '包含的科室',
                    '包含诊断': '包含的诊断'
                };
                return labels[paramName] || paramName;
            }
            
            // 获取参数占位符
            function getParameterPlaceholder(paramName) {
                const placeholders = {
                    '医保名称1': '请输入医保项目名称1',
                    '医保名称2': '请输入医保项目名称2',
                    '医保编码1': '请输入医保项目编码1',
                    '医保编码2': '请输入医保项目编码2',
                    '国家编码1': '请输入国家医保编码1',
                    '国家编码2': '请输入国家医保编码2',
                    '国家名称1': '请输入国家医保名称1',
                    '国家名称2': '请输入国家医保名称2',
                    '违规数量': '请输入违规数量限制',
                    '违规天数': '请输入违规天数限制',
                    '违规小时数': '请输入违规小时数限制',
                    '违规金额': '请输入违规金额限制',
                    '年龄': '请输入年龄限制',
                    '性别': '请输入性别（男/女）',
                    '时间类型': '请输入时间类型',
                    '排除诊断': '多个诊断用逗号分隔，支持%通配符',
                    '排除科室': '多个科室用逗号分隔，支持%通配符',
                    '包含科室': '多个科室用逗号分隔，支持%通配符',
                    '包含诊断': '多个诊断用逗号分隔，支持%通配符'
                };
                return placeholders[paramName] || `请输入${paramName}`;
            }
            
            // 数据库类型改变时的处理
            function onDatabaseChange() {
                const schemaSelect = document.getElementById('schemaSelect');
                const hostInput = document.getElementById('hostInput');

                // 清空schema选择
                schemaSelect.innerHTML = '<option value="">请先输入主机IP</option>';

                // 清空主机IP输入
                hostInput.value = '';

                // 根据数据库类型设置默认提示
                const database = document.getElementById('databaseSelect').value;
                if (database === 'oracle') {
                    hostInput.placeholder = '输入Oracle主机IP (默认: 127.0.0.1, 用户: datachange)';
                } else {
                    hostInput.placeholder = '输入PostgreSQL主机IP (默认: *************, 用户: postgres)';
                }
                
                // 如果主机输入框为空，自动加载默认Schema
                if (!hostInput.value.trim()) {
                    loadDatabaseSchemas();
                }
            }

            // 加载数据库Schema列表
            function loadDatabaseSchemas() {
                console.log('loadDatabaseSchemas() 开始执行');
                
                const database = document.getElementById('databaseSelect').value;
                const hostInput = document.getElementById('hostInput');
                const schemaSelect = document.getElementById('schemaSelect');

                console.log('数据库类型:', database);
                console.log('主机输入框元素:', hostInput);
                console.log('Schema选择器元素:', schemaSelect);

                const host = hostInput.value.trim();
                console.log('主机地址:', host);

                // 检查必要元素是否存在
                if (!schemaSelect) {
                    console.error('schemaSelect 元素不存在');
                    return;
                }

                // 显示加载状态
                schemaSelect.innerHTML = '<option value="">加载中...</option>';
                schemaSelect.disabled = true;

                fetch('/api/database_schemas', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        database: database,
                        host: host || 'default'  // 如果没有输入主机，使用默认
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        schemaSelect.innerHTML = '<option value="">默认Schema</option>';
                        schemaSelect.disabled = false;

                        const schemas = data.schemas || [];
                        schemas.forEach(schema => {
                            const option = document.createElement('option');
                            option.value = schema;
                            option.textContent = schema;
                            schemaSelect.appendChild(option);
                        });

                        if (schemas.length === 0) {
                            schemaSelect.innerHTML = '<option value="">无可用Schema</option>';
                        }
                        
                        showToast(`成功加载 ${schemas.length} 个Schema`, 'success');
                    } else {
                        schemaSelect.innerHTML = '<option value="">加载失败</option>';
                        schemaSelect.disabled = false;
                        showToast('加载Schema失败: ' + (data.error || '未知错误'), 'warning');
                    }
                })
                .catch(error => {
                    console.error('加载Schema失败:', error);
                    schemaSelect.innerHTML = '<option value="">加载失败</option>';
                    schemaSelect.disabled = false;
                    showToast('加载Schema失败: ' + error.message, 'error');
                });
                
                console.log('loadDatabaseSchemas() 执行完成');
            }

            // 更新连接状态显示
            function updateConnectionStatus() {
                const statusDiv = document.getElementById('connection-status');
                const statusDot = statusDiv.querySelector('div');
                const statusText = statusDiv.querySelector('span');
                
                const database = document.getElementById('databaseSelect').value;
                const host = document.getElementById('hostInput').value.trim();
                
                if (host) {
                    statusDot.className = 'w-2 h-2 bg-yellow-500 rounded-full mr-2';
                    statusText.textContent = `${database.toUpperCase()} - ${host}`;
                    statusText.className = 'text-yellow-400';
                } else {
                    statusDot.className = 'w-2 h-2 bg-green-500 rounded-full mr-2';
                    statusText.textContent = `${database.toUpperCase()} - 默认主机`;
                    statusText.className = 'text-green-400';
                }
                
                // 同时更新医保搜索模态框中的连接信息
                updateMedicalConnectionInfo().catch(error => {
                    console.error('更新医保连接信息失败:', error);
                });
                
                // 如果医保搜索模态框是打开的，重新加载schema
                const searchModal = document.getElementById('search-modal');
                if (searchModal && !searchModal.classList.contains('hidden')) {
                    loadMedicalSchemas().catch(error => {
                        console.error('重新加载Schema失败:', error);
                    });
                }
            }
            
            // 更新医保搜索模态框中的数据库连接信息
            async function updateMedicalConnectionInfo() {
                const dbTypeSpan = document.getElementById('medical-db-type');
                const dbHostSpan = document.getElementById('medical-db-host');
                
                if (!dbTypeSpan || !dbHostSpan) return;
                
                try {
                    // 从配置文件加载数据库配置
                    const response = await fetch('/api/database/config/load', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            const config = result.config;
                            const database = document.getElementById('databaseSelect').value;
                            
                            // 显示数据库类型
                            const dbTypeText = database === 'oracle' ? 'Oracle' : 'PostgreSQL';
                            dbTypeSpan.textContent = dbTypeText;
                            
                            // 根据数据库类型显示对应的配置信息
                            if (database === 'oracle' && config.oracle) {
                                const oracleConfig = config.oracle;
                                dbHostSpan.textContent = `@ ${oracleConfig.host}:${oracleConfig.port}/${oracleConfig.dsn}`;
                                dbHostSpan.className = 'ml-2 text-green-400';
                            } else if (database === 'pg' && config.postgresql) {
                                const pgConfig = config.postgresql;
                                const databaseName = pgConfig.database || 'databasetools';
                                dbHostSpan.textContent = `@ ${pgConfig.host}:${pgConfig.port}/${databaseName}`;
                                dbHostSpan.className = 'ml-2 text-green-400';
                            } else {
                                // 如果配置不存在，使用默认值
                                const defaultHost = database === 'oracle' ? '127.0.0.1:1521/orcl' : '**************:5432/databasetools';
                                dbHostSpan.textContent = `@ ${defaultHost} (默认)`;
                                dbHostSpan.className = 'ml-2 text-yellow-400';
                            }
                        } else {
                            // 配置加载失败，使用界面上的值
                            const database = document.getElementById('databaseSelect').value;
                            const host = document.getElementById('hostInput').value.trim();
                            
                            const dbTypeText = database === 'oracle' ? 'Oracle' : 'PostgreSQL';
                            dbTypeSpan.textContent = dbTypeText;
                            
                            if (host) {
                                dbHostSpan.textContent = `@ ${host}`;
                                dbHostSpan.className = 'ml-2 text-yellow-400';
                            } else {
                                const defaultHost = database === 'oracle' ? '127.0.0.1' : '**************';
                                dbHostSpan.textContent = `@ ${defaultHost} (默认)`;
                                dbHostSpan.className = 'ml-2 text-green-400';
                            }
                        }
                    } else {
                        // 请求失败，使用界面上的值
                        const database = document.getElementById('databaseSelect').value;
                        const host = document.getElementById('hostInput').value.trim();
                        
                        const dbTypeText = database === 'oracle' ? 'Oracle' : 'PostgreSQL';
                        dbTypeSpan.textContent = dbTypeText;
                        
                        if (host) {
                            dbHostSpan.textContent = `@ ${host}`;
                            dbHostSpan.className = 'ml-2 text-yellow-400';
                        } else {
                            const defaultHost = database === 'oracle' ? '127.0.0.1' : '**************';
                            dbHostSpan.textContent = `@ ${defaultHost} (默认)`;
                            dbHostSpan.className = 'ml-2 text-green-400';
                        }
                    }
                } catch (error) {
                    console.error('加载数据库配置失败:', error);
                    // 出错时使用界面上的值
                    const database = document.getElementById('databaseSelect').value;
                    const host = document.getElementById('hostInput').value.trim();
                    
                    const dbTypeText = database === 'oracle' ? 'Oracle' : 'PostgreSQL';
                    dbTypeSpan.textContent = dbTypeText;
                    
                    if (host) {
                        dbHostSpan.textContent = `@ ${host}`;
                        dbHostSpan.className = 'ml-2 text-yellow-400';
                    } else {
                        const defaultHost = database === 'oracle' ? '127.0.0.1' : '**************';
                        dbHostSpan.textContent = `@ ${defaultHost} (默认)`;
                        dbHostSpan.className = 'ml-2 text-green-400';
                    }
                }
            }

            // 测试数据库连接
            function testConnection() {
                const database = document.getElementById('databaseSelect').value;
                const host = document.getElementById('hostInput').value.trim();
                
                showToast('正在测试数据库连接...', 'info');
                
                // 通过尝试加载Schema来测试连接
                loadDatabaseSchemas();
            }

            // 保存数据库配置
            async function saveDatabaseConfig() {
                const database = document.getElementById('databaseSelect').value;
                const host = document.getElementById('hostInput').value || 'default';
                const port = database === 'oracle' ? '1521' : '5432';
                
                // 这里可以添加用户名和密码的输入框，暂时使用默认值
                const username = database === 'oracle' ? 'datachange' : 'postgres';
                const password = database === 'oracle' ? 'drgs2019' : 'P@ssw0rd';

                try {
                    const response = await fetch('/api/database/config/save', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            type: database,
                            host: host,
                            port: port,
                            username: username,
                            password: password,
                            database: 'databasetools'
                        })
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            showToast('数据库配置已保存', 'success');
                        } else {
                            showToast('保存失败: ' + result.error, 'error');
                        }
                    } else {
                        throw new Error('保存失败');
                    }

                } catch (error) {
                    showToast('保存数据库配置失败: ' + error.message, 'error');
                }
            }

            // 加载数据库配置
            async function loadDatabaseConfig() {
                try {
                    const response = await fetch('/api/database/config/load', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            const config = result.config;
                            
                            // 根据当前选择的数据库类型加载对应配置
                            const currentDatabase = document.getElementById('databaseSelect').value;
                            
                            if (currentDatabase === 'oracle' && config.oracle) {
                                document.getElementById('hostInput').value = config.oracle.host;
                            } else if (currentDatabase === 'pg' && config.postgresql) {
                                document.getElementById('hostInput').value = config.postgresql.host;
                            }
                            
                            showToast('数据库配置已加载', 'success');
                        } else {
                            showToast('加载失败: ' + result.error, 'error');
                        }
                    } else {
                        throw new Error('加载失败');
                    }

                } catch (error) {
                    showToast('加载数据库配置失败: ' + error.message, 'error');
                }
            }

            // 验证SQL语法
            function validateSQL() {
                const sql = getCurrentSqlContent();
                
                if (!sql || sql.trim() === '' || sql.startsWith('-- 在左侧表单中填写信息以生成SQL...')) {
                    showToast('没有可验证的SQL', 'warning');
                    return;
                }

                // 检查SQL安全性
                const sqlUpper = sql.trim().toUpperCase();
                const dangerousKeywords = ['UPDATE', 'DELETE', 'INSERT', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE', 'GRANT', 'REVOKE'];
                
                for (const keyword of dangerousKeywords) {
                    if (sqlUpper.includes(keyword)) {
                        showToast(`SQL验证失败：出于安全考虑，不允许执行包含 ${keyword} 的语句`, 'error');
                        return;
                    }
                }

                // 基本语法检查
                const trimmedSql = sql.trim();
                if (!trimmedSql.toUpperCase().startsWith('SELECT')) {
                    showToast('SQL验证失败：只允许执行SELECT查询语句', 'error');
                    return;
                }

                if (!trimmedSql.includes('FROM')) {
                    showToast('SQL验证失败：缺少FROM子句', 'error');
                    return;
                }

                showToast('SQL语法验证通过，可以安全执行', 'success');
            }

            // 执行SQL查询
            function executeSQL() {
                const sql = getCurrentSqlContent();
                const database = document.getElementById('databaseSelect').value;
                const host = document.getElementById('hostInput').value.trim();
                const schema = document.getElementById('schemaSelect').value;

                if (!sql || sql.trim() === '' || sql.startsWith('-- 在左侧表单中填写信息以生成SQL...')) {
                    showToast('没有可执行的SQL', 'warning');
                    return;
                }

                // 检查SQL安全性（前端预检查）
                const sqlUpper = sql.trim().toUpperCase();
                const dangerousKeywords = ['UPDATE', 'DELETE', 'INSERT', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE', 'GRANT', 'REVOKE'];
                
                for (const keyword of dangerousKeywords) {
                    if (sqlUpper.includes(keyword)) {
                        showToast(`出于安全考虑，不允许执行包含 ${keyword} 的语句`, 'error');
                        return;
                    }
                }

                // 显示执行结果区域
                const resultDiv = document.getElementById('sqlResult');
                const resultContent = document.getElementById('resultContent');
                resultDiv.style.display = 'block';
                resultContent.innerHTML = '<div class="text-center text-gray-300"><i class="fas fa-spinner fa-spin mr-2"></i> 正在执行SQL...</div>';

                fetch('/api/rules/execute_sql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        sql: sql,
                        database: database,
                        host: host || 'default',
                        schema: schema
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showToast(`SQL执行成功！数据库: ${data.database}，返回 ${data.affected_rows} 条记录`, 'success');

                        // 显示详细执行结果
                        let resultHtml = `
                            <div class="bg-green-900 border border-green-600 rounded-lg p-3 mb-4">
                                <div class="text-green-300 font-semibold mb-2">
                                    <i class="fas fa-check-circle mr-2"></i>执行成功！
                                </div>
                                <div class="text-green-200 text-sm">
                                    数据库: ${data.database}<br>
                                    主机: ${data.host}<br>
                                    Schema: ${data.schema || '默认'}<br>
                                    返回行数: ${data.affected_rows}
                                </div>
                            </div>
                        `;

                        if (data.data && data.data.length > 0) {
                            resultHtml += `
                                <div class="mb-4">
                                    <h6 class="text-blue-300 font-semibold mb-3">
                                        <i class="fas fa-table mr-2"></i>查询结果
                                    </h6>
                                    <div class="overflow-x-auto" style="max-height: 400px;">
                                        <table class="w-full text-sm text-gray-300 border-collapse">
                                            <thead class="bg-gray-700 sticky top-0">`;

                            // 表头
                            if (data.columns && data.columns.length > 0) {
                                resultHtml += '<tr>';
                                data.columns.forEach(col => {
                                    resultHtml += `<th class="border border-gray-600 px-3 py-2 text-left font-medium" style="white-space: nowrap; font-size: 12px;">${col}</th>`;
                                });
                                resultHtml += '</tr>';
                            }
                            resultHtml += '</thead><tbody>';

                            // 数据行（显示所有返回的数据，后端已限制行数）
                            data.data.forEach((row, index) => {
                                resultHtml += `<tr class="${index % 2 === 0 ? 'bg-gray-800' : 'bg-gray-700'}">`;
                                if (Array.isArray(row)) {
                                    row.forEach(cell => {
                                        let cellValue = cell !== null ? cell : '<span class="text-gray-500">NULL</span>';
                                        // 限制单元格内容长度，避免过长
                                        if (typeof cellValue === 'string' && cellValue.length > 50) {
                                            cellValue = cellValue.substring(0, 50) + '...';
                                        }
                                        resultHtml += `<td class="border border-gray-600 px-3 py-2 text-xs" style="max-width: 200px; word-wrap: break-word;">${cellValue}</td>`;
                                    });
                                } else {
                                    let cellValue = row !== null ? row : '<span class="text-gray-500">NULL</span>';
                                    resultHtml += `<td class="border border-gray-600 px-3 py-2 text-xs">${cellValue}</td>`;
                                }
                                resultHtml += '</tr>';
                            });
                            resultHtml += `
                                        </tbody>
                                    </table>
                                </div>
                            `;

                            // 添加数据统计信息
                            resultHtml += `
                                <div class="flex justify-between items-center text-xs text-gray-400">
                                    <div>
                                        <i class="fas fa-chart-bar mr-1"></i>
                                        列数: ${data.columns ? data.columns.length : 0} |
                                        显示行数: ${data.data.length}
                                    </div>
                                    <div class="text-green-400">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        SQL语法验证通过
                                    </div>
                                </div>
                            `;
                        } else {
                            resultHtml += `
                                <div class="bg-blue-900 border border-blue-600 rounded-lg p-3">
                                    <div class="text-blue-200 text-sm">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        查询执行成功，但没有返回数据。
                                    </div>
                                </div>
                            `;
                        }

                        resultContent.innerHTML = resultHtml;
                    } else {
                        showToast('SQL执行失败：' + (data.error || '未知错误'), 'error');
                        resultContent.innerHTML = `
                            <div class="bg-red-900 border border-red-600 rounded-lg p-3">
                                <div class="text-red-300 font-semibold mb-2">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>执行失败
                                </div>
                                <div class="text-red-200">${data.error || '未知错误'}</div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('SQL执行失败:', error);
                    showToast('SQL执行失败: ' + error.message, 'error');
                    resultContent.innerHTML = `
                        <div class="bg-red-900 border border-red-600 rounded-lg p-3">
                            <div class="text-red-300 font-semibold mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>执行错误
                            </div>
                            <div class="text-red-200">${error.message}</div>
                        </div>
                    `;
                });
            }

            // 测试参数功能
            function testParameters() {
                try {
                    const conditions = conditionsContainer.querySelectorAll('[data-param-name]');
                    if (conditions.length === 0) {
                        // 如果没有参数，创建一个测试模板
                        const testTemplate = `
SELECT * FROM medical_records A
JOIN medical_items B ON A.patient_id = B.patient_id
WHERE B.医保项目名称 IN ({医保名称1})
  AND B.医保项目编码 IN ({医保编码1})
  AND A.违规数量 > {违规数量}
  AND 1=1
  AND 2=2
  AND 3=3
  AND 4=4
`;
                        setSqlContent(testTemplate);
                        parseAndRenderConditions(testTemplate);
                        showToast('已创建测试模板，请填写参数后再次测试', 'info');
                        return;
                    }
                    
                    const testResults = [];
                    conditions.forEach(condition => {
                        const paramName = condition.getAttribute('data-param-name');
                        const valueInput = condition.querySelector('.condition-value');
                        const operatorSelect = condition.querySelector('.condition-op');
                        
                        const value = valueInput ? valueInput.value.trim() : '';
                        const operator = operatorSelect ? operatorSelect.value : '等于';
                        
                        if (value) {
                            // 测试参数处理逻辑
                            let formattedValue = value;
                            
                            // 特殊处理医保项目名称、编码等参数
                            if (paramName.includes('医保名称') || paramName.includes('医保编码') || 
                                paramName.includes('国家编码') || paramName.includes('国家名称')) {
                                // 处理多个项目的情况
                                if (value.includes(',') || value.includes('，')) {
                                    const items = value.split(/[,，;；、|]+/).map(v => v.trim()).filter(v => v);
                                    formattedValue = items.map(item => `'${item}'`).join(', ');
                                } else {
                                    formattedValue = `'${value}'`;
                                }
                            } else if (operator === 'IN') {
                                const values = value.split(/[,，;；、|]+/).map(v => `'${v.trim()}'`).join(', ');
                                formattedValue = `(${values})`;
                            } else if (operator === '包含') {
                                formattedValue = `'%${value}%'`;
                            } else if (operator === '等于') {
                                formattedValue = `'${value}'`;
                            } else if (operator === '不等于') {
                                formattedValue = `'${value}'`;
                            } else if (operator === '大于' || operator === '小于' || 
                                     operator === '大于等于' || operator === '小于等于') {
                                formattedValue = value;
                            }
                            
                            testResults.push({
                                paramName: getParameterLabel(paramName),
                                originalValue: value,
                                operator: operator,
                                formattedValue: formattedValue
                            });
                        }
                    });
                    
                    if (testResults.length === 0) {
                        showToast('请先输入一些参数值进行测试', 'warning');
                        return;
                    }
                    
                    // 显示测试结果
                    let resultMessage = '参数处理测试结果：\n\n';
                    testResults.forEach(result => {
                        resultMessage += `${result.paramName}:\n`;
                        resultMessage += `  原始值: ${result.originalValue}\n`;
                        resultMessage += `  操作符: ${result.operator}\n`;
                        resultMessage += `  格式化后: ${result.formattedValue}\n\n`;
                    });
                    
                    console.log('参数测试结果:', testResults);
                    showToast(`测试完成！共处理 ${testResults.length} 个参数`, 'success');
                    
                    // 在控制台显示详细结果
                    console.log('详细测试结果:', resultMessage);
                    
                } catch (error) {
                    console.error('测试参数时发生错误:', error);
                    showToast('测试参数时发生错误: ' + error.message, 'error');
            }
        }

        // --- SQL Editor Mode Management ---
        function toggleEditMode() {
            const sqlPreview = document.getElementById('sql-preview');
            const sqlEditor = document.getElementById('sql-editor');
            const sqlOutput = document.getElementById('sql-output');
            const editModeText = document.getElementById('edit-mode-text');
            const toggleButton = document.getElementById('toggle-edit-mode');

            if (isEditMode) {
                // 切换到预览模式
                const editorContent = sqlEditor.value;
                sqlOutput.textContent = editorContent;
                hljs.highlightElement(sqlOutput);

                sqlPreview.style.display = 'block';
                sqlEditor.style.display = 'none';

                editModeText.textContent = '编辑模式';
                toggleButton.innerHTML = '<i class="fas fa-edit mr-2"></i> <span id="edit-mode-text">编辑模式</span>';
                isEditMode = false;
            } else {
                // 切换到编辑模式
                const previewContent = sqlOutput.textContent;
                sqlEditor.value = previewContent;

                sqlPreview.style.display = 'none';
                sqlEditor.style.display = 'block';

                editModeText.textContent = '预览模式';
                toggleButton.innerHTML = '<i class="fas fa-eye mr-2"></i> <span id="edit-mode-text">预览模式</span>';
                isEditMode = true;
                sqlEditor.focus();
            }
        }

        function getCurrentSqlContent() {
            if (isEditMode) {
                return document.getElementById('sql-editor').value;
            } else {
                return document.getElementById('sql-output').textContent;
            }
        }

        function setSqlContent(content) {
            const sqlOutput = document.getElementById('sql-output');
            const sqlEditor = document.getElementById('sql-editor');
            
            sqlOutput.textContent = content;
            sqlEditor.value = content;
            hljs.highlightElement(sqlOutput);
        }

        // --- Rule Attribute Management Logic ---
        async function loadRuleAttributeOptions() {
            try {
                const response = await fetch('/api/rules/attribute-options');
                if (!response.ok) {
                    throw new Error('Failed to fetch attribute options');
                }
                const data = await response.json();
                
                // Populate rule type options
                const ruleTypeSelect = document.getElementById('rule-type');
                data.rule_types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    ruleTypeSelect.appendChild(option);
                });

            } catch (error) {
                console.error('Error loading attribute options:', error);
            }
        }



            function showSuggestion(elementId, text) {
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = text;
                    element.style.display = 'block';
            }
        }

        async function getTemplateRecommendations() {
            const ruleName = document.getElementById('rule-name').value.trim();
            const ruleType = document.getElementById('rule-type').value;
            const patientType = document.getElementById('patient-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const databaseType = document.getElementById('database-type').value;

            if (!ruleName) {
                    showToast('请先输入规则名称！', 'warning');
                return;
            }

                // 显示加载状态
                const recommendationsDiv = document.getElementById('template-recommendations');
                recommendationsDiv.innerHTML = '<p class="text-blue-400 text-sm">正在获取推荐模板...</p>';

                try {
                    // Build query parameters with all filter conditions
                    const params = new URLSearchParams();
                    params.append('rule_name', ruleName);

                    // 添加所有筛选条件，即使为空也要传递
                    params.append('rule_type', ruleType || '');
                    params.append('patient_type', patientType || '');
                    params.append('match_method', matchMethod || '');
                    params.append('database_type', databaseType || '');
                    params.append('max_recommendations', '10');

                    console.log('Sending template recommendation request with params:', params.toString());

                    const response = await fetch(`/api/templates/recommend?${params.toString()}`);

                if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                    console.log('Template recommendation response:', data);
                
                    if (data.status === 'success' && data.data && data.data.recommendations) {
                        const recommendations = data.data.recommendations;
                displayTemplateRecommendations(recommendations);
                        updateTemplateDropdown(recommendations);
                    } else {
                        throw new Error(data.message || 'Invalid response format');
                    }

            } catch (error) {
                console.error('Error getting template recommendations:', error);
                    recommendationsDiv.innerHTML = `<p class="text-red-500 text-sm">获取模板推荐失败: ${error.message}</p>`;
                    showToast('获取模板推荐失败: ' + error.message, 'error');
            }
        }

        function displayTemplateRecommendations(recommendations) {
            const container = document.getElementById('template-recommendations');
            
            if (!recommendations || recommendations.length === 0) {
                container.innerHTML = '<p class="text-yellow-500 text-sm">没有找到匹配的模板推荐，请检查规则属性设置</p>';
                return;
            }

            // 只显示最相关的第一条推荐结果
            const topRecommendation = recommendations[0];
            const score = Math.round(topRecommendation.score * 100);
            
            // 安全获取模板信息
            const template = topRecommendation.template || {};
            const description = template.description || template.id || '未知模板';
            const category = template.category || '未知分类';
            const databaseType = template.database_type || '未知数据库';
            const patientType = template.patient_type || '未知类型';
            const reasons = Array.isArray(topRecommendation.reasons) ? topRecommendation.reasons : ['匹配度较高'];

            const html = `
                <div class="space-y-3">
                    <p class="text-sm text-gray-300 mb-3">找到最佳推荐模板:</p>
                    <div class="p-3 rounded-lg border border-green-500 bg-green-900">
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span class="text-white font-medium">${description}</span>
                                    <span class="ml-2 text-xs bg-green-600 text-white px-2 py-1 rounded">推荐</span>
                            </div>
                                <p class="text-xs text-gray-400 mt-1">${category} | ${databaseType} | ${patientType}</p>
                            </div>
                            <div class="text-right">
                                <span class="text-green-400 text-sm font-medium">${score}%</span>
                            </div>
                        </div>
                        <p class="text-gray-300 text-xs mb-2">${reasons.join(', ')}</p>
                        <button onclick="selectRecommendedTemplate('${template.id || ''}', '${description}')" 
                            class="w-full bg-purple-600 hover:bg-purple-700 text-white text-xs py-2 px-3 rounded">
                            选择此模板
                            </button>
                        </div>
                    </div>
                `;
            
            container.innerHTML = html;
        }

            function updateTemplateDropdown(recommendations) {
                const dropdown = document.getElementById('rule-template');
                const useRecommendedBtn = document.getElementById('use-recommended-template-btn');

                // Clear existing options
                dropdown.innerHTML = '<option value="">请选择模板</option>';

                // Add recommended templates first
                if (recommendations && recommendations.length > 0) {
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = '推荐模板';

                    recommendations.forEach(rec => {
                        const template = rec.template || {};
                        const option = document.createElement('option');
                        option.value = template.id || '';
                        const description = template.description || template.id || '未知模板';
                        const score = Math.round(rec.score * 100);
                        option.textContent = `${description} (${score}%)`;
                        optgroup.appendChild(option);
                    });

                    dropdown.appendChild(optgroup);

                    // Show use recommended button
                    if (recommendations[0] && recommendations[0].template) {
                        useRecommendedBtn.style.display = 'inline-flex';
                        const firstTemplate = recommendations[0].template;
                        const description = firstTemplate.description || firstTemplate.id || '未知模板';
                        useRecommendedBtn.onclick = () => selectRecommendedTemplate(firstTemplate.id || '', description);
                    }
                }
            }

            function selectRecommendedTemplate(templateId, templateDescription) {
            // Set the template in the dropdown
            document.getElementById('rule-template').value = templateId;
            
            // Trigger the change event to load the template
            const event = new Event('change');
            document.getElementById('rule-template').dispatchEvent(event);

                // 清除推荐模板区域，避免干扰
                const recommendationsDiv = document.getElementById('template-recommendations');
                recommendationsDiv.innerHTML = '';
            
            // Show success message
                showToast(`已选择推荐模板: ${templateDescription}`, 'success');
            }



            // 测试推荐功能
            async function testRecommendations() {
                // 设置测试数据
                document.getElementById('rule-name').value = '测试规则-超频次';
                document.getElementById('rule-type').value = '超频次';
                document.getElementById('patient-type').value = 'inpatient';
                document.getElementById('match-method').value = 'name';
                document.getElementById('database-type').value = 'postgresql';

                // 自动触发推荐
                await getTemplateRecommendations();
            }

            async function showAllTemplates() {
                try {
                    const response = await fetch('/api/sql-templates');
                    if (!response.ok) {
                        throw new Error('Failed to fetch templates');
                    }

                    const templates = await response.json();
                    const dropdown = document.getElementById('rule-template');

                    // Clear existing options
                    dropdown.innerHTML = '<option value="">请选择模板</option>';

                    // Add all templates
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = '所有模板';

                    Object.keys(templates).forEach(templateId => {
                        const option = document.createElement('option');
                        option.value = templateId;
                        option.textContent = templateId;
                        optgroup.appendChild(option);
                    });

                    dropdown.appendChild(optgroup);

                } catch (error) {
                    console.error('Error loading all templates:', error);
                    showToast('加载模板列表失败: ' + error.message, 'error');
                }
        }

        function translatePatientType(patientType) {
            const translations = {
                'inpatient': '住院',
                'outpatient': '门诊',
                'general': '通用'
            };
            return translations[patientType] || patientType;
        }

        function translateMatchMethod(matchMethod) {
            const translations = {
                'name': '按名称',
                'code': '按编码'
            };
            return translations[matchMethod] || matchMethod;
        }

        function validateRuleAttributes() {
            const ruleType = document.getElementById('rule-type').value;
            const patientType = document.getElementById('patient-type').value;
            const matchMethod = document.getElementById('match-method').value;
            const databaseType = document.getElementById('database-type').value;
            
            const errors = [];
            
            // Add validation rules as needed
            if (!databaseType) {
                errors.push('请选择数据库类型');
            }
            
            return errors;
        }

        // --- Edit and Delete Logic ---
        async function editRule(ruleName) {
            try {
                const response = await fetch(`/api/rules/${ruleName}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const rule = await response.json();

                // 设置编辑状态
                isEditingExistingRule = true;
                originalRuleName = rule.name;

                // 填充规则名称
                document.getElementById('rule-name').value = rule.name;

                // 填充规则属性
                if (rule.rule_type) {
                    document.getElementById('rule-type').value = rule.rule_type;
                }
                if (rule.patient_type) {
                    document.getElementById('patient-type').value = rule.patient_type;
                }
                if (rule.match_method) {
                    document.getElementById('match-method').value = rule.match_method;
                }
                if (rule.database_type) {
                    document.getElementById('database-type').value = rule.database_type;
                }
                if (rule.template_id) {
                    document.getElementById('rule-template').value = rule.template_id;
                }
                if (rule.description) {
                    document.getElementById('rule-description').value = rule.description;
                }
                if (rule.policy_basis) {
                    document.getElementById('rule-policy-basis').value = rule.policy_basis;
                }

                // 设置SQL内容
                setSqlContent(rule.content);

                // 清空模板推荐
                document.getElementById('template-recommendations').innerHTML = '<p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>';
                document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">当前为编辑模式，可直接修改SQL内容</p>';

                // 自动切换到编辑模式以便用户修改
                if (!isEditMode) {
                    toggleEditMode();
                }

                // 更新保存按钮文本
                const saveButton = document.getElementById('save-rule-btn');
                saveButton.innerHTML = '<i class="fas fa-save mr-2"></i> 更新规则';

                // 滚动到编辑器顶部
                window.scrollTo({ top: 0, behavior: 'smooth' });

                    showToast(`已加载规则 "${rule.name}" 进行编辑`, 'success');
            } catch (error) {
                console.error('Error fetching rule for editing:', error);
                    showToast('加载规则进行编辑时出错: ' + error.message, 'error');
            }
        }

        function resetEditingState() {
            isEditingExistingRule = false;
            originalRuleName = null;

            // 恢复保存按钮文本
            const saveButton = document.getElementById('save-rule-btn');
            saveButton.innerHTML = '<i class="fas fa-check-circle mr-2"></i> 保存规则';
        }

        async function deleteRule(ruleName) {
            if (!confirm(`您确定要删除规则 "${ruleName}" 吗？此操作无法撤销。`)) {
                return;
            }

            try {
                const response = await fetch(`/api/rules/${ruleName}`, {
                    method: 'DELETE',
                });
                const result = await response.json();
                if (response.ok) {
                        showToast(result.message, 'success');
                    await loadSavedRules(); // Refresh the list
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('Error deleting rule:', error);
                    showToast(`删除规则时出错: ${error.message}`, 'error');
            }
        }

        // --- Save Rule Logic ---
        async function saveRule() {
            const ruleName = document.getElementById('rule-name').value.trim();
            const sqlContent = getCurrentSqlContent();
            const patientType = document.getElementById('patient-type').value;

            if (!ruleName) {
                    showToast('请输入规则名称！', 'warning');
                return;
            }

            if (!sqlContent || sqlContent.trim() === '' || sqlContent.startsWith('-- 在左侧表单中填写信息以生成SQL...')) {
                    showToast('没有可保存的SQL内容！请先选择模板并填写条件，或直接在编辑模式下编写SQL。', 'warning');
                return;
            }

            // Validate rule attributes
            const attributeErrors = validateRuleAttributes();
            if (attributeErrors.length > 0) {
                    showToast('规则属性验证失败:\n' + attributeErrors.join('\n'), 'error', '验证失败', 6000);
                return;
            }

            // 确保内容是字符串类型
            const cleanSqlContent = String(sqlContent).trim();

            // 检查是否为通用规则
            if (patientType === 'general') {
                // 通用规则：需要用户选择保存门诊还是住院版本
                const saveChoice = await showSaveChoiceDialog();
                if (!saveChoice) return; // 用户取消
                
                // 根据用户选择保存相应版本的规则
                const ruleAttributes = {
                    name: saveChoice === 'both' ? ruleName : `[${saveChoice === 'outpatient' ? '门诊' : '住院'}]${ruleName}`,
                    content: cleanSqlContent,
                    description: document.getElementById('rule-description')?.value?.trim() || `通过规则编辑器${isEditingExistingRule ? '更新' : '创建'}的${saveChoice === 'outpatient' ? '门诊' : saveChoice === 'inpatient' ? '住院' : '通用'}规则`,
                    category: 'manual',
                    rule_type: document.getElementById('rule-type').value || null,
                    patient_type: saveChoice === 'both' ? 'general' : saveChoice,
                    match_method: document.getElementById('match-method').value || null,
                    database_type: document.getElementById('database-type').value || 'postgresql',
                    template_id: document.getElementById('rule-template').value || null,
                    policy_basis: document.getElementById('rule-policy-basis')?.value?.trim() || null
                };

                try {
                    let response;
                    if (isEditingExistingRule && originalRuleName) {
                        // 更新现有规则
                        response = await fetch(`/api/rules/${originalRuleName}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(ruleAttributes),
                        });
                    } else {
                        // 创建新规则
                        response = await fetch('/api/rules', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(ruleAttributes),
                        });
                    }

                    const result = await response.json();

                    if (response.ok) {
                        const action = isEditingExistingRule ? '更新' : '创建';
                        const typeLabel = saveChoice === 'outpatient' ? '门诊' : saveChoice === 'inpatient' ? '住院' : '通用';
                        showToast(`规则 "${ruleAttributes.name}" ${action}成功！`, 'success');

                        // 清空表单和重置状态
                        resetFormAfterSave();

                        // 刷新规则列表
                        await loadSavedRules();
                    } else {
                        showToast(`保存失败: ${result.message || '未知错误'}`, 'error');
                    }
                } catch (error) {
                    console.error('Error saving rule:', error);
                    showToast('保存规则时发生网络错误: ' + error.message, 'error');
                }
            } else {
                // 非通用规则：使用原有逻辑
                const ruleAttributes = {
                    name: ruleName,
                    content: cleanSqlContent,
                    description: document.getElementById('rule-description')?.value?.trim() || `通过规则编辑器${isEditingExistingRule ? '更新' : '创建'}的规则`,
                    category: 'manual',
                    rule_type: document.getElementById('rule-type').value || null,
                    patient_type: patientType,
                    match_method: document.getElementById('match-method').value || null,
                    database_type: document.getElementById('database-type').value || 'postgresql',
                    template_id: document.getElementById('rule-template').value || null,
                    policy_basis: document.getElementById('rule-policy-basis')?.value?.trim() || null
                };

                console.log('准备保存规则:', {
                    ...ruleAttributes,
                    isEditing: isEditingExistingRule,
                    originalName: originalRuleName
                });

                try {
                    let response;

                    if (isEditingExistingRule && originalRuleName) {
                        // 更新现有规则
                        response = await fetch(`/api/rules/${originalRuleName}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(ruleAttributes),
                        });
                    } else {
                        // 创建新规则
                        response = await fetch('/api/rules', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(ruleAttributes),
                        });
                    }

                    const result = await response.json();

                    if (response.ok) {
                        const action = isEditingExistingRule ? '更新' : '创建';
                        showToast(`规则 "${ruleName}" ${action}成功！`, 'success');

                        // 清空表单和重置状态
                        resetFormAfterSave();

                        // 刷新规则列表
                        await loadSavedRules();
                    } else {
                        showToast(`保存失败: ${result.message || '未知错误'}`, 'error');
                    }
                } catch (error) {
                    console.error('Error saving rule:', error);
                    showToast('保存规则时发生网络错误: ' + error.message, 'error');
                }
            }
        }

        // --- Rule Management Logic ---
        async function loadSavedRules() {
            try {
                const response = await fetch('/api/rules');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                const rules = data.rules || [];
                const tableBody = document.getElementById('rules-table-body');
                
                if (rules.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4 text-gray-500">暂无规则</td></tr>';
                    return;
                }

                tableBody.innerHTML = '';
                rules.forEach(rule => {
                    let createdAt = '未知';
                    if (rule.created_at) {
                        // 处理Unix时间戳（秒）或毫秒时间戳
                        const timestamp = rule.created_at * (rule.created_at < 10000000000 ? 1000 : 1);
                        createdAt = new Date(timestamp).toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                    }
                    const categoryText = rule.category === 'manual' ? '手动创建' : 
                                       rule.category === 'template' ? '模板生成' : 
                                       rule.category === 'intelligent' ? '智能生成' : '其他';
                    
                    const statusClass = rule.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
                    const statusText = rule.status === 'active' ? '活跃' : '非活跃';
                    
                    const row = `
                        <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                            <td class="px-6 py-4 font-medium text-white whitespace-nowrap">${rule.name}</td>
                            <td class="px-6 py-4 text-gray-300 max-w-xs truncate" title="${rule.description || ''}">${rule.description || '-'}</td>
                            <td class="px-6 py-4 text-gray-300 max-w-xs truncate" title="${rule.policy_basis || ''}">${rule.policy_basis || '-'}</td>
                            <td class="px-6 py-4">${categoryText}</td>
                            <td class="px-6 py-4"><span class="${statusClass} text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">${statusText}</span></td>
                            <td class="px-6 py-4">${createdAt}</td>
                            <td class="px-6 py-4 text-center">
                                <button onclick="editRule('${rule.name}')" class="font-medium text-blue-500 hover:underline">编辑</button>
                                <button onclick="deleteRule('${rule.name}')" class="font-medium text-red-500 hover:underline ml-4">删除</button>
                            </td>
                        </tr>
                    `;
                    tableBody.innerHTML += row;
                });

                // 显示分页信息（如果有的话）
                if (data.pagination) {
                    console.log(`加载了 ${rules.length} 个规则，总共 ${data.pagination.total_count} 个`);
                }

            } catch (error) {
                console.error('Error fetching saved rules:', error);
                const tableBody = document.getElementById('rules-table-body');
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-4 text-red-400">加载规则失败: ' + error.message + '</td></tr>';
            }
        }

        // --- Workflow Logic ---
            async function initializeWorkflowWizard() {
                try {
                    // 获取规则名称
                    const ruleName = document.getElementById('rule-name').value.trim() || '新规则';

                    // 启动工作流
                    const response = await fetch('/api/workflow/start', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            rule_name: ruleName
                        }),
                    });

                    if (!response.ok) {
                        throw new Error('Failed to start workflow');
                    }

                    const result = await response.json();
                    currentWorkflow = result.data;
            currentWorkflowStep = 0;

            updateWorkflowProgress();
                    await loadWorkflowStep(workflowSteps[currentWorkflowStep]);
                    updateWorkflowSummary();

                } catch (error) {
                    console.error('Error initializing workflow:', error);
                    alert('启动智能向导失败: ' + error.message);
                    closeWorkflowWizard();
                }
        }

        function closeWorkflowWizard() {
            closeModal('workflow-wizard-modal');
            currentWorkflow = null;
            currentWorkflowStep = 0;
        }

            async function workflowPrevStep() {
            if (currentWorkflowStep > 0) {
                currentWorkflowStep--;
                updateWorkflowProgress();
                    await loadWorkflowStep(workflowSteps[currentWorkflowStep]);
                    updateWorkflowSummary();
                }
            }

            async function workflowNextStep() {
                try {
                    // 验证当前步骤并提交数据
                    const isValid = await validateAndSubmitCurrentStep();
                    if (!isValid) {
                        return;
                    }

            if (currentWorkflowStep < workflowSteps.length - 1) {
                currentWorkflowStep++;
                updateWorkflowProgress();
                        await loadWorkflowStep(workflowSteps[currentWorkflowStep]);
                        updateWorkflowSummary();
            } else {
                // 完成向导
                        await completeWorkflowWizard();
                    }
                } catch (error) {
                    console.error('Error in workflow next step:', error);
                    alert('工作流步骤执行失败: ' + error.message);
            }
        }

        function updateWorkflowProgress() {
            const progress = ((currentWorkflowStep + 1) / workflowSteps.length) * 100;
            document.getElementById('workflow-progress-bar').style.width = progress + '%';
            document.getElementById('workflow-progress-text').textContent = `${currentWorkflowStep + 1}/${workflowSteps.length}`;
            
            // 更新按钮状态
            document.getElementById('workflow-prev-btn').disabled = currentWorkflowStep === 0;
            const nextBtn = document.getElementById('workflow-next-btn');
            if (currentWorkflowStep === workflowSteps.length - 1) {
                nextBtn.innerHTML = '<i class="fas fa-check mr-2"></i> 完成';
            } else {
                nextBtn.innerHTML = '下一步 <i class="fas fa-arrow-right ml-2"></i>';
            }
        }

            async function validateAndSubmitCurrentStep() {
                const stepName = workflowSteps[currentWorkflowStep];

                switch (stepName) {
                    case 'rule_attributes':
                        return await submitRuleAttributes();
                    case 'template_selection':
                        return await submitTemplateSelection();
                    case 'parameter_input':
                        return await submitParameters();
                    case 'sql_preview':
                        return await submitSqlPreview();
                    case 'rule_creation':
                        return true; // Final step, no validation needed
                    default:
                        return true;
                }
            }

            async function submitRuleAttributes() {
                const attributes = {
                    name: document.getElementById('wizard-rule-name')?.value?.trim(),
                    rule_type: document.getElementById('wizard-rule-type')?.value,
                    patient_type: document.getElementById('wizard-patient-type')?.value,
                    match_method: document.getElementById('wizard-match-method')?.value,
                    database_type: document.getElementById('wizard-database-type')?.value,
                    description: document.getElementById('wizard-description')?.value?.trim() || '',
                    category: 'intelligent',
                    policy_basis: document.getElementById('wizard-policy-basis')?.value?.trim() || ''
                };

                // 验证必填字段
                if (!attributes.name) {
                    alert('请输入规则名称');
                    return false;
                }

                try {
                    const response = await fetch(`/api/workflow/${currentWorkflow.workflow_id}/attributes`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(attributes),
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || 'Failed to update attributes');
                    }

                    const result = await response.json();
                    currentWorkflow = result.data;
                    return true;

                } catch (error) {
                    console.error('Error submitting rule attributes:', error);
                    alert('提交规则属性失败: ' + error.message);
                    return false;
                }
            }

            async function submitTemplateSelection() {
                const selectedTemplateId = document.querySelector('input[name="wizard-template"]:checked')?.value;

                if (!selectedTemplateId) {
                    alert('请选择一个模板');
                    return false;
                }

                try {
                    const response = await fetch(`/api/workflow/${currentWorkflow.workflow_id}/template`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            template_id: selectedTemplateId
                        }),
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || 'Failed to select template');
                    }

                    const result = await response.json();
                    currentWorkflow = result.data;
                    return true;

                } catch (error) {
                    console.error('Error submitting template selection:', error);
                    alert('选择模板失败: ' + error.message);
                    return false;
                }
            }

            async function submitParameters() {
                const parameters = {};
                const paramInputs = document.querySelectorAll('#wizard-conditions-container input, #wizard-conditions-container select, #wizard-conditions-container textarea');

                paramInputs.forEach(input => {
                    const paramName = input.dataset.paramName;
                    if (paramName) {
                        parameters[paramName] = input.value;
                    }
                });

                try {
                    const response = await fetch(`/api/workflow/${currentWorkflow.workflow_id}/parameters`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            parameters: parameters
                        }),
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || 'Failed to submit parameters');
                    }

                    const result = await response.json();
                    currentWorkflow = result.data;
                    return true;

                } catch (error) {
                    console.error('Error submitting parameters:', error);
                    alert('提交参数失败: ' + error.message);
                    return false;
                }
            }

            async function submitSqlPreview() {
                const sqlContent = document.getElementById('wizard-sql-editor')?.value || currentWorkflow.generated_sql;

                if (sqlContent !== currentWorkflow.generated_sql) {
                    // SQL was manually edited, update it
                    try {
                        const response = await fetch(`/api/workflow/${currentWorkflow.workflow_id}/sql`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                sql_content: sqlContent
                            }),
                        });

                        if (!response.ok) {
                            const error = await response.json();
                            throw new Error(error.message || 'Failed to update SQL');
                        }

                        const result = await response.json();
                        currentWorkflow = result.data;

                    } catch (error) {
                        console.error('Error updating SQL:', error);
                        alert('更新SQL失败: ' + error.message);
                        return false;
                    }
                }

                return true;
            }

            async function loadWorkflowStep(stepName) {
            const contentDiv = document.getElementById('workflow-content');
            
                switch (stepName) {
                case 'rule_attributes':
                        await loadRuleAttributesStep(contentDiv);
                        break;
                    case 'template_selection':
                        await loadTemplateSelectionStep(contentDiv);
                        break;
                    case 'parameter_input':
                        await loadParameterInputStep(contentDiv);
                        break;
                    case 'sql_preview':
                        await loadSqlPreviewStep(contentDiv);
                        break;
                    case 'rule_creation':
                        await loadRuleCreationStep(contentDiv);
                        break;
                }
            }

            async function loadRuleAttributesStep(contentDiv) {
                const stepData = currentWorkflow?.current_step_data || {};
                const suggestedAttributes = stepData.suggested_attributes || {};

                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第1步: 规则属性设置</h3>
                        <div class="space-y-4">
                            <div>
                        <label class="block mb-2 text-sm font-medium text-gray-300">规则名称 <span class="text-red-400">*</span></label>
                        <input type="text" id="wizard-rule-name" 
                               value="${stepData.rule_name || ''}"
                               class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                               placeholder="例如：限定支付-限单人多次">
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-300">规则类型</label>
                                    <select id="wizard-rule-type" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="">自动检测</option>
                                <option value="超频次" ${suggestedAttributes.detected_rule_type === '超频次' ? 'selected' : ''}>超频次</option>
                                <option value="重复收费" ${suggestedAttributes.detected_rule_type === '重复收费' ? 'selected' : ''}>重复收费</option>
                                <option value="限性别" ${suggestedAttributes.detected_rule_type === '限性别' ? 'selected' : ''}>限性别</option>
                                <option value="限年龄" ${suggestedAttributes.detected_rule_type === '限年龄' ? 'selected' : ''}>限年龄</option>
                                <option value="限定支付" ${suggestedAttributes.detected_rule_type === '限定支付' ? 'selected' : ''}>限定支付</option>
                                <option value="超住院天数" ${suggestedAttributes.detected_rule_type === '超住院天数' ? 'selected' : ''}>超住院天数</option>
                                    </select>
                            ${suggestedAttributes.detected_rule_type ? `<p class="text-xs text-green-400 mt-1">智能检测: ${suggestedAttributes.detected_rule_type}</p>` : ''}
                                </div>
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-300">适用范围</label>
                                    <select id="wizard-patient-type" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="inpatient" ${suggestedAttributes.detected_patient_type === 'inpatient' ? 'selected' : ''}>住院</option>
                                <option value="outpatient" ${suggestedAttributes.detected_patient_type === 'outpatient' ? 'selected' : ''}>门诊</option>
                                <option value="general" ${suggestedAttributes.detected_patient_type === 'general' ? 'selected' : ''}>通用</option>
                                    </select>
                            ${suggestedAttributes.detected_patient_type ? `<p class="text-xs text-green-400 mt-1">智能检测: ${suggestedAttributes.detected_patient_type === 'inpatient' ? '住院' : suggestedAttributes.detected_patient_type === 'outpatient' ? '门诊' : '通用'}</p>` : ''}
                                </div>
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-300">匹配方式</label>
                                    <select id="wizard-match-method" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="name" ${suggestedAttributes.detected_match_method === 'name' ? 'selected' : ''}>按名称</option>
                                <option value="code" ${suggestedAttributes.detected_match_method === 'code' ? 'selected' : ''}>按编码</option>
                                    </select>
                            ${suggestedAttributes.detected_match_method ? `<p class="text-xs text-green-400 mt-1">智能检测: ${suggestedAttributes.detected_match_method === 'name' ? '按名称' : '按编码'}</p>` : ''}
                                </div>
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-300">数据库类型</label>
                                    <select id="wizard-database-type" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="postgresql" ${suggestedAttributes.suggested_database_type === 'postgresql' ? 'selected' : ''}>PostgreSQL</option>
                                <option value="oracle" ${suggestedAttributes.suggested_database_type === 'oracle' ? 'selected' : ''}>Oracle</option>
                                    </select>
                                </div>
                            </div>
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-300">规则描述</label>
                            <textarea id="wizard-description" 
                                      class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                      rows="2" 
                                      placeholder="描述规则的业务逻辑和用途"></textarea>
                        </div>
                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-300">政策依据</label>
                            <input type="text" id="wizard-policy-basis" 
                                   class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                   placeholder="相关政策文件或条款">
                                </div>
                            </div>
                        </div>
                    `;
            }

            async function loadTemplateSelectionStep(contentDiv) {
                const stepData = currentWorkflow?.current_step_data || {};
                const recommendations = stepData.recommendations || [];

                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第2步: 模板选择</h3>
                        <div class="space-y-4">
                    ${recommendations.length > 0 ? `
                        <div class="mb-4">
                            <h4 class="text-lg font-semibold text-white mb-3">智能推荐模板</h4>
                            <div class="space-y-3">
                                ${recommendations.map((rec, index) => `
                                    <div class="bg-gray-700 border border-gray-600 rounded-lg p-4">
                                        <div class="flex items-start justify-between">
                                            <div class="flex items-start space-x-3">
                                                <input type="radio" name="wizard-template" value="${rec.template.id}" 
                                                       id="template-${index}" ${index === 0 ? 'checked' : ''}
                                                       class="mt-1 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500">
                                                <div class="flex-1">
                                                    <label for="template-${index}" class="block text-white font-medium cursor-pointer">
                                                        ${rec.template.description}
                                                    </label>
                                                    <p class="text-sm text-gray-400 mt-1">${rec.template.category} | ${rec.template.database_type} | ${rec.template.patient_type}</p>
                                                    <div class="flex items-center mt-2">
                                                        <span class="text-xs bg-green-600 text-white px-2 py-1 rounded">匹配度: ${Math.round(rec.score * 100)}%</span>
                                                        <div class="ml-2 text-xs text-gray-400">
                                                            ${rec.reasons.join(', ')}
                            </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : `
                        <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-4">
                            <p class="text-yellow-200">未找到匹配的模板推荐，请检查规则属性设置。</p>
                        </div>
                    `}
                        </div>
                    `;
            }

            async function loadParameterInputStep(contentDiv) {
                const stepData = currentWorkflow?.current_step_data || {};
                const formConfig = stepData.form_config || {};

                if (!formConfig.parameters || formConfig.parameters.length === 0) {
                    contentDiv.innerHTML = `
                    <h3 class="text-xl font-semibold text-white mb-4">第3步: 参数填写</h3>
                    <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-4">
                        <p class="text-yellow-200">请先选择模板</p>
                    </div>
                `;
                    return;
                }

                let parametersHtml = '';
                formConfig.parameters.forEach((param, index) => {
                    const isRequired = param.required ? '<span class="text-red-400">*</span>' : '';
                    const placeholder = param.placeholder || `请输入${param.label}`;

                    if (param.type === 'select') {
                        parametersHtml += `
                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-300">${param.label} ${isRequired}</label>
                            <select data-param-name="${param.name}" 
                                    class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                                <option value="">请选择</option>
                                ${param.options.map(option => `<option value="${option.value}">${option.label}</option>`).join('')}
                            </select>
                            ${param.description ? `<p class="text-xs text-gray-400 mt-1">${param.description}</p>` : ''}
                        </div>
                    `;
                    } else if (param.type === 'textarea') {
                        parametersHtml += `
                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-300">${param.label} ${isRequired}</label>
                            <textarea data-param-name="${param.name}" 
                                      class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                      rows="3" 
                                      placeholder="${placeholder}"></textarea>
                            ${param.description ? `<p class="text-xs text-gray-400 mt-1">${param.description}</p>` : ''}
                        </div>
                    `;
                    } else {
                        parametersHtml += `
                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-300">${param.label} ${isRequired}</label>
                            <div class="flex items-center space-x-2">
                                <input type="${param.type || 'text'}" 
                                       data-param-name="${param.name}" 
                                       class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" 
                                       placeholder="${placeholder}">
                                ${param.searchable ? `
                                    <button type="button" onclick="openSearchModal('${param.name}')" 
                                            class="bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-2 rounded-lg flex items-center">
                                        <i class="fas fa-search"></i>
                                    </button>
                                ` : ''}
                            </div>
                            ${param.description ? `<p class="text-xs text-gray-400 mt-1">${param.description}</p>` : ''}
                        </div>
                    `;
                    }
                });

                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第3步: 参数填写</h3>
                        <div id="wizard-conditions-container" class="space-y-4">
                    ${parametersHtml}
                </div>
                <div class="mt-6">
                    <button type="button" onclick="previewSqlInWizard()" 
                            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-eye mr-2"></i> 预览SQL
                    </button>
                        </div>
                    `;
            }

            async function loadSqlPreviewStep(contentDiv) {
                const generatedSql = currentWorkflow?.generated_sql || '-- SQL将在此处显示';

                    contentDiv.innerHTML = `
                <h3 class="text-xl font-semibold text-white mb-4">第4步: SQL预览与编辑</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <h4 class="text-lg font-semibold text-white">生成的SQL</h4>
                        <div class="flex space-x-2">
                            <button type="button" onclick="toggleWizardSqlEdit()" 
                                    class="bg-gray-600 hover:bg-gray-700 text-white text-sm px-3 py-2 rounded-lg flex items-center">
                                <i class="fas fa-edit mr-2"></i> <span id="wizard-edit-mode-text">编辑</span>
                            </button>
                            <button type="button" onclick="copyWizardSql()" 
                                    class="bg-gray-600 hover:bg-gray-700 text-white text-sm px-3 py-2 rounded-lg flex items-center">
                                <i class="fas fa-copy mr-2"></i> 复制
                            </button>
                        </div>
                    </div>
                    <div class="bg-gray-900 rounded-lg p-1 relative">
                        <pre id="wizard-sql-preview" class="language-sql h-64 block p-4 overflow-auto" style="margin: 0;">
                            <code id="wizard-sql-output" class="language-sql">${generatedSql}</code>
                        </pre>
                        <textarea id="wizard-sql-editor" 
                                  class="w-full h-64 bg-gray-900 text-gray-300 font-mono text-sm p-4 border-none outline-none resize-none" 
                                  style="display: none;">${generatedSql}</textarea>
                    </div>
                        </div>
                    `;

                // 应用语法高亮
                setTimeout(() => {
                    if (typeof hljs !== 'undefined') {
                        hljs.highlightElement(document.getElementById('wizard-sql-output'));
                    }
                }, 100);
            }

            async function loadRuleCreationStep(contentDiv) {
                    contentDiv.innerHTML = `
                        <h3 class="text-xl font-semibold text-white mb-4">第5步: 规则创建</h3>
                        <div class="space-y-4">
                    <div class="bg-blue-900 border border-blue-600 rounded-lg p-4">
                        <h4 class="text-blue-300 font-semibold mb-2">准备创建规则</h4>
                        <p class="text-blue-200 text-sm mb-4">请确认规则信息无误，点击"完成"按钮创建规则。</p>
                        <button type="button" onclick="finalizeWorkflowRule()" 
                                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center">
                            <i class="fas fa-check mr-2"></i> 创建规则
                        </button>
                            </div>
                    <div id="rule-creation-result" class="hidden">
                        <div class="bg-green-900 border border-green-600 rounded-lg p-4">
                            <h4 class="text-green-300 font-semibold mb-2">规则创建成功</h4>
                            <p class="text-green-200 text-sm mb-4">您的规则已成功创建并保存。</p>
                            <div class="flex space-x-2">
                                <button type="button" onclick="applyWizardToMainForm()" 
                                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">
                                应用到主表单
                            </button>
                                <button type="button" onclick="closeWorkflowWizard()" 
                                        class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">
                                    关闭向导
                            </button>
                            </div>
                        </div>
                    </div>
                        </div>
                    `;
        }

        function updateWorkflowSummary() {
            const summaryDiv = document.getElementById('workflow-summary');

                if (!currentWorkflow) {
                    summaryDiv.innerHTML = '<div class="text-gray-400 text-sm">工作流未启动</div>';
                    return;
                }

                const summary = currentWorkflow.summary || {};
                const ruleInfo = summary.rule_info || {};
                const selectedTemplate = summary.selected_template || {};

                let summaryHtml = `
                <div class="space-y-3 text-sm">
                    <div class="border-b border-gray-700 pb-2">
                        <h4 class="text-white font-semibold mb-2">规则信息</h4>
                        <div class="space-y-1">
                            <div><span class="text-gray-400">名称:</span> <span class="text-white">${ruleInfo.name || '未设置'}</span></div>
                            <div><span class="text-gray-400">类型:</span> <span class="text-white">${ruleInfo.rule_type || '未设置'}</span></div>
                            <div><span class="text-gray-400">范围:</span> <span class="text-white">${ruleInfo.patient_type === 'inpatient' ? '住院' : ruleInfo.patient_type === 'outpatient' ? '门诊' : ruleInfo.patient_type || '未设置'}</span></div>
                            <div><span class="text-gray-400">数据库:</span> <span class="text-white">${ruleInfo.database_type || '未设置'}</span></div>
                        </div>
                    </div>
                    
                    ${selectedTemplate.id ? `
                        <div class="border-b border-gray-700 pb-2">
                            <h4 class="text-white font-semibold mb-2">选择的模板</h4>
                            <div class="space-y-1">
                                <div><span class="text-gray-400">模板:</span> <span class="text-white">${selectedTemplate.description || selectedTemplate.id}</span></div>
                            </div>
                        </div>
                    ` : ''}
                    
                    <div>
                        <h4 class="text-white font-semibold mb-2">进度</h4>
                        <div class="space-y-1">
                            <div><span class="text-gray-400">当前步骤:</span> <span class="text-white">${currentWorkflowStep + 1}/5</span></div>
                            <div><span class="text-gray-400">SQL生成:</span> <span class="text-white">${summary.has_generated_sql ? '已生成' : '未生成'}</span></div>
                        </div>
                    </div>
                </div>
            `;

            summaryDiv.innerHTML = summaryHtml;
        }

        async function getWizardTemplateRecommendations() {
            const ruleName = document.getElementById('wizard-rule-name')?.value || '';
            const ruleType = document.getElementById('wizard-rule-type')?.value || '';
            const patientType = document.getElementById('wizard-patient-type')?.value || 'inpatient';
            const matchMethod = document.getElementById('wizard-match-method')?.value || 'name';
            const databaseType = document.getElementById('wizard-database-type')?.value || 'postgresql';

            try {
                const rule = {
                    name: ruleName,
                    类型: ruleType,
                    适用范围: patientType,
                    匹配方式: matchMethod
                };

                const response = await fetch('/api/templates/select', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        rule: rule,
                        db_type: databaseType === 'oracle' ? 'oracle' : 'pg',
                        code_type: matchMethod === 'code' ? 'code' : 'name',
                        patient_type: patientType === 'outpatient' ? 'outpatient' : 'inpatient'
                    }),
                });

                const result = await response.json();
                
                if (result.success && result.template) {
                    document.getElementById('wizard-template-recommendations').innerHTML = `
                        <div class="bg-green-900 border border-green-600 rounded-lg p-4">
                            <h4 class="text-green-300 font-semibold mb-2">推荐模板</h4>
                            <p class="text-green-200 text-sm mb-2">${result.template.name}</p>
                            <p class="text-green-400 text-xs">${result.template.folder}</p>
                        </div>
                    `;
                } else {
                    document.getElementById('wizard-template-recommendations').innerHTML = 
                        '<p class="text-yellow-500 text-sm">未找到匹配的模板推荐</p>';
                }
            } catch (error) {
                console.error('Error getting wizard template recommendations:', error);
                document.getElementById('wizard-template-recommendations').innerHTML = 
                    '<p class="text-red-500 text-sm">获取模板推荐失败</p>';
            }
        }

            async function completeWorkflowWizard() {
                await finalizeWorkflowRule();
            }

            async function finalizeWorkflowRule() {
                try {
                    const response = await fetch(`/api/workflow/${currentWorkflow.workflow_id}/finalize`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.message || 'Failed to finalize rule');
                    }

                    const result = await response.json();

                    // 显示成功结果
                    document.getElementById('rule-creation-result').classList.remove('hidden');

                    // 更新工作流状态
                    currentWorkflow = { ...currentWorkflow, ...result.data };
                    updateWorkflowSummary();

                    // 刷新主页面的规则列表
                    await loadSavedRules();

                } catch (error) {
                    console.error('Error finalizing rule:', error);
                    alert('创建规则失败: ' + error.message);
                }
        }

        function applyWizardToMainForm() {
                if (!currentWorkflow) {
                    alert('没有可应用的工作流数据');
                    return;
                }

                const summary = currentWorkflow.summary || {};
                const ruleInfo = summary.rule_info || {};
            
            // 应用到主表单
                document.getElementById('rule-name').value = ruleInfo.name || '';
                document.getElementById('rule-type').value = ruleInfo.rule_type || '';
                document.getElementById('patient-type').value = ruleInfo.patient_type || '';
                document.getElementById('match-method').value = ruleInfo.match_method || '';
                document.getElementById('database-type').value = ruleInfo.database_type || 'postgresql';

                // 应用生成的SQL
                if (currentWorkflow.generated_sql) {
                    setSqlContent(currentWorkflow.generated_sql);
                }
            
            closeWorkflowWizard();
            alert('向导设置已应用到主表单！');
        }

            // Additional workflow helper functions
            async function previewSqlInWizard() {
                const parameters = {};
                const paramInputs = document.querySelectorAll('#wizard-conditions-container input, #wizard-conditions-container select, #wizard-conditions-container textarea');

                paramInputs.forEach(input => {
                    const paramName = input.dataset.paramName;
                    if (paramName) {
                        parameters[paramName] = input.value;
                    }
                });

                const selectedTemplateId = document.querySelector('input[name="wizard-template"]:checked')?.value;
                const databaseType = document.getElementById('wizard-database-type')?.value || 'postgresql';

                if (!selectedTemplateId) {
                    alert('请先选择模板');
                    return;
                }

                try {
                    const response = await fetch('/api/workflow/preview-sql', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            template_id: selectedTemplateId,
                            parameters: parameters,
                            database_type: databaseType
                        }),
                    });

                    if (!response.ok) {
                        throw new Error('Failed to preview SQL');
                    }

                    const result = await response.json();

                    // 显示预览结果
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'mt-4 bg-gray-900 rounded-lg p-4';
                    previewDiv.innerHTML = `
                    <h5 class="text-white font-semibold mb-2">SQL预览</h5>
                    <pre class="language-sql"><code class="language-sql">${result.data.preview_sql}</code></pre>
                `;

                    // 移除之前的预览
                    const existingPreview = document.querySelector('.sql-preview-container');
                    if (existingPreview) {
                        existingPreview.remove();
                    }

                    previewDiv.classList.add('sql-preview-container');
                    document.getElementById('wizard-conditions-container').appendChild(previewDiv);

                    // 应用语法高亮
                    setTimeout(() => {
                        if (typeof hljs !== 'undefined') {
                            hljs.highlightElement(previewDiv.querySelector('code'));
                        }
                    }, 100);

                } catch (error) {
                    console.error('Error previewing SQL:', error);
                    alert('预览SQL失败: ' + error.message);
                }
            }

            function toggleWizardSqlEdit() {
                const preview = document.getElementById('wizard-sql-preview');
                const editor = document.getElementById('wizard-sql-editor');
                const toggleText = document.getElementById('wizard-edit-mode-text');

                if (preview.style.display === 'none') {
                    // 切换到预览模式
                    preview.style.display = 'block';
                    editor.style.display = 'none';
                    toggleText.textContent = '编辑';

                    // 更新预览内容
                    const code = document.getElementById('wizard-sql-output');
                    code.textContent = editor.value;
                    if (typeof hljs !== 'undefined') {
                        hljs.highlightElement(code);
                    }
                } else {
                    // 切换到编辑模式
                    preview.style.display = 'none';
                    editor.style.display = 'block';
                    toggleText.textContent = '预览';
                    editor.focus();
                }
            }

            function copyWizardSql() {
                const sqlContent = document.getElementById('wizard-sql-editor').value ||
                    document.getElementById('wizard-sql-output').textContent;

                navigator.clipboard.writeText(sqlContent).then(() => {
                    // 显示复制成功提示
                    const copyButton = event.target.closest('button');
                    const originalText = copyButton.innerHTML;
                    copyButton.innerHTML = '<i class="fas fa-check mr-2"></i> 已复制';
                    setTimeout(() => {
                        copyButton.innerHTML = originalText;
                    }, 2000);
                });
            }

            // 医保项目检索相关变量
            let currentSearchParamName = null;
            let selectedMedicalItems = new Set();

            // Search modal function for parameter input
            function openSearchModal(paramName) {
                console.log('openSearchModal 被调用，参数:', paramName);
                
                currentSearchParamName = paramName;
                selectedMedicalItems.clear();
                openModal('search-modal');
                
                // 同步主界面的数据库配置
                const mainDatabaseSelect = document.getElementById('databaseSelect');
                const mainHostInput = document.getElementById('hostInput');
                
                console.log('数据库选择器:', mainDatabaseSelect?.value);
                console.log('主机输入框:', mainHostInput?.value);
                
                // 更新医保搜索模态框中的连接信息
                updateMedicalConnectionInfo().catch(error => {
                    console.error('更新医保连接信息失败:', error);
                });
                
                // 加载Schema列表 - 同时调用两个函数确保schema加载
                console.log('开始调用 loadMedicalSchemas()');
                loadMedicalSchemas();
                
                console.log('开始调用 loadDatabaseSchemas()');
                try {
                    loadDatabaseSchemas(); // 调用主界面的schema加载函数
                    console.log('loadDatabaseSchemas() 调用成功');
                } catch (error) {
                    console.error('loadDatabaseSchemas() 调用失败:', error);
                }
                
                // 清空搜索结果
                document.getElementById('medical-search-results').innerHTML = '';
                document.getElementById('search-result-count').textContent = '共找到 0 个项目';
                document.getElementById('selected-medical-items').classList.add('hidden');
                
                console.log('openSearchModal 执行完成');
            }

            // 搜索医保项目
            async function searchMedicalInsurance() {
                const searchTerm = document.getElementById('medical-search-input').value.trim();
                if (!searchTerm) {
                    showToast('请输入搜索关键词', 'warning');
                    return;
                }

                const database = document.getElementById('databaseSelect').value;
                const schema = document.getElementById('medical-schema-select').value;
                
                // 支持多种分隔符的多项目检索
                const searchTerms = searchTerm.split(/[,，;；、|]+/).map(term => term.trim()).filter(term => term);
                console.log('解析的搜索词:', searchTerms);

                try {
                    // 首先尝试从配置文件加载数据库配置
                    const configResponse = await fetch('/api/database/config/load', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    let host = 'default';
                    
                    if (configResponse.ok) {
                        const configResult = await configResponse.json();
                        if (configResult.success) {
                            const config = configResult.config;
                            
                            // 根据数据库类型获取对应的配置
                            if (database === 'oracle' && config.oracle) {
                                host = config.oracle.host;
                            } else if (database === 'pg' && config.postgresql) {
                                host = config.postgresql.host;
                            }
                        }
                    }

                    // 如果配置加载失败，使用界面上的值作为备用
                    if (host === 'default') {
                        host = document.getElementById('hostInput').value || 'default';
                    }

                    showToast('正在搜索...', 'info');
                    
                    const response = await fetch('/api/medical-insurance/search', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            search_term: searchTerm, // 发送原始搜索词，后端会解析
                            database: database,
                            host: 'default', // 使用default让后端从配置文件加载
                            schema: schema,
                            limit: 50
                        })
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        displayMedicalSearchResults(result.data);
                        showToast(`找到 ${result.total} 个项目`, 'success');
                    } else {
                        // 处理业务逻辑错误（如表不存在）
                        if (response.status === 404 && result.error === '找不到医保对照表') {
                            showToast(result.message, 'warning');
                            displayMedicalSearchResults([]);
                        } else {
                            showToast('搜索失败: ' + (result.error || `HTTP ${response.status}`), 'error');
                        }
                    }

                } catch (error) {
                    console.error('搜索医保项目失败:', error);
                    showToast('搜索失败: ' + error.message, 'error');
                }
            }

            // 显示医保搜索结果
            function displayMedicalSearchResults(data) {
                const tbody = document.getElementById('medical-search-results');
                const countSpan = document.getElementById('search-result-count');
                
                tbody.innerHTML = '';
                countSpan.textContent = `共找到 ${data.length} 个项目`;

                if (data.length === 0) {
                    tbody.innerHTML = `
                        <tr class="border-b border-gray-700">
                            <td colspan="7" class="px-4 py-2 text-center text-gray-500">未找到匹配的项目</td>
                        </tr>
                    `;
                    return;
                }

                data.forEach((item, index) => {
                    const row = document.createElement('tr');
                    row.className = 'border-b border-gray-700 hover:bg-gray-700';
                    row.innerHTML = `
                        <td class="px-4 py-2">
                            <input type="checkbox" class="medical-item-checkbox" 
                                   data-index="${index}" onchange="toggleMedicalItemSelection(${index})">
                        </td>
                        <td class="px-4 py-2">${item.医保项目编码 || ''}</td>
                        <td class="px-4 py-2">${item.医保项目名称 || ''}</td>
                        <td class="px-4 py-2">${item.医院项目编码 || ''}</td>
                        <td class="px-4 py-2">${item.医院项目名称 || ''}</td>
                        <td class="px-4 py-2">${item.费用类别 || ''}</td>
                        <td class="px-4 py-2">
                            <button onclick="selectSingleMedicalItem(${index})" 
                                    class="text-blue-500 hover:text-blue-400">单选</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // 切换医保项目选择状态
            function toggleMedicalItemSelection(index) {
                const checkbox = document.querySelector(`[data-index="${index}"]`);
                if (checkbox.checked) {
                    selectedMedicalItems.add(index);
                } else {
                    selectedMedicalItems.delete(index);
                }
                updateSelectedItemsDisplay();
            }

            // 全选/取消全选
            function toggleSelectAllMedical() {
                const selectAllCheckbox = document.getElementById('select-all-medical');
                const checkboxes = document.querySelectorAll('.medical-item-checkbox');
                
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                    const index = parseInt(checkbox.getAttribute('data-index'));
                    if (selectAllCheckbox.checked) {
                        selectedMedicalItems.add(index);
                    } else {
                        selectedMedicalItems.delete(index);
                    }
                });
                
                updateSelectedItemsDisplay();
            }

            // 单选医保项目
            function selectSingleMedicalItem(index) {
                // 清空所有选择
                selectedMedicalItems.clear();
                document.querySelectorAll('.medical-item-checkbox').forEach(cb => cb.checked = false);
                document.getElementById('select-all-medical').checked = false;
                
                // 选择当前项目
                selectedMedicalItems.add(index);
                document.querySelector(`[data-index="${index}"]`).checked = true;
                
                updateSelectedItemsDisplay();
            }

            // 更新已选项目显示
            function updateSelectedItemsDisplay() {
                const selectedDiv = document.getElementById('selected-medical-items');
                const listDiv = document.getElementById('selected-items-list');
                
                if (selectedMedicalItems.size === 0) {
                    selectedDiv.classList.add('hidden');
                    return;
                }
                
                selectedDiv.classList.remove('hidden');
                
                const results = Array.from(document.querySelectorAll('#medical-search-results tr'));
                const selectedItems = [];
                
                selectedMedicalItems.forEach(index => {
                    if (results[index]) {
                        const cells = results[index].querySelectorAll('td');
                        if (cells.length >= 5) {
                            const code = cells[1].textContent.trim();
                            const name = cells[2].textContent.trim();
                            selectedItems.push(`${code} - ${name}`);
                        }
                    }
                });
                
                listDiv.innerHTML = selectedItems.map(item => 
                    `<div class="mb-1">• ${item}</div>`
                ).join('');
            }

            // 确认医保项目选择
            function confirmMedicalSelection() {
                console.log('confirmMedicalSelection 被调用');
                console.log('当前搜索参数名:', currentSearchParamName);
                console.log('选中的项目数量:', selectedMedicalItems.size);
                
                if (selectedMedicalItems.size === 0) {
                    showToast('请先选择项目', 'warning');
                    return;
                }

                const results = Array.from(document.querySelectorAll('#medical-search-results tr'));
                const selectedCodes = [];
                const selectedNames = [];
                
                selectedMedicalItems.forEach(index => {
                    if (results[index]) {
                        const cells = results[index].querySelectorAll('td');
                        if (cells.length >= 5) {
                            const code = cells[1].textContent.trim();
                            const name = cells[2].textContent.trim();
                            if (code) selectedCodes.push(code);
                            if (name) selectedNames.push(name);
                        }
                    }
                });

                console.log('选中的编码:', selectedCodes);
                console.log('选中的名称:', selectedNames);

                // 根据参数类型填充不同的值
                if (currentSearchParamName) {
                    // 尝试多种方式查找目标输入框
                    let input = document.querySelector(`input[data-param-name="${currentSearchParamName}"]`);
                    
                    // 如果找不到，尝试查找包含该参数名的div，然后找其中的input
                    if (!input) {
                        const container = document.querySelector(`div[data-param-name="${currentSearchParamName}"]`);
                        if (container) {
                            input = container.querySelector('input');
                        }
                    }
                    
                    // 如果还是找不到，尝试通过placeholder查找
                    if (!input) {
                        input = document.querySelector(`input[placeholder*="${currentSearchParamName}"]`);
                    }
                    
                    // 如果还是找不到，尝试通过name属性查找
                    if (!input) {
                        input = document.querySelector(`input[name="${currentSearchParamName}"]`);
                    }
                    
                    // 如果还是找不到，尝试通过id查找
                    if (!input) {
                        input = document.getElementById(currentSearchParamName);
                    }
                    
                    console.log('找到的目标输入框:', input);
                    console.log('输入框的data-param-name属性:', input?.getAttribute('data-param-name'));
                    console.log('输入框的placeholder属性:', input?.getAttribute('placeholder'));
                    console.log('输入框的当前值:', input?.value);
                    console.log('输入框的可见性:', input?.style.display, input?.style.visibility, input?.style.opacity);

                    if (input) {
                        let fillValue = '';
                        
                        // 智能判断参数类型并填充相应的值
                        if (currentSearchParamName.includes('编码') || currentSearchParamName.includes('代码')) {
                            // 医保项目编码类型
                            fillValue = selectedCodes.join(',');
                            console.log('填充编码类型，值:', fillValue);
                        } else if (currentSearchParamName.includes('名称') || currentSearchParamName.includes('名称')) {
                            // 医保项目名称类型
                            fillValue = selectedNames.join(',');
                            console.log('填充名称类型，值:', fillValue);
                        } else {
                            // 默认使用编码+名称的组合
                            const combined = selectedCodes.map((code, i) => 
                                `${code} - ${selectedNames[i] || ''}`
                            ).join(',');
                            fillValue = combined;
                            console.log('填充组合类型，值:', fillValue);
                        }
                        
                        // 设置输入框的值
                        input.value = fillValue;
                        
                        // 确保输入框可见和可编辑
                        input.style.display = 'block';
                        input.style.visibility = 'visible';
                        input.style.opacity = '1';
                        input.removeAttribute('readonly');
                        input.removeAttribute('disabled');
                        
                        // 触发多个事件以确保值更新
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                        input.dispatchEvent(new Event('blur', { bubbles: true }));
                        input.dispatchEvent(new Event('focus', { bubbles: true }));
                        
                        // 强制重新渲染
                        input.style.color = '#ffffff'; // 确保文本颜色可见
                        input.style.backgroundColor = '#374151'; // 确保背景色正确
                        
                        console.log('已填充值到输入框:', input.value);
                        console.log('填充后的输入框样式:', {
                            display: input.style.display,
                            visibility: input.style.visibility,
                            opacity: input.style.opacity,
                            color: input.style.color,
                            backgroundColor: input.style.backgroundColor,
                            readonly: input.readOnly,
                            disabled: input.disabled
                        });
                    } else {
                        console.error('未找到目标输入框，参数名:', currentSearchParamName);
                        showToast(`未找到参数 "${currentSearchParamName}" 对应的输入框`, 'error');
                        return;
                    }
                } else {
                    console.error('currentSearchParamName 为空');
                    showToast('无法确定目标参数', 'error');
                    return;
                }

                closeModal('search-modal');
                showToast(`已选择 ${selectedMedicalItems.size} 个项目并填充到 "${currentSearchParamName}"`, 'success');
            }

            // 加载医保检索的Schema列表
            async function loadMedicalSchemas() {
                const schemaSelect = document.getElementById('medical-schema-select');
                const database = document.getElementById('databaseSelect').value;

                try {
                    // 首先尝试从配置文件加载数据库配置
                    const configResponse = await fetch('/api/database/config/load', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    let host = 'default';
                    
                    if (configResponse.ok) {
                        const configResult = await configResponse.json();
                        if (configResult.success) {
                            const config = configResult.config;
                            
                            // 根据数据库类型获取对应的配置
                            if (database === 'oracle' && config.oracle) {
                                host = config.oracle.host;
                            } else if (database === 'pg' && config.postgresql) {
                                host = config.postgresql.host;
                            }
                        }
                    }

                    // 如果配置加载失败，使用界面上的值作为备用
                    if (host === 'default') {
                        host = document.getElementById('hostInput').value || 'default';
                    }

                    const response = await fetch('/api/database_schemas', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            database: database,
                            host: host
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.success) {
                        schemaSelect.innerHTML = '<option value="">选择Schema</option>';
                        result.schemas.forEach(schema => {
                            const option = document.createElement('option');
                            option.value = schema;
                            option.textContent = schema;
                            schemaSelect.appendChild(option);
                        });
                        console.log(`成功加载 ${result.schemas.length} 个Schema`);
                    } else {
                        showToast('加载Schema失败: ' + result.error, 'error');
                    }

                } catch (error) {
                    console.error('加载Schema失败:', error);
                    showToast('加载Schema失败: ' + error.message, 'error');
                }
            }

            // 添加医保搜索输入框的回车键事件监听
        document.addEventListener('DOMContentLoaded', function() {
                // 为医保搜索输入框添加回车键事件
                const searchInput = document.getElementById('medical-search-input');
                if (searchInput) {
                    searchInput.addEventListener('keypress', function(event) {
                        if (event.key === 'Enter') {
                            event.preventDefault();
                            searchMedicalInsurance();
                        }
                    });
                }
            });

            // Show all templates function
            async function showAllTemplates() {
                try {
                    const databaseType = document.getElementById('database-type').value;
                    const patientType = document.getElementById('patient-type').value;
                    const matchMethod = document.getElementById('match-method').value;

                    // Call the intelligent template filter API
                    const params = new URLSearchParams();
                    if (databaseType) params.append('database_type', databaseType);
                    if (patientType) params.append('patient_type', patientType);
                    if (matchMethod) params.append('match_method', matchMethod);

                    const response = await fetch(`/api/templates/filter?${params.toString()}`);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();

                    if (result.status === 'success' && result.data.templates) {
                        const templates = result.data.templates;

                        // Update template select options
                        const templateSelect = document.getElementById('rule-template');
                        templateSelect.innerHTML = '<option value="">选择一个模板</option>';

                        templates.forEach(template => {
                            const option = document.createElement('option');
                            option.value = template.id;
                            option.textContent = template.description;
                            templateSelect.appendChild(option);
                        });

                        // Update recommendations display
                        const recommendationsDiv = document.getElementById('template-recommendations');
                        if (templates.length > 0) {
                            recommendationsDiv.innerHTML = `
                            <div class="bg-blue-900 border border-blue-600 rounded-lg p-3">
                                <h4 class="text-blue-300 font-semibold mb-2">所有可用模板</h4>
                                <p class="text-blue-200 text-sm">找到 ${templates.length} 个匹配的模板，请从下拉列表中选择。</p>
                            </div>
                        `;
                        } else {
                            recommendationsDiv.innerHTML = `
                            <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-3">
                                <p class="text-yellow-200 text-sm">未找到匹配的模板，请调整筛选条件。</p>
                            </div>
                        `;
                        }
                    } else {
                        throw new Error('Failed to load templates');
                    }

                } catch (error) {
                    console.error('Error loading all templates:', error);
                    document.getElementById('template-recommendations').innerHTML =
                        '<p class="text-red-500 text-sm">加载模板失败: ' + error.message + '</p>';
                }
        }

        // --- Database Monitor Functions ---
        
        // 打开数据库监控配置模态框
        function openDatabaseMonitorConfig() {
            openModal('db-monitor-modal');
            loadDatabaseConnections();
            updateMonitorStatus();
        }
        
        // --- Rule Import Functions ---
        
        // 测试导入连接
        async function testImportConnection() {
            const host = document.getElementById('import-db-host').value;
            const port = document.getElementById('import-db-port').value;
            const database = document.getElementById('import-db-name').value;
            const username = document.getElementById('import-db-user').value;
            const password = document.getElementById('import-db-password').value;
            
            if (!host || !database || !username || !password) {
                showToast('请填写完整的数据库连接信息', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/database/test-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        host: host,
                        port: parseInt(port),
                        database: database,
                        username: username,
                        password: password
                    })
                });
                
                const result = await response.json();
                const testResult = document.getElementById('connection-test-result');
                const testMessage = document.getElementById('connection-test-message');
                
                testResult.classList.remove('hidden');
                
                if (result.success) {
                    testMessage.innerHTML = '<span class="text-green-400"><i class="fas fa-check mr-2"></i>连接测试成功</span>';
                    showToast('连接测试成功', 'success');
                } else {
                    testMessage.innerHTML = '<span class="text-red-400"><i class="fas fa-times mr-2"></i>连接测试失败: ' + result.error + '</span>';
                    showToast('连接测试失败: ' + result.error, 'error');
                }
            } catch (error) {
                const testResult = document.getElementById('connection-test-result');
                const testMessage = document.getElementById('connection-test-message');
                testResult.classList.remove('hidden');
                testMessage.innerHTML = '<span class="text-red-400"><i class="fas fa-times mr-2"></i>连接测试失败: ' + error.message + '</span>';
                showToast('连接测试失败: ' + error.message, 'error');
            }
        }
        
        // 开始导入规则
        async function startImportRules() {
            const host = document.getElementById('import-db-host').value;
            const port = document.getElementById('import-db-port').value;
            const database = document.getElementById('import-db-name').value;
            const username = document.getElementById('import-db-user').value;
            const password = document.getElementById('import-db-password').value;
            const overwrite = document.getElementById('import-overwrite').checked;
            const keepOriginal = document.getElementById('import-keep-original').checked;
            
            if (!host || !database || !username || !password) {
                showToast('请填写完整的数据库连接信息', 'warning');
                return;
            }
            
            // 显示进度区域
            document.getElementById('import-progress').classList.remove('hidden');
            document.getElementById('import-results').classList.add('hidden');
            
            try {
                // 第一步：获取可导入的规则
                document.getElementById('import-progress-text').textContent = '25%';
                document.getElementById('import-progress-bar').style.width = '25%';
                document.getElementById('import-status').textContent = '正在扫描可导入的规则...';
                
                const response = await fetch('/api/rules/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        host: host,
                        port: parseInt(port),
                        database: database,
                        username: username,
                        password: password
                    })
                });
                
                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '扫描规则失败');
                }
                
                if (result.count === 0) {
                    showToast('没有找到可导入的规则', 'info');
                    document.getElementById('import-progress').classList.add('hidden');
                    return;
                }
                
                // 更新进度
                document.getElementById('import-progress-text').textContent = '50%';
                document.getElementById('import-progress-bar').style.width = '50%';
                document.getElementById('import-status').textContent = `找到 ${result.count} 个可导入的规则，正在导入...`;
                
                // 第二步：执行导入
                const importResponse = await fetch('/api/rules/import/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        selected_rules: result.rules.map(rule => rule.id),
                        overwrite: overwrite,
                        keep_original: keepOriginal,
                        host: host,
                        port: parseInt(port),
                        database: database,
                        username: username,
                        password: password
                    })
                });
                
                const importResult = await importResponse.json();
                if (!importResult.success) {
                    throw new Error(importResult.error || '导入规则失败');
                }
                
                // 更新进度
                document.getElementById('import-progress-text').textContent = '100%';
                document.getElementById('import-progress-bar').style.width = '100%';
                document.getElementById('import-status').textContent = '导入完成';
                
                // 显示结果
                document.getElementById('import-results').classList.remove('hidden');
                
                let resultHtml = `
                    <div class="bg-green-900 border border-green-600 rounded-lg p-4">
                        <h4 class="text-green-300 font-semibold mb-2">导入成功</h4>
                        <p class="text-green-200 text-sm">成功导入 ${importResult.imported_count} 个规则</p>
                `;
                
                if (importResult.failed_count > 0) {
                    resultHtml += `
                        <p class="text-yellow-200 text-sm mt-2">失败 ${importResult.failed_count} 个规则</p>
                        <div class="mt-2 max-h-32 overflow-y-auto">
                            <p class="text-yellow-300 text-xs font-semibold">失败详情：</p>
                            <ul class="text-yellow-200 text-xs mt-1">
                    `;
                    importResult.failed_rules.forEach(rule => {
                        resultHtml += `<li class="mb-1">• ${rule}</li>`;
                    });
                    resultHtml += `
                            </ul>
                        </div>
                    `;
                }
                
                resultHtml += `
                        <div class="mt-3">
                            <button onclick="closeModal('import-rules-modal')" 
                                    class="bg-green-600 hover:bg-green-700 text-white text-sm px-3 py-1 rounded">
                                关闭
                            </button>
                        </div>
                    </div>
                `;
                
                document.getElementById('import-results-content').innerHTML = resultHtml;
                
                // 显示成功消息
                if (importResult.failed_count > 0) {
                    showToast(`导入完成：成功 ${importResult.imported_count} 个，失败 ${importResult.failed_count} 个`, 'warning');
                } else {
                    showToast(`成功导入 ${importResult.imported_count} 个规则`, 'success');
                }
                
                // 刷新规则列表
                setTimeout(() => {
                    loadRules();
                }, 1000);
                
            } catch (error) {
                console.error('导入规则失败:', error);
                showToast('导入规则失败: ' + error.message, 'error');
                
                // 显示错误结果
                document.getElementById('import-results').classList.remove('hidden');
                document.getElementById('import-results-content').innerHTML = `
                    <div class="bg-red-900 border border-red-600 rounded-lg p-4">
                        <h4 class="text-red-300 font-semibold mb-2">导入失败</h4>
                        <p class="text-red-200 text-sm">${error.message}</p>
                        <div class="mt-3">
                            <button onclick="closeModal('import-rules-modal')" 
                                    class="bg-red-600 hover:bg-red-700 text-white text-sm px-3 py-1 rounded">
                                关闭
                            </button>
                        </div>
                    </div>
                `;
            }
        }
        
        // 初始化导入模态框
        function initializeImportModal() {
            // 重置步骤
            document.getElementById('step-1').classList.remove('hidden');
            document.getElementById('step-2').classList.add('hidden');
            document.getElementById('import-progress').classList.add('hidden');
            document.getElementById('import-results').classList.add('hidden');
            
            // 重置步骤指示器
            document.getElementById('step-1-indicator').classList.remove('bg-green-600');
            document.getElementById('step-1-indicator').classList.add('bg-blue-600');
            document.getElementById('step-2-indicator').classList.remove('bg-blue-600');
            document.getElementById('step-2-indicator').classList.add('bg-gray-600');
            
            // 加载保存的连接
            loadSavedConnections();
            
            // 添加连接方式切换事件监听器
            document.querySelectorAll('input[name="connection-method"]').forEach(radio => {
                radio.addEventListener('change', handleConnectionMethodChange);
            });
            
            // 初始化连接方式
            handleConnectionMethodChange();
        }
        
        // 加载保存的数据库连接
        async function loadSavedConnections() {
            try {
                console.log('开始加载保存的连接...');
                const response = await fetch('/api/database/connections');
                const data = await response.json();
                
                console.log('API响应:', data);
                
                const select = document.getElementById('saved-connection-select');
                if (!select) {
                    console.error('找不到saved-connection-select元素');
                    return;
                }
                
                select.innerHTML = '<option value="">选择保存的连接</option>';
                
                if (data.success && data.connections) {
                    console.log(`找到 ${data.connections.length} 个连接`);
                    data.connections.forEach(connection => {
                        const option = document.createElement('option');
                        option.value = connection.id;
                        option.textContent = `${connection.name} (${connection.host}:${connection.port})`;
                        select.appendChild(option);
                        console.log(`添加连接选项: ${connection.name} (${connection.host}:${connection.port})`);
                    });
                } else {
                    console.log('没有找到连接或API调用失败:', data.error);
                }
            } catch (error) {
                console.error('加载保存的连接失败:', error);
            }
        }
        
        // 处理连接方式切换
        function handleConnectionMethodChange() {
            const method = document.querySelector('input[name="connection-method"]:checked').value;
            const savedSection = document.getElementById('saved-connections-section');
            const manualSection = document.getElementById('manual-connection-section');
            
            if (method === 'saved') {
                savedSection.classList.remove('hidden');
                manualSection.classList.add('hidden');
                loadSavedConnections();
            } else {
                savedSection.classList.add('hidden');
                manualSection.classList.remove('hidden');
            }
        }
        
        // 打开数据库连接管理页面
        function openDatabaseConnectionsPage() {
            window.open('/page/database_connections', '_blank');
        }
        
        // 扫描可导入规则
        async function scanImportableRules() {
            const method = document.querySelector('input[name="connection-method"]:checked').value;
            let connectionData = null;
            let connectionId = null;
            
            if (method === 'saved') {
                connectionId = document.getElementById('saved-connection-select').value;
                if (!connectionId) {
                    showToast('请选择一个保存的连接', 'warning');
                    return;
                }
            } else {
                const host = document.getElementById('import-db-host').value;
                const port = document.getElementById('import-db-port').value;
                const database = document.getElementById('import-db-name').value;
                const username = document.getElementById('import-db-user').value;
                const password = document.getElementById('import-db-password').value;
                
                if (!host || !database || !username || !password) {
                    showToast('请填写完整的数据库连接信息', 'warning');
                    return;
                }
                
                connectionData = {
                    host: host,
                    port: parseInt(port),
                    database: database,
                    username: username,
                    password: password
                };
            }
            
            try {
                const response = await fetch('/api/rules/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        connection_id: connectionId,
                        connection_data: connectionData
                    })
                });
                
                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '扫描可导入规则失败');
                }
                
                // 保存当前导入数据
                currentImportData = {
                    connection_id: connectionId,
                    connection_data: connectionData,
                    connection_info: result.connection_info
                };
                availableRules = result.rules;
                
                // 切换到第二步
                goToStep2();
                
            } catch (error) {
                console.error('扫描可导入规则失败:', error);
                showToast('扫描可导入规则失败: ' + error.message, 'error');
            }
        }
        
        // 切换到第二步
        function goToStep2() {
            // 更新步骤指示器
            document.getElementById('step-1-indicator').classList.remove('bg-blue-600');
            document.getElementById('step-1-indicator').classList.add('bg-green-600');
            document.getElementById('step-2-indicator').classList.remove('bg-gray-600');
            document.getElementById('step-2-indicator').classList.add('bg-blue-600');
            
            // 显示连接信息
            if (currentImportData.connection_info) {
                document.getElementById('connection-name-display').textContent = currentImportData.connection_info.name || '手动连接';
                document.getElementById('connection-details-display').textContent = 
                    ` (${currentImportData.connection_info.host}:${currentImportData.connection_info.port}/${currentImportData.connection_info.database})`;
            }
            
            // 渲染规则列表
            renderRulesList();
            
            // 切换显示
            document.getElementById('step-1').classList.add('hidden');
            document.getElementById('step-2').classList.remove('hidden');
        }
        
        // 返回第一步
        function backToStep1() {
            // 重置步骤指示器
            document.getElementById('step-1-indicator').classList.remove('bg-green-600');
            document.getElementById('step-1-indicator').classList.add('bg-blue-600');
            document.getElementById('step-2-indicator').classList.remove('bg-blue-600');
            document.getElementById('step-2-indicator').classList.add('bg-gray-600');
            
            // 切换显示
            document.getElementById('step-2').classList.add('hidden');
            document.getElementById('step-1').classList.remove('hidden');
            
            // 清空数据
            currentImportData = null;
            availableRules = [];
        }
        
        // 渲染规则列表
        function renderRulesList() {
            const container = document.getElementById('rules-list');
            
            if (availableRules.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-gray-400 py-8">
                        <i class="fas fa-info-circle text-2xl mb-2"></i>
                        <p>没有找到可导入的规则</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = availableRules.map(rule => `
                <div class="border border-gray-600 rounded-lg p-3 mb-3 hover:bg-gray-600 transition-colors">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <input type="checkbox" id="rule-${rule.id}" value="${rule.id}" class="mr-3 rule-checkbox">
                                <label for="rule-${rule.id}" class="font-medium text-white cursor-pointer">${rule.rule_name}</label>
                            </div>
                            <div class="text-sm text-gray-400 ml-6">
                                <p><strong>ID:</strong> ${rule.id}</p>
                                <p><strong>规则描述:</strong> ${rule.rule_content || rule.rule_intension || '无描述'}</p>
                                <p><strong>政策依据:</strong> ${rule.policy_basis || '无描述'}</p>
                                <p><strong>创建时间:</strong> ${rule.created_at || rule.create_time || '未知'}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // 全选规则
        function selectAllRules() {
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }
        
        // 取消全选规则
        function deselectAllRules() {
            const checkboxes = document.querySelectorAll('.rule-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }
        
        // 执行规则导入
        async function executeRuleImport() {
            const selectedRules = Array.from(document.querySelectorAll('.rule-checkbox:checked')).map(cb => cb.value);
            const overwrite = document.getElementById('import-overwrite').checked;
            
            if (selectedRules.length === 0) {
                showToast('请选择要导入的规则', 'warning');
                return;
            }
            
            if (!currentImportData) {
                showToast('导入数据无效，请重新开始', 'error');
                return;
            }
            
            // 显示进度区域
            document.getElementById('import-progress').classList.remove('hidden');
            document.getElementById('step-2').classList.add('hidden');
            
            try {
                document.getElementById('import-progress-text').textContent = '50%';
                document.getElementById('import-progress-bar').style.width = '50%';
                document.getElementById('import-status').textContent = '正在导入选定的规则...';
                
                const response = await fetch('/api/rules/import/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        selected_rules: selectedRules,
                        overwrite: overwrite,
                        connection_id: currentImportData.connection_id,
                        connection_data: currentImportData.connection_data
                    })
                });
                
                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.error || '导入规则失败');
                }
                
                // 完成
                document.getElementById('import-progress-text').textContent = '100%';
                document.getElementById('import-progress-bar').style.width = '100%';
                document.getElementById('import-status').textContent = '导入完成';
                
                // 显示结果
                document.getElementById('import-results').classList.remove('hidden');
                let resultHtml = `
                    <div class="text-green-400 mb-4">
                        <i class="fas fa-check-circle mr-2"></i>
                        <p class="text-green-200 text-sm">成功导入 ${result.imported_count} 个规则</p>
                    </div>
                `;
                
                if (result.failed_count > 0) {
                    resultHtml += `
                        <div class="text-yellow-400 mb-4">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <p class="text-yellow-200 text-sm mt-2">失败 ${result.failed_count} 个规则</p>
                        </div>
                    `;
                    
                    if (result.failed_rules && result.failed_rules.length > 0) {
                        resultHtml += '<div class="mt-4"><h4 class="text-sm font-semibold text-gray-300 mb-2">失败详情：</h4><ul class="text-sm text-gray-400 space-y-1">';
                        result.failed_rules.forEach(rule => {
                            resultHtml += `<li>• ${rule}</li>`;
                        });
                        resultHtml += '</ul></div>';
                    }
                }
                
                document.getElementById('import-results-content').innerHTML = resultHtml;
                
                // 显示通知
                if (result.failed_count > 0) {
                    showToast(`导入完成：成功 ${result.imported_count} 个，失败 ${result.failed_count} 个`, 'warning');
                } else {
                    showToast(`成功导入 ${result.imported_count} 个规则`, 'success');
                }
                
                // 刷新规则列表
                // loadRules(); // 暂时注释掉，避免未定义错误
                
            } catch (error) {
                console.error('导入规则失败:', error);
                document.getElementById('import-progress').classList.add('hidden');
                document.getElementById('import-results').classList.remove('hidden');
                document.getElementById('import-results-content').innerHTML = `
                    <div class="text-red-400">
                        <i class="fas fa-times-circle mr-2"></i>
                        <p class="text-red-200">导入失败: ${error.message}</p>
                    </div>
                `;
                showToast('导入规则失败: ' + error.message, 'error');
            }
        }
        
        // 全局变量
        let currentImportData = null;
        let availableRules = [];
        
        // 简单的规则列表刷新函数
        function loadRules() {
            // TODO: 实现规则列表刷新逻辑
            console.log('规则列表刷新功能待实现');
        }
        
        // 加载数据库连接配置
        function loadDatabaseConnections() {
            const connections = JSON.parse(localStorage.getItem('databaseConnections') || '[]');
            databaseConnections = connections;
            renderDatabaseConnections();
        }
        
        // 保存数据库连接配置
        function saveDatabaseConnections() {
            localStorage.setItem('databaseConnections', JSON.stringify(databaseConnections));
        }
        
        // 渲染数据库连接列表
        function renderDatabaseConnections() {
            const container = document.getElementById('db-connections-list');
            if (databaseConnections.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-sm">暂无数据库连接配置</p>';
                return;
            }
            
            container.innerHTML = databaseConnections.map((conn, index) => `
                <div class="bg-gray-700 border border-gray-600 rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <h4 class="text-white font-semibold">${conn.name}</h4>
                                <span class="ml-2 px-2 py-1 bg-blue-600 text-white text-xs rounded">${conn.host}:${conn.port}</span>
                            </div>
                            <div class="text-sm text-gray-300">
                                <p>数据库: ${conn.database}</p>
                                <p>用户: ${conn.username}</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="testDatabaseConnection(${index})" 
                                    class="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1 rounded">
                                <i class="fas fa-plug mr-1"></i>测试
                            </button>
                            <button onclick="editDatabaseConnection(${index})" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1 rounded">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button onclick="deleteDatabaseConnection(${index})" 
                                    class="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // 添加数据库连接
        function addDatabaseConnection() {
            const name = prompt('请输入连接名称:');
            if (!name) return;
            
            const host = prompt('请输入数据库主机地址:');
            if (!host) return;
            
            const port = prompt('请输入端口号 (默认5432):', '5432');
            if (!port) return;
            
            const database = prompt('请输入数据库名称:');
            if (!database) return;
            
            const username = prompt('请输入用户名:');
            if (!username) return;
            
            const password = prompt('请输入密码:');
            if (!password) return;
            
            const connection = {
                name,
                host,
                port: parseInt(port),
                database,
                username,
                password
            };
            
            databaseConnections.push(connection);
            saveDatabaseConnections();
            renderDatabaseConnections();
            showToast('数据库连接已添加', 'success');
        }
        
        // 编辑数据库连接
        function editDatabaseConnection(index) {
            const conn = databaseConnections[index];
            const name = prompt('连接名称:', conn.name);
            if (!name) return;
            
            const host = prompt('主机地址:', conn.host);
            if (!host) return;
            
            const port = prompt('端口号:', conn.port.toString());
            if (!port) return;
            
            const database = prompt('数据库名称:', conn.database);
            if (!database) return;
            
            const username = prompt('用户名:', conn.username);
            if (!username) return;
            
            const password = prompt('密码:', conn.password);
            if (!password) return;
            
            databaseConnections[index] = {
                name,
                host,
                port: parseInt(port),
                database,
                username,
                password
            };
            
            saveDatabaseConnections();
            renderDatabaseConnections();
            showToast('数据库连接已更新', 'success');
        }
        
        // 删除数据库连接
        function deleteDatabaseConnection(index) {
            if (confirm('确定要删除这个数据库连接吗？')) {
                databaseConnections.splice(index, 1);
                saveDatabaseConnections();
                renderDatabaseConnections();
                showToast('数据库连接已删除', 'success');
            }
        }
        
        // 测试数据库连接
        async function testDatabaseConnection(index) {
            const conn = databaseConnections[index];
            try {
                const response = await fetch('/api/database/test-connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(conn)
                });
                
                const result = await response.json();
                if (result.success) {
                    showToast(`连接测试成功: ${conn.name}`, 'success');
                } else {
                    showToast(`连接测试失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showToast(`连接测试失败: ${error.message}`, 'error');
            }
        }
        
        // 启动数据库监控
        function startDatabaseMonitor() {
            if (databaseConnections.length === 0) {
                showToast('请先添加数据库连接配置', 'warning');
                return;
            }
            
            const interval = parseInt(document.getElementById('monitor-interval').value) * 1000;
            isMonitoring = true;
            
            // 立即执行一次检查
            checkDatabaseRequirements();
            
            // 设置定时器
            monitorInterval = setInterval(checkDatabaseRequirements, interval);
            
            updateMonitorStatus();
            showToast('数据库监控已启动', 'success');
        }
        
        // 停止数据库监控
        function stopDatabaseMonitor() {
            isMonitoring = false;
            if (monitorInterval) {
                clearInterval(monitorInterval);
                monitorInterval = null;
            }
            updateMonitorStatus();
            showToast('数据库监控已停止', 'info');
        }
        
        // 更新监控状态显示
        function updateMonitorStatus() {
            const indicator = document.getElementById('monitor-status-indicator');
            const statusText = document.getElementById('monitor-status-text');
            const startBtn = document.getElementById('start-monitor-btn');
            const stopBtn = document.getElementById('stop-monitor-btn');
            
            if (isMonitoring) {
                indicator.className = 'w-3 h-3 bg-green-500 rounded-full mr-3';
                statusText.textContent = '监控运行中';
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                indicator.className = 'w-3 h-3 bg-red-500 rounded-full mr-3';
                statusText.textContent = '监控已停止';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }
        
        // 检查数据库需求
        async function checkDatabaseRequirements() {
            for (const conn of databaseConnections) {
                try {
                    const response = await fetch('/api/database/check-requirements', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(conn)
                    });
                    
                    const result = await response.json();
                    if (result.success && result.newRequirements.length > 0) {
                        handleNewRequirements(conn, result.newRequirements);
                    }
                } catch (error) {
                    console.error(`检查数据库 ${conn.name} 时出错:`, error);
                }
            }
        }
        
        // 处理新的需求
        function handleNewRequirements(connection, requirements) {
            const alert = {
                id: Date.now(),
                timestamp: new Date().toLocaleString(),
                database: connection.name,
                requirements: requirements,
                url: `http://${connection.host}/dataAnalyse/synchronousRule`
            };
            
            recentAlerts.unshift(alert);
            if (recentAlerts.length > 10) {
                recentAlerts = recentAlerts.slice(0, 10);
            }
            
            // 播放声音提醒
            if (document.getElementById('sound-notification').checked) {
                playNotificationSound();
            }
            
            // 显示弹窗提醒
            if (document.getElementById('popup-notification').checked) {
                showNotificationPopup(alert);
            }
            
            // 更新提醒列表
            updateRecentAlerts();
            
            showToast(`发现 ${requirements.length} 条新规则需求`, 'info');
        }
        
        // 播放通知声音
        function playNotificationSound() {
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.play();
            } catch (error) {
                console.error('播放声音失败:', error);
            }
        }
        
        // 显示通知弹窗
        function showNotificationPopup(alert) {
            const popup = document.createElement('div');
            popup.className = 'fixed top-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm';
            popup.innerHTML = `
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-semibold mb-2">新规则需求提醒</h4>
                        <p class="text-sm mb-2">数据库: ${alert.database}</p>
                        <p class="text-sm mb-3">发现 ${alert.requirements.length} 条新规则需求</p>
                        <div class="flex space-x-2">
                            <button onclick="openDatabaseUrl('${alert.url}')" 
                                    class="bg-white text-blue-600 px-3 py-1 rounded text-sm">
                                打开链接
                            </button>
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                                    class="bg-gray-600 text-white px-3 py-1 rounded text-sm">
                                关闭
                            </button>
                        </div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="text-white ml-2">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(popup);
            
            // 5秒后自动移除
            setTimeout(() => {
                if (popup.parentElement) {
                    popup.remove();
                }
            }, 5000);
        }
        
        // 打开数据库URL
        function openDatabaseUrl(url) {
            window.open(url, '_blank');
        }
        
        // 更新最近提醒列表
        function updateRecentAlerts() {
            const container = document.getElementById('recent-alerts');
            if (recentAlerts.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-sm">暂无提醒记录</p>';
                return;
            }
            
            container.innerHTML = recentAlerts.map(alert => `
                <div class="bg-gray-700 border border-gray-600 rounded-lg p-3">
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="text-white text-sm font-semibold">${alert.database}</p>
                            <p class="text-gray-300 text-xs">${alert.timestamp}</p>
                            <p class="text-gray-300 text-xs">${alert.requirements.length} 条新需求</p>
                        </div>
                        <button onclick="openDatabaseUrl('${alert.url}')" 
                                class="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 rounded">
                            打开
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // --- Event Listeners ---
            document.addEventListener('DOMContentLoaded', function () {
            // Initialize the application
            fetchSqlTemplates();
            loadSavedRules();
            loadRuleAttributeOptions();
            
            // 确保政策依据字段为空
            document.getElementById('rule-policy-basis').value = '';
            
            // 初始化数据库配置
            onDatabaseChange();
            
            // 加载保存的数据库配置
            loadDatabaseConfig();
            
            // 更新连接状态
            updateConnectionStatus();

            // Bind event listeners
            document.getElementById('toggle-edit-mode').addEventListener('click', toggleEditMode);
            document.getElementById('save-rule-btn').addEventListener('click', saveRule);
            document.getElementById('get-recommendations-btn').addEventListener('click', getTemplateRecommendations);
            document.getElementById('db-monitor-config-btn').addEventListener('click', openDatabaseMonitorConfig);
            document.getElementById('import-rules-btn').addEventListener('click', function () {
                openModal('import-rules-modal');
                initializeImportModal();
            });
            
            // 添加连接管理按钮事件监听器
            document.getElementById('manage-connections-btn').addEventListener('click', function () {
                window.open('/page/database_connections', '_blank');
            });
                document.getElementById('start-wizard-btn').addEventListener('click', function () {
                openModal('workflow-wizard-modal');
                initializeWorkflowWizard();
            });
            
            // 添加属性变化时的模板更新监听器
            document.getElementById('database-type').addEventListener('change', onTemplateSelectionChange);
            document.getElementById('match-method').addEventListener('change', onTemplateSelectionChange);
            document.getElementById('patient-type').addEventListener('change', onTemplateSelectionChange);
            
            // 添加数据库配置变化时的状态更新监听器
            document.getElementById('databaseSelect').addEventListener('change', updateConnectionStatus);
            document.getElementById('hostInput').addEventListener('input', updateConnectionStatus);
            
            // New rule button
                document.getElementById('new-rule-btn').addEventListener('click', function () {
                // 清空所有表单字段
                document.getElementById('rule-name').value = '';
                document.getElementById('rule-type').value = '';
                document.getElementById('patient-type').value = '';
                document.getElementById('match-method').value = '';
                document.getElementById('database-type').value = 'postgresql';
                document.getElementById('rule-template').value = '';
                document.getElementById('rule-description').value = '';
                document.getElementById('rule-policy-basis').value = '';
                
                // 重置SQL内容
                setSqlContent('-- 在左侧表单中填写信息以生成SQL...');
                
                // 清空条件容器和推荐
                document.getElementById('conditions-container').innerHTML = '<p class="text-gray-500">请先选择一个SQL模板</p>';
                document.getElementById('template-recommendations').innerHTML = '<p class="text-gray-500 text-sm">点击"获取推荐"按钮获取智能模板推荐</p>';
                
                // 重置编辑状态
                resetEditingState();
                
                // 如果在编辑模式，切换回预览模式
                if (isEditMode) {
                    toggleEditMode();
                }

                    showToast('已清空表单，可以开始创建新规则', 'success');
            });

                            // Copy SQL button (original)
                document.getElementById('copy-sql').addEventListener('click', function () {
                const sqlContent = getCurrentSqlContent();
                navigator.clipboard.writeText(sqlContent).then(() => {
                        showToast('SQL已复制到剪贴板', 'success');
                    }).catch(() => {
                        showToast('复制失败，请手动复制', 'error');
                });
            });

                // Copy SQL button (new)
                document.getElementById('copy-sql-btn').addEventListener('click', function () {
                const sqlContent = getCurrentSqlContent();
                navigator.clipboard.writeText(sqlContent).then(() => {
                        showToast('SQL已复制到剪贴板', 'success');
                    }).catch(() => {
                        showToast('复制失败，请手动复制', 'error');
                });
            });

                // Test connection button
                document.getElementById('test-connection-btn').addEventListener('click', testConnection);
                
                // Load config button
                document.getElementById('load-config-btn').addEventListener('click', loadDatabaseConfig);
                
                // Save config button
                document.getElementById('save-config-btn').addEventListener('click', saveDatabaseConfig);
                
                // Validate SQL button
                document.getElementById('validate-sql-btn').addEventListener('click', validateSQL);
                
                // Execute SQL button
                document.getElementById('execute-sql-btn').addEventListener('click', executeSQL);
                
                // Generate SQL button
                document.getElementById('generate-sql-btn').addEventListener('click', generateSqlFromConditions);
                
                // Test Parameters button
                document.getElementById('test-params-btn').addEventListener('click', testParameters);
        });

    </script>
</body>

</html>