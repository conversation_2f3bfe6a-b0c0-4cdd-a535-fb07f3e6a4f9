#!/usr/bin/env python3
"""
测试规则导入修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.rule_import_service import RuleImportService

def test_import_service():
    """测试导入服务的参数处理"""
    print("=== 测试导入服务参数处理 ===")
    
    service = RuleImportService()
    
    # 测试不同类型的规则ID
    test_cases = [
        [1, 2, 3],  # 整数ID
        ["1", "2", "3"],  # 字符串ID
        ["abc123", "def456"],  # UUID类型ID
        [1, "2", "abc123"],  # 混合类型ID
    ]
    
    for i, rule_ids in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {rule_ids}")
        print(f"规则ID类型: {[type(rid) for rid in rule_ids]}")
        
        # 模拟调用import_rules方法（不实际执行）
        try:
            # 这里只是测试参数接收，不实际执行数据库操作
            print(f"参数接收成功: selected_rule_ids={rule_ids}")
        except Exception as e:
            print(f"参数接收失败: {e}")

def test_frontend_data_processing():
    """测试前端数据处理"""
    print("\n=== 测试前端数据处理 ===")
    
    # 模拟前端收集的数据
    frontend_cases = [
        # 整数ID
        {
            "selected_rules": [1, 2, 3],
            "overwrite": False
        },
        # 字符串ID
        {
            "selected_rules": ["1", "2", "3"],
            "overwrite": False
        },
        # UUID类型ID
        {
            "selected_rules": ["abc123-def456-ghi789", "xyz987-uvw654-rst321"],
            "overwrite": False
        }
    ]
    
    for i, case in enumerate(frontend_cases):
        print(f"\n前端数据用例 {i+1}:")
        print(f"selected_rules: {case['selected_rules']}")
        print(f"selected_rules类型: {[type(rid) for rid in case['selected_rules']]}")
        
        # 模拟后端处理
        selected_rules = case['selected_rules']
        if not isinstance(selected_rules, list):
            selected_rules = [selected_rules] if selected_rules else []
        
        print(f"处理后: {selected_rules}")
        print(f"处理后类型: {[type(rid) for rid in selected_rules]}")

if __name__ == "__main__":
    test_import_service()
    test_frontend_data_processing() 