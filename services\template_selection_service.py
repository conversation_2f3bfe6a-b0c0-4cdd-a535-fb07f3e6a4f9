"""
Template selection service for intelligent template selection and parameter processing.
"""
from typing import Dict, Any, Optional, Tuple, List
import re
from services.rule_template_selector import RuleTemplateSelector


class TemplateSelectionService:
    """Service for intelligent template selection and parameter processing."""
    
    def __init__(self):
        self.template_selector = RuleTemplateSelector()
    
    def select_template(self, rule: Dict[str, Any], templates: Dict[str, str]) -> Tu<PERSON>[Optional[str], Optional[str]]:
        """根据规则选择对应的 SQL 模板，返回(模板内容, 模板名称)"""
        try:
            behavior_type = rule.get("行为认定", "")
            rule_type = rule.get("类型", "")
            time_type = rule.get("时间类型", "")
            
            print(f"选择模板 - 行为认定: {behavior_type}, 规则类型: {rule_type}, 时间类型: {time_type}")
            
            # 1. 优先级最高的条件判断
            if rule_type == "全量病例":
                return templates.get("病例提取-根据项目提取"), "病例提取-根据项目提取"
                
            # 重复收费根据时间类型选择不同模板
            if rule_type == "重复收费":
                if time_type == "日":
                    return templates.get("重复收费"), "重复收费"
                elif time_type == "小时":
                    return templates.get("重复收费_时"), "重复收费_时"
                elif time_type == "分钟":
                    return templates.get("重复收费_分"), "重复收费_分"
                elif time_type == "周":
                    return templates.get("重复收费_周"), "重复收费_周"
                elif time_type == "月":
                    return templates.get("重复收费_月"), "重复收费_月"
                elif time_type == "年":
                    return templates.get("重复收费_年"), "重复收费_年"
                elif time_type == "住院期间":
                    return templates.get("重复收费_住院期间"), "重复收费_住院期间"
                elif time_type == "审查":
                    return templates.get("重复收费_审查"), "重复收费_审查"
                else:
                    # 默认使用普通重复收费模板
                    return templates.get("重复收费"), "重复收费"
                    
            if rule_type == "住院天数":
                return templates.get("超住院天数"), "超住院天数"    
            if rule_type == "合超住院天数":
                return templates.get("合超住院天数"), "合超住院天数"          
            # 2. 需要组合条件判断的情况（行为认定+类型）
            if rule_type == "频次上限":
                return templates.get("住院期间超频次"), "住院期间超频次"
            if rule_type == "天数上限":
                return templates.get("住院期间超天数"), "住院期间超天数"
            if rule_type == "组套收费":
                return templates.get("组套收费"), "组套收费"        
            if rule_type == "小时":
                return templates.get("超每日数量"), "超每日数量"
            if rule_type == "日":
                return templates.get("超每日数量"), "超每日数量"
            if rule_type == "周":
                return templates.get("超每周数量"), "超每周数量"
            if rule_type == "两周":
                return templates.get("超两周数量"), "超两周数量"
            if rule_type == "月":
                return templates.get("超每月数量"), "超每月数量"        
            if rule_type == "年":
                return templates.get("超每年数量"), "超每年数量"
            if rule_type == "金额":
                return templates.get("超每日金额"), "超每日金额"
            if rule_type == "年龄":
                return templates.get("限年龄"), "限年龄"
            if rule_type == "性别":
                return templates.get("限性别"), "限性别"
            # 如果没有找到对应模板，使用通用模板
            template = templates.get("通用模板")
            if template:
                print(f"使用通用模板替代: {rule_type}/{behavior_type}")
                return template, "通用模板"
            return None, None
            
        except Exception as e:
            print(f"选择SQL模板失败: {str(e)}")
            return None, None

    def generate_rule_sql(self, rule: Dict[str, Any], template_paths: List[str]) -> Tuple[Optional[str], Optional[str]]:
        """根据规则生成 SQL 查询语句，返回(SQL语句, 模板名称)"""
        try:
            # 加载所有模板
            templates = self.load_sql_templates(template_paths)
            if not templates:
                print(template_paths + "未找到任何SQL模板文件")
                return None, None
            template_content, template_name = self.select_template(rule, templates)
            if not template_content:
                print(template_paths + "未找到匹配的模板：规则类型=" + rule.get('类型', '') + ", "
                    f"行为认定=" + rule.get('行为认定', ''))
                return None, None
            # 直接根据rule中的visit_type判断是否为门诊
            visit_type = rule.get('visit_type') or rule.get('就诊类型')
            is_outpatient = visit_type == '门诊'
            query = self.render_template_sql(template_content, rule, is_outpatient)
            return query, template_name
        except Exception as e:
            print(f"生成规则SQL失败: {str(e)}")
            return None, None

    def load_sql_templates(self, template_paths: List[str]) -> Dict[str, str]:
        """加载SQL模板文件"""
        templates = {}
        for path in template_paths:
            try:
                # 这里需要根据实际的模板加载逻辑来实现
                # 暂时返回空字典，实际实现时需要加载模板文件
                pass
            except Exception as e:
                print(f"加载模板文件失败 {path}: {str(e)}")
        return templates

    def split_and_join_values(self, value: str) -> str:
        """将逗号分隔的字符串拆分并转换为SQL查询格式"""
        if not value or not isinstance(value, str):
            return "''"
        
        # 检查是否包含%
        if '%' in value:
            values = [val.strip() for val in value.split(',') if val.strip()]
            like_conditions = [f"B.医保项目名称 LIKE '{val}'" for val in values]
            return ' OR '.join(like_conditions)
        
        # 不包含%的情况，使用原来的逻辑
        values = [f"'{val.strip()}'" for val in value.split(',') if val.strip()]
        return ','.join(values) if values else "''"

    def build_ilike_any_array(self, keywords: str) -> str:
        """
        将字符串转换为SQL ILIKE ANY数组格式
        例：'癌|肿瘤,卵巢，前列腺' -> "ARRAY['%癌%','%肿瘤%','%卵巢%','%前列腺%']"
        """
        if not keywords:
            return "ARRAY[]::text[]"
        # 替换所有分隔符为统一的|
        for sep in ['，', ',', '|']:
            keywords = keywords.replace(sep, '|')
        # 分割并去除空白
        items = [kw.strip() for kw in keywords.split('|') if kw.strip()]
        if not items:
            return "ARRAY[]::text[]"
        # 拼接成SQL数组
        array_str = ",".join([f"'%{kw}%'" for kw in items])
        return f"ARRAY[{array_str}]"

    def render_template_sql(self, template: str, rule: Dict[str, Any], is_outpatient: bool = False) -> str:
        """根据规则动态替换 SQL 模板中的占位符"""
        try:
            # 处理医保项目名称
            medical_items = []
            for i in range(1, 5):  # 支持4个医保项目
                item_name = rule.get(f"医保名称{i}")
                if item_name and isinstance(item_name, str):
                    medical_items.extend(item_name.split(','))
            
            medical_items = [f"'{item.strip()}'" for item in medical_items if item.strip()]
            medical_items_str = ','.join(medical_items) if medical_items else "''"
            
            # 处理排除诊断
            exclude_diag = rule.get("排除诊断", "")
            exclude_diag_array = self.build_ilike_any_array(exclude_diag)
            
            # 处理排除科室
            exclude_dept = rule.get("排除科室", "")
            exclude_dept_array = self.build_ilike_any_array(exclude_dept)
            
            # 处理包含科室
            include_dept = rule.get("包含科室", "")
            include_dept_array = self.build_ilike_any_array(include_dept)
            
            # 处理包含诊断
            include_diag = rule.get("包含诊断", "")
            include_diag_array = self.build_ilike_any_array(include_diag)

            
            # 定义替换规则
            replacements = {
                "{医保项目名称}": medical_items_str,
                "{医保名称1}": self.split_and_join_values(rule.get('医保名称1', '')),
                "{医保名称2}": self.split_and_join_values(rule.get('医保名称2', '')),
                "{医保编码1}": self.split_and_join_values(rule.get('医保编码1', '')),
                "{医保编码2}": self.split_and_join_values(rule.get('医保编码2', '')),
                "{国家编码1}": self.split_and_join_values(rule.get('国家医保编码1', '')),
                "{国家编码2}": self.split_and_join_values(rule.get('国家医保编码2', '')),
                "{国家名称1}": self.split_and_join_values(rule.get('国家医保名称1', '')),
                "{国家名称2}": self.split_and_join_values(rule.get('国家医保名称2', '')),
                "{违规数量}": str(rule.get("违规数量") if rule.get("违规数量") is not None else "0"),
                "{违规天数}": str(rule.get("违规天数", "1")),
                "{违规小时数}": str(rule.get("违规小时数", "1")),
                "{违规金额}": str(rule.get("违规金额", "0")),
                "{年龄}": str(rule.get("年龄", "0")),
                "{性别}": str(rule.get("性别", "")),
                "{时间类型}": str(rule.get("时间类型",'')) 
            }
            
            # 只有当排除诊断不为空时才替换1=1
            if exclude_diag and exclude_diag.strip():
                if is_outpatient:
                    replacements["1=1"] = f"not (COALESCE(A.诊断名称,'') ILIKE ANY ({exclude_diag_array}))"
                else:
                    replacements["1=1"] = f"not (COALESCE(A.出院诊断名称,'') ILIKE ANY ({exclude_diag_array}) OR COALESCE(A.入院诊断名称,'') ILIKE ANY ({exclude_diag_array}))"
            
            # 只有当排除科室不为空时才替换{排除科室}
            if exclude_dept and exclude_dept.strip():
                replacements["2=2"] = f"not COALESCE(B.开单科室名称,'') ILIKE ANY ({exclude_dept_array}) "
            # 只有当包含科室不为空时才替换{包含科室}
            if include_dept and include_dept.strip():
                replacements["3=3"] = f"COALESCE(B.开单科室名称,'') ILIKE ANY ({include_dept_array}) "
            # 只有当包含诊断不为空时才替换{包含诊断}
            if include_diag and include_diag.strip():
                if is_outpatient:
                    replacements["4=4"] = f"COALESCE(A.诊断名称,'') ILIKE ANY ({include_diag_array})"
                else:
                    replacements["4=4"] = f"(COALESCE(A.出院诊断名称,'') ILIKE ANY ({include_diag_array}) OR COALESCE(A.入院诊断名称,'') ILIKE ANY ({include_diag_array}))"

            # 执行替换
            result = template
            for placeholder, value in replacements.items():
                result = result.replace(placeholder, value)
                
            return result
        except Exception as e:
            print(f"渲染SQL模板失败: {str(e)}")
            return template

    def parse_template_parameters(self, sql_content: str) -> List[Dict[str, Any]]:
        """解析SQL模板中的参数占位符"""
        parameters = []
        # 使用正则表达式匹配 {参数名} 格式的占位符
        pattern = r'\{([^}]+)\}'
        matches = re.findall(pattern, sql_content)
        
        for param_name in matches:
            # 跳过已经处理的特殊占位符
            if param_name in ['1=1', '2=2', '3=3', '4=4']:
                continue
                
            parameter = {
                'name': param_name,
                'description': self.get_parameter_description(param_name),
                'type': self.get_parameter_type(param_name),
                'required': True,
                'default_value': self.get_parameter_default(param_name)
            }
            parameters.append(parameter)
        
        return parameters

    def get_parameter_description(self, param_name: str) -> str:
        """获取参数描述"""
        descriptions = {
            '医保名称1': '医保项目名称1',
            '医保名称2': '医保项目名称2',
            '医保编码1': '医保项目编码1',
            '医保编码2': '医保项目编码2',
            '国家编码1': '国家医保编码1',
            '国家编码2': '国家医保编码2',
            '国家名称1': '国家医保名称1',
            '国家名称2': '国家医保名称2',
            '违规数量': '违规数量限制',
            '违规天数': '违规天数限制',
            '违规小时数': '违规小时数限制',
            '违规金额': '违规金额限制',
            '年龄': '年龄限制',
            '性别': '性别限制',
            '时间类型': '时间类型',
            '排除诊断': '排除的诊断',
            '排除科室': '排除的科室',
            '包含科室': '包含的科室',
            '包含诊断': '包含的诊断'
        }
        return descriptions.get(param_name, param_name)

    def get_parameter_type(self, param_name: str) -> str:
        """获取参数类型"""
        if '数量' in param_name or '天数' in param_name or '小时数' in param_name or '金额' in param_name or '年龄' in param_name:
            return 'number'
        elif '性别' in param_name:
            return 'select'
        else:
            return 'text'

    def get_parameter_default(self, param_name: str) -> str:
        """获取参数默认值"""
        defaults = {
            '违规数量': '1',
            '违规天数': '1',
            '违规小时数': '1',
            '违规金额': '0',
            '年龄': '0',
            '性别': '',
            '时间类型': ''
        }
        return defaults.get(param_name, '')

    def generate_sql_from_conditions(self, template_content: str, conditions: Dict[str, Any]) -> str:
        """根据条件生成SQL"""
        try:
            # 构建规则数据
            rule_data = {
                'visit_type': conditions.get('patient_type') == 'outpatient' and '门诊' or '住院',
                '就诊类型': conditions.get('patient_type') == 'outpatient' and '门诊' or '住院',
                '类型': conditions.get('rule_type', ''),
                '时间类型': conditions.get('time_type', ''),
                '行为认定': conditions.get('behavior_type', ''),
                '医保名称1': conditions.get('medical_name1', ''),
                '医保名称2': conditions.get('medical_name2', ''),
                '医保编码1': conditions.get('medical_code1', ''),
                '医保编码2': conditions.get('medical_code2', ''),
                '国家编码1': conditions.get('national_code1', ''),
                '国家编码2': conditions.get('national_code2', ''),
                '国家名称1': conditions.get('national_name1', ''),
                '国家名称2': conditions.get('national_name2', ''),
                '违规数量': conditions.get('violation_count', '1'),
                '违规天数': conditions.get('violation_days', '1'),
                '违规小时数': conditions.get('violation_hours', '1'),
                '违规金额': conditions.get('violation_amount', '0'),
                '年龄': conditions.get('age', '0'),
                '性别': conditions.get('gender', ''),
                '时间类型': conditions.get('time_type', ''),
                '排除诊断': conditions.get('exclude_diagnosis', ''),
                '排除科室': conditions.get('exclude_department', ''),
                '包含科室': conditions.get('include_department', ''),
                '包含诊断': conditions.get('include_diagnosis', '')
            }
            
            # 判断是否为门诊
            is_outpatient = conditions.get('patient_type') == 'outpatient'
            
            # 渲染SQL
            return self.render_template_sql(template_content, rule_data, is_outpatient)
            
        except Exception as e:
            print(f"根据条件生成SQL失败: {str(e)}")
            return template_content 