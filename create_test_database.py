#!/usr/bin/env python3
"""
创建测试数据库和表结构
"""

import psycopg
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_database():
    """创建测试数据库和表结构"""
    
    # 数据库连接配置
    host = "localhost"
    port = 5432
    database = "databasetools"
    username = "postgres"
    password = "P@ssw0rd"
    
    try:
        # 连接数据库
        conn_str = f"host={host} port={port} dbname={database} user={username} password={password}"
        with psycopg.connect(conn_str) as conn:
            with conn.cursor() as cursor:
                
                # 检查kj_rule表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_name = 'kj_rule'
                    )
                """)
                table_exists = cursor.fetchone()[0]
                
                if table_exists:
                    logger.info("kj_rule表已存在")
                else:
                    logger.info("创建kj_rule表...")
                    
                    # 创建kj_rule表
                    cursor.execute("""
                        CREATE TABLE public.kj_rule (
                            id SERIAL PRIMARY KEY,
                            rule_name VARCHAR(255) NOT NULL,
                            rule_intension TEXT,
                            policy_basis TEXT,
                            sql_name VARCHAR(255),
                            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """)
                    
                    logger.info("kj_rule表创建成功")
                
                # 插入测试数据
                logger.info("插入测试数据...")
                
                # 清空现有数据
                cursor.execute("DELETE FROM public.kj_rule")
                
                # 插入测试规则
                test_rules = [
                    {
                        'rule_name': '超频次测试规则',
                        'rule_intension': '检查患者每日用药频次是否超过限制',
                        'policy_basis': '医保政策规定每日用药频次限制',
                        'sql_name': None  # 可导入的规则
                    },
                    {
                        'rule_name': '重复收费检测规则',
                        'rule_intension': '检测是否存在重复收费情况',
                        'policy_basis': '医保政策禁止重复收费',
                        'sql_name': None  # 可导入的规则
                    },
                    {
                        'rule_name': '限性别用药规则',
                        'rule_intension': '检查性别相关用药是否符合规定',
                        'policy_basis': '医保政策对性别相关用药有特殊规定',
                        'sql_name': None  # 可导入的规则
                    },
                    {
                        'rule_name': '已处理规则',
                        'rule_intension': '这是一个已经处理过的规则',
                        'policy_basis': '测试用',
                        'sql_name': 'processed_rule.sql'  # 已处理的规则，不可导入
                    }
                ]
                
                for rule in test_rules:
                    cursor.execute("""
                        INSERT INTO public.kj_rule (rule_name, rule_intension, policy_basis, sql_name)
                        VALUES (%s, %s, %s, %s)
                    """, (rule['rule_name'], rule['rule_intension'], rule['policy_basis'], rule['sql_name']))
                
                # 提交事务
                conn.commit()
                
                logger.info(f"成功插入 {len(test_rules)} 条测试数据")
                
                # 验证数据
                cursor.execute("""
                    SELECT id, rule_name, rule_intension, policy_basis, sql_name, create_time
                    FROM public.kj_rule 
                    ORDER BY id
                """)
                
                rules = cursor.fetchall()
                logger.info(f"验证数据：找到 {len(rules)} 条规则")
                
                for rule in rules:
                    logger.info(f"  - ID: {rule[0]}, 名称: {rule[1]}, SQL名称: {rule[4]}")
                
                # 查询可导入的规则
                cursor.execute("""
                    SELECT id, rule_name, rule_intension, policy_basis, create_time
                    FROM public.kj_rule 
                    WHERE sql_name IS NULL OR sql_name = '' OR sql_name = 'NULL'
                    ORDER BY create_time DESC
                """)
                
                importable_rules = cursor.fetchall()
                logger.info(f"可导入的规则：{len(importable_rules)} 条")
                
                for rule in importable_rules:
                    logger.info(f"  - ID: {rule[0]}, 名称: {rule[1]}")
                
                logger.info("测试数据库设置完成！")
                
    except Exception as e:
        logger.error(f"创建测试数据库失败: {str(e)}")
        raise

if __name__ == "__main__":
    create_test_database() 